{"name": "@rtc-plugin/basic-beauty", "version": "5.10.1", "description": "TRTC Web SDK 5.x basic-beauty plugin", "main": "./basic-beauty.esm.js", "module": "./basic-beauty.esm.js", "repository": {"type": "git", "url": "**************:LiteAVSDK/TRTC_Web.git"}, "homepage": "https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-36-advanced-basic-beauty.html", "keywords": ["webrtc", "TRTC", "rtc", "call", "video call", "audio call", "javascript", "video", "audio", "camera", "microphone", "live streaming", "real-time communication", "blur background"], "types": "./basic-beauty.esm.d.ts"}