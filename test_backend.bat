@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 腾讯云直播平台后端测试脚本

echo [INFO] 开始测试后端服务...

:: 进入后端目录
cd backend

:: 检查配置文件
if not exist "config\.env" (
    echo [ERROR] 配置文件 config\.env 不存在
    echo [INFO] 请先复制 config\.env.example 到 config\.env 并配置相关参数
    pause
    exit /b 1
)

:: 检查Go环境
go version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Go 未安装或未添加到 PATH
    pause
    exit /b 1
)

echo [INFO] Go 环境检查通过

:: 下载依赖
echo [INFO] 下载 Go 模块依赖...
go mod tidy
if errorlevel 1 (
    echo [ERROR] 下载依赖失败
    pause
    exit /b 1
)

echo [INFO] 依赖下载完成

:: 编译项目
echo [INFO] 编译后端项目...
go build -o bin/server.exe cmd/server/main.go
if errorlevel 1 (
    echo [ERROR] 编译失败
    pause
    exit /b 1
)

echo [INFO] 编译成功

:: 创建必要的目录
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads

echo [INFO] 启动后端服务...
echo [INFO] 服务将在 http://localhost:8080 启动
echo [INFO] 按 Ctrl+C 停止服务
echo.

:: 启动服务
bin\server.exe

pause
