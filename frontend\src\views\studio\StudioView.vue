<template>
  <div class="studio-container">
    <!-- 头部导航 -->
    <header class="studio-header">
      <div class="header-content">
        <h1>直播控制台</h1>
        <div class="header-actions">
          <el-button @click="$router.push('/')">返回首页</el-button>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="studio-main">
      <!-- 直播间信息卡片 -->
      <el-card class="room-info-card" v-if="currentRoom">
        <template #header>
          <div class="card-header">
            <span>直播间信息</span>
            <el-tag :type="roomStatusType" size="large">
              {{ roomStatusText }}
            </el-tag>
          </div>
        </template>

        <div class="room-info">
          <div class="room-basic">
            <h3>{{ currentRoom.title }}</h3>
            <p>{{ currentRoom.description }}</p>
            <div class="room-stats">
              <div class="stat-item">
                <span class="label">观看人数:</span>
                <span class="value">{{ currentRoom.viewer_count }}</span>
              </div>
              <div class="stat-item">
                <span class="label">点赞数:</span>
                <span class="value">{{ currentRoom.like_count }}</span>
              </div>
              <div class="stat-item">
                <span class="label">消息数:</span>
                <span class="value">{{ currentRoom.message_count }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 创建直播间 -->
      <el-card class="create-room-card" v-if="!currentRoom">
        <template #header>
          <span>创建直播间</span>
        </template>

        <el-form
          ref="roomFormRef"
          :model="roomForm"
          :rules="roomRules"
          label-width="100px"
          class="room-form"
        >
          <el-form-item label="直播标题" prop="title">
            <el-input
              v-model="roomForm.title"
              placeholder="请输入直播标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="直播描述" prop="description">
            <el-input
              v-model="roomForm.description"
              type="textarea"
              placeholder="请输入直播描述"
              :rows="3"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="直播分类" prop="category_id">
            <el-select v-model="roomForm.category_id" placeholder="请选择分类">
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="标签" prop="tags">
            <el-input
              v-model="roomForm.tags"
              placeholder="请输入标签，用逗号分隔"
              maxlength="200"
            />
          </el-form-item>

          <el-form-item label="画质等级" prop="quality_level">
            <el-select v-model="roomForm.quality_level" placeholder="请选择画质">
              <el-option label="流畅 (480p)" :value="1" />
              <el-option label="标清 (720p)" :value="2" />
              <el-option label="高清 (1080p)" :value="3" />
              <el-option label="超清 (4K)" :value="4" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="roomForm.allow_chat">允许聊天</el-checkbox>
            <el-checkbox v-model="roomForm.allow_gift">允许礼物</el-checkbox>
            <el-checkbox v-model="roomForm.is_private">私密直播间</el-checkbox>
          </el-form-item>

          <el-form-item v-if="roomForm.is_private" label="直播密码" prop="password">
            <el-input
              v-model="roomForm.password"
              type="password"
              placeholder="请输入直播间密码"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              :loading="creating"
              @click="createRoom"
            >
              创建直播间
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 直播控制面板 -->
      <div class="control-panels" v-if="currentRoom">
        <!-- 推流方式选择 -->
        <el-card class="stream-method-card">
          <template #header>
            <span>推流方式</span>
          </template>

          <el-radio-group v-model="streamMethod" @change="onStreamMethodChange">
            <el-radio value="webrtc">
              <div class="method-option">
                <div class="method-title">Web 推流 (推荐)</div>
                <div class="method-desc">浏览器内直接推流，无需安装软件</div>
              </div>
            </el-radio>
            <el-radio value="rtmp">
              <div class="method-option">
                <div class="method-title">RTMP 推流</div>
                <div class="method-desc">使用 OBS 等推流软件</div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-card>

        <!-- WebRTC 推流 -->
        <div v-if="streamMethod === 'webrtc'">
          <WebRTCPusher
            v-if="currentRoom && webrtcConfig"
            :config="webrtcConfig"
          />
        </div>

        <!-- RTMP 推流配置 -->
        <el-card v-if="streamMethod === 'rtmp'" class="stream-config-card">
          <template #header>
            <span>RTMP 推流配置</span>
          </template>

          <div class="stream-config">
            <div class="config-item">
              <label>推流地址:</label>
              <div class="url-input">
                <el-input
                  :value="currentRoom.push_url"
                  readonly
                  class="url-field"
                />
                <el-button @click="copyToClipboard(currentRoom.push_url)">
                  复制
                </el-button>
              </div>
            </div>

            <div class="config-item">
              <label>流密钥:</label>
              <div class="url-input">
                <el-input
                  :value="currentRoom.stream_key"
                  readonly
                  class="url-field"
                />
                <el-button @click="copyToClipboard(currentRoom.stream_key)">
                  复制
                </el-button>
              </div>
            </div>

            <div class="config-item">
              <label>播放地址:</label>
              <div class="play-urls">
                <div class="url-item">
                  <span class="url-type">RTMP:</span>
                  <el-input :value="currentRoom.play_url" readonly />
                  <el-button @click="copyToClipboard(currentRoom.play_url)">复制</el-button>
                </div>
                <div class="url-item">
                  <span class="url-type">HLS:</span>
                  <el-input :value="currentRoom.hls_play_url" readonly />
                  <el-button @click="copyToClipboard(currentRoom.hls_play_url)">复制</el-button>
                </div>
                <div class="url-item">
                  <span class="url-type">FLV:</span>
                  <el-input :value="currentRoom.flv_play_url" readonly />
                  <el-button @click="copyToClipboard(currentRoom.flv_play_url)">复制</el-button>
                </div>
                <div class="url-item">
                  <span class="url-type">WebRTC:</span>
                  <el-input :value="currentRoom.webrtc_play_url" readonly />
                  <el-button @click="copyToClipboard(currentRoom.webrtc_play_url)">复制</el-button>
                </div>
              </div>
            </div>

            <el-alert
              title="使用说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <ol style="margin: 0; padding-left: 20px;">
                  <li>下载并安装 OBS Studio 或其他推流软件</li>
                  <li>在推流软件中添加"流"源</li>
                  <li>将上述推流地址和流密钥填入软件配置</li>
                  <li>点击"开始推流"按钮</li>
                </ol>
              </template>
            </el-alert>
          </div>
        </el-card>

        <!-- 直播控制 -->
        <el-card class="live-control-card">
          <template #header>
            <span>直播控制</span>
          </template>

          <div class="live-controls">
            <!-- 调试信息 -->
            <div class="debug-info" style="margin-bottom: 16px; padding: 8px; background: #f0f0f0; border-radius: 4px; font-size: 12px;">
              <div>当前状态: {{ currentRoom?.status }} (类型: {{ typeof currentRoom?.status }})</div>
              <div>直播间ID: {{ currentRoom?.id }}</div>
              <div>标题: {{ currentRoom?.title }}</div>
            </div>

            <div class="control-buttons">
              <!-- 未开播或已结束状态都可以开始直播 -->
              <el-button
                v-if="currentRoom && (currentRoom.status === 0 || currentRoom.status === 3)"
                type="success"
                size="large"
                :loading="starting"
                @click="startLive"
              >
                <el-icon><VideoPlay /></el-icon>
                {{ currentRoom.status === 3 ? '重新开始直播' : '开始直播' }}
              </el-button>

              <el-button
                v-if="currentRoom && currentRoom.status === 1"
                type="danger"
                size="large"
                :loading="stopping"
                @click="stopLive"
              >
                <el-icon><VideoPause /></el-icon>
                结束直播
              </el-button>

              <!-- 暂停状态 -->
              <div v-if="currentRoom && currentRoom.status === 2"
                   style="color: #e6a23c; font-size: 14px;">
                直播已暂停
              </div>
            </div>

            <div class="live-status" v-if="currentRoom.status === 1">
              <div class="status-item">
                <span class="label">直播时长:</span>
                <span class="value">{{ formatDuration(liveDuration) }}</span>
              </div>
              <div class="status-item">
                <span class="label">当前观众:</span>
                <span class="value">{{ currentRoom.viewer_count }}</span>
              </div>
              <div class="status-item">
                <span class="label">最高观众:</span>
                <span class="value">{{ currentRoom.max_viewer_count }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 直播预览 -->
      <el-card class="preview-card" v-if="currentRoom && currentRoom.status === 1">
        <template #header>
          <span>直播预览</span>
        </template>

        <div class="preview-container">
          <video
            ref="previewVideo"
            class="preview-video"
            controls
            muted
            autoplay
          >
            您的浏览器不支持视频播放
          </video>
        </div>
      </el-card>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElForm, ElMessage, ElMessageBox } from 'element-plus'
import { VideoPlay, VideoPause } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { copyToClipboard, formatDuration } from '@/utils'
import { roomApi, categoryApi } from '@/api/live'
import type { Room, CreateRoomRequest, Category } from '@/types/live'
import WebRTCPusher from '@/components/WebRTCPusher.vue'
import type { WebRTCConfig } from '@/services/webrtc-pusher'
import { WEBRTC_CONFIG, generateUserSig } from '@/config/webrtc'

const router = useRouter()
const userStore = useUserStore()

// 表单引用
const roomFormRef = ref<InstanceType<typeof ElForm>>()
const previewVideo = ref<HTMLVideoElement>()

// 状态管理
const currentRoom = ref<Room | null>(null)
const categories = ref<Category[]>([])
const creating = ref(false)
const starting = ref(false)
const stopping = ref(false)
const liveDuration = ref(0)
const showCreateForm = ref(false)
const streamMethod = ref<'webrtc' | 'rtmp'>('webrtc')
let durationTimer: NodeJS.Timeout | null = null

// WebRTC 配置
const webrtcConfig = computed<WebRTCConfig | null>(() => {
  if (!currentRoom.value || !userStore.user) return null

  return {
    sdkAppId: WEBRTC_CONFIG.SDK_APP_ID,
    userId: `user_${userStore.user.id}`,
    userSig: '', // 将在 WebRTCPusher 组件中异步获取
    roomId: `room_${currentRoom.value.id}`,
    streamId: currentRoom.value.stream_key,
    pushUrl: currentRoom.value.webrtc_push_url || ''
  }
})

// 表单数据
const roomForm = reactive<CreateRoomRequest>({
  title: '',
  description: '',
  category_id: 1,
  tags: '',
  is_private: false,
  password: '',
  allow_chat: true,
  allow_gift: true,
  chat_mode: 0,
  quality_level: 2
})

// 表单验证规则
const roomRules = {
  title: [
    { required: true, message: '请输入直播标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择直播分类', trigger: 'change' }
  ],
  password: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (roomForm.is_private && !value) {
          callback(new Error('私密直播间必须设置密码'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const roomStatusType = computed(() => {
  if (!currentRoom.value) return 'info'
  switch (currentRoom.value.status) {
    case 0: return 'info'    // 未开播
    case 1: return 'success' // 直播中
    case 2: return 'warning' // 暂停
    case 3: return 'danger'  // 已结束
    default: return 'info'
  }
})

const roomStatusText = computed(() => {
  if (!currentRoom.value) return '未知'
  switch (currentRoom.value.status) {
    case 0: return '未开播'
    case 1: return '直播中'
    case 2: return '暂停中'
    case 3: return '已结束'
    default: return '未知'
  }
})

// 方法
const loadCategories = async () => {
  try {
    const response = await categoryApi.getCategories()
    categories.value = response.data
  } catch (error) {
    console.error('加载分类失败:', error)
    ElMessage.error('加载分类失败')
    // 使用默认分类作为后备
    categories.value = [
      { id: 1, name: '游戏', description: '游戏直播', icon: '', sort: 1, is_active: true, room_count: 0, created_at: '', updated_at: '' },
      { id: 2, name: '娱乐', description: '娱乐直播', icon: '', sort: 2, is_active: true, room_count: 0, created_at: '', updated_at: '' },
      { id: 3, name: '教育', description: '教育直播', icon: '', sort: 3, is_active: true, room_count: 0, created_at: '', updated_at: '' },
      { id: 4, name: '科技', description: '科技直播', icon: '', sort: 4, is_active: true, room_count: 0, created_at: '', updated_at: '' }
    ]
  }
}

const loadCurrentRoom = async () => {
  try {
    const response = await roomApi.getMyRoom()
    currentRoom.value = response.data
    console.log('✅ 成功加载直播间:', response.data)
  } catch (error: any) {
    // 如果用户没有直播间，这是正常的，不显示错误
    if (error?.response?.status === 404) {
      console.log('ℹ️ 用户还没有创建直播间')
      currentRoom.value = null
    } else {
      console.error('❌ 加载直播间失败:', error)
      ElMessage.error('加载直播间失败')
      currentRoom.value = null
    }
  }
}

const createRoom = async () => {
  if (!roomFormRef.value) return

  try {
    await roomFormRef.value.validate()
    creating.value = true

    const response = await roomApi.createRoom(roomForm)
    currentRoom.value = response.data
    showCreateForm.value = false

    ElMessage.success('直播间创建成功')

  } catch (error: any) {
    console.error('创建直播间失败:', error)
    ElMessage.error(error?.response?.data?.message || '创建直播间失败')
  } finally {
    creating.value = false
  }
}

const startLive = async () => {
  if (!currentRoom.value) return

  try {
    await ElMessageBox.confirm(
      '确定要开始直播吗？',
      '开始直播',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    starting.value = true

    await roomApi.startLive(currentRoom.value.id)

    // 重新加载直播间信息获取最新状态
    await loadCurrentRoom()

    // 开始计时
    startDurationTimer()

    // 初始化预览
    initPreview()

    ElMessage.success('直播已开始')

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('开始直播失败:', error)
      ElMessage.error(error?.response?.data?.message || '开始直播失败')
    }
  } finally {
    starting.value = false
  }
}

const stopLive = async () => {
  if (!currentRoom.value) return

  try {
    await ElMessageBox.confirm(
      '确定要结束直播吗？结束后无法恢复。',
      '结束直播',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    stopping.value = true

    await roomApi.stopLive(currentRoom.value.id)

    // 重新加载直播间信息获取最新状态
    await loadCurrentRoom()

    // 停止计时
    stopDurationTimer()

    ElMessage.success('直播已结束')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('结束直播失败:', error)
      ElMessage.error('结束直播失败')
    }
  } finally {
    stopping.value = false
  }
}

const startDurationTimer = () => {
  liveDuration.value = 0
  durationTimer = setInterval(() => {
    liveDuration.value++
  }, 1000)
}

const stopDurationTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
}

const initPreview = () => {
  if (!previewVideo.value || !currentRoom.value) return

  try {
    // 设置预览视频源
    previewVideo.value.src = currentRoom.value.hls_play_url
    previewVideo.value.load()
  } catch (error) {
    console.error('初始化预览失败:', error)
  }
}

const onStreamMethodChange = (method: 'webrtc' | 'rtmp') => {
  console.log('推流方式切换为:', method)
  // 可以在这里添加切换逻辑，比如保存用户偏好等
}

// 生命周期
onMounted(async () => {
  await loadCategories()
  await loadCurrentRoom()
})

onUnmounted(() => {
  stopDurationTimer()
})
</script>

<style scoped>
.studio-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 头部样式 */
.studio-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-content h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

/* 主要内容样式 */
.studio-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 卡片样式 */
.room-info-card,
.create-room-card,
.stream-method-card,
.stream-config-card,
.live-control-card,
.preview-card {
  margin-bottom: 20px;
}

/* 推流方式选择样式 */
.method-option {
  margin-left: 8px;
}

.method-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.method-desc {
  font-size: 12px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 直播间信息样式 */
.room-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.room-info p {
  margin: 0 0 16px 0;
  color: #606266;
}

.room-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-item .label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-item .value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 表单样式 */
.room-form {
  max-width: 600px;
}

/* 控制面板样式 */
.control-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

/* 推流配置样式 */
.stream-config .config-item {
  margin-bottom: 16px;
}

.stream-config .config-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #303133;
}

.url-input {
  display: flex;
  gap: 8px;
}

.url-field {
  flex: 1;
}

.play-urls .url-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.url-type {
  min-width: 50px;
  font-weight: 500;
  color: #606266;
}

/* 直播控制样式 */
.live-controls {
  text-align: center;
}

.control-buttons {
  margin-bottom: 24px;
}

.control-buttons .el-button {
  min-width: 120px;
}

.live-status {
  display: flex;
  justify-content: center;
  gap: 32px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.live-status .status-item {
  text-align: center;
}

.live-status .label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.live-status .value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 预览样式 */
.preview-container {
  text-align: center;
}

.preview-video {
  width: 100%;
  max-width: 640px;
  height: auto;
  border-radius: 6px;
  background-color: #000;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panels {
    grid-template-columns: 1fr;
  }

  .room-stats {
    flex-direction: column;
    gap: 12px;
  }

  .live-status {
    flex-direction: column;
    gap: 16px;
  }

  .url-input {
    flex-direction: column;
  }

  .play-urls .url-item {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
