<template>
  <div class="home-container">
    <!-- 头部导航 -->
    <header class="home-header">
      <div class="header-content">
        <div class="logo">
          <h1>{{ appTitle }}</h1>
        </div>
        <nav class="nav-menu">
          <el-menu
            mode="horizontal"
            :default-active="activeMenu"
            class="nav-menu-items"
            @select="handleMenuSelect"
          >
            <el-menu-item index="home">首页</el-menu-item>
            <el-menu-item index="live">直播</el-menu-item>
            <el-menu-item index="categories">分类</el-menu-item>
          </el-menu>
        </nav>
        <div class="header-actions">
          <template v-if="userStore.isLoggedIn">
            <el-button
              v-if="userStore.isStreamer"
              type="primary"
              @click="handleStartLive"
            >
              开始直播
            </el-button>
            <el-dropdown @command="handleUserCommand">
              <div class="user-avatar">
                <el-avatar :src="userStore.user?.avatar" :size="32">
                  {{ userStore.user?.nickname?.charAt(0) }}
                </el-avatar>
                <span class="username">{{ userStore.user?.nickname }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                  <el-dropdown-item command="settings">设置</el-dropdown-item>
                  <el-dropdown-item v-if="userStore.isAdmin" command="admin">管理后台</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button @click="$router.push('/login')">登录</el-button>
            <el-button type="primary" @click="$router.push('/register')">注册</el-button>
          </template>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="home-main">
      <!-- 轮播图 -->
      <section class="hero-section">
        <el-carousel height="400px" indicator-position="inside">
          <el-carousel-item v-for="item in banners" :key="item.id">
            <div class="carousel-item" :style="{ backgroundImage: `url(${item.image})` }">
              <div class="carousel-content">
                <h2>{{ item.title }}</h2>
                <p>{{ item.description }}</p>
                <el-button type="primary" size="large">立即体验</el-button>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </section>

      <!-- 热门直播 -->
      <section class="live-section">
        <div class="section-header">
          <h2>热门直播</h2>
          <el-button text type="primary" @click="$router.push('/live')">
            查看更多 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="live-grid">
          <div
            v-for="room in hotRooms"
            :key="room.id"
            class="live-card"
            @click="$router.push(`/live/${room.id}`)"
          >
            <div class="live-cover">
              <img :src="room.cover" :alt="room.title" />
              <div class="live-status">
                <el-tag type="danger" size="small">直播中</el-tag>
                <span class="viewer-count">{{ formatNumber(room.viewer_count) }}人观看</span>
              </div>
            </div>
            <div class="live-info">
              <h3>{{ room.title }}</h3>
              <div class="streamer-info">
                <el-avatar :src="room.user?.avatar" :size="24">
                  {{ room.user?.nickname?.charAt(0) }}
                </el-avatar>
                <span>{{ room.user?.nickname }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 分类导航 -->
      <section class="categories-section">
        <div class="section-header">
          <h2>热门分类</h2>
        </div>
        <div class="categories-grid">
          <div
            v-for="category in categories"
            :key="category.id"
            class="category-card"
            @click="handleCategoryClick(category)"
          >
            <div class="category-icon">
              <el-icon :size="32">
                <component :is="category.icon" />
              </el-icon>
            </div>
            <h3>{{ category.name }}</h3>
            <p>{{ category.room_count }}个直播间</p>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="home-footer">
      <div class="footer-content">
        <div class="footer-info">
          <h3>{{ appTitle }}</h3>
          <p>基于腾讯云的专业直播平台</p>
        </div>
        <div class="footer-links">
          <div class="link-group">
            <h4>产品</h4>
            <a href="#">直播功能</a>
            <a href="#">技术支持</a>
            <a href="#">API文档</a>
          </div>
          <div class="link-group">
            <h4>帮助</h4>
            <a href="#">使用指南</a>
            <a href="#">常见问题</a>
            <a href="#">联系我们</a>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2023 {{ appTitle }}. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowDown, ArrowRight, VideoPlay, Microphone, Reading, Monitor } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { formatNumber } from '@/utils'

const router = useRouter()
const userStore = useUserStore()

// 应用标题
const appTitle = computed(() => import.meta.env.VITE_APP_TITLE)

// 当前激活的菜单
const activeMenu = ref('home')

// 轮播图数据
const banners = ref([
  {
    id: 1,
    title: '专业直播平台',
    description: '基于腾讯云技术，提供高清稳定的直播体验',
    image: '/images/banner1.jpg'
  },
  {
    id: 2,
    title: '实时互动',
    description: '支持弹幕、礼物、连麦等多种互动方式',
    image: '/images/banner2.jpg'
  },
  {
    id: 3,
    title: '多端观看',
    description: '支持PC、手机、平板等多种设备观看',
    image: '/images/banner3.jpg'
  }
])

// 热门直播间
const hotRooms = ref<any[]>([])

// 分类数据
const categories = ref<any[]>([])

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  activeMenu.value = index
  switch (index) {
    case 'home':
      router.push('/')
      break
    case 'live':
      router.push('/live')
      break
    case 'categories':
      // 滚动到分类区域
      document.querySelector('.categories-section')?.scrollIntoView({ behavior: 'smooth' })
      break
  }
}

// 处理用户下拉菜单
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'admin':
      router.push('/admin')
      break
    case 'logout':
      userStore.logout()
      break
  }
}

// 处理分类点击
const handleCategoryClick = (category: any) => {
  router.push({
    name: 'live',
    query: { category: category.id }
  })
}

// 处理开始直播
const handleStartLive = () => {
  router.push('/studio')
}

// 加载数据
const loadData = async () => {
  try {
    // 这里应该调用API获取数据
    // 暂时使用模拟数据
    hotRooms.value = [
      {
        id: 1,
        title: '王者荣耀巅峰赛',
        cover: '/images/room1.jpg',
        viewer_count: 12580,
        user: {
          id: 1,
          nickname: '游戏主播',
          avatar: '/images/avatar1.jpg'
        }
      },
      {
        id: 2,
        title: '英雄联盟排位赛',
        cover: '/images/room2.jpg',
        viewer_count: 8960,
        user: {
          id: 2,
          nickname: '电竞选手',
          avatar: '/images/avatar2.jpg'
        }
      }
    ]

    categories.value = [
      {
        id: 1,
        name: '游戏',
        icon: 'VideoPlay',
        room_count: 1250
      },
      {
        id: 2,
        name: '娱乐',
        icon: 'Microphone',
        room_count: 890
      },
      {
        id: 3,
        name: '教育',
        icon: 'Reading',
        room_count: 456
      },
      {
        id: 4,
        name: '科技',
        icon: 'Monitor',
        room_count: 234
      }
    ]
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.home-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  height: 64px;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.nav-menu {
  flex: 1;
  margin-left: 40px;
}

.nav-menu-items {
  border: none;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-avatar:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #303133;
}

/* 主要内容样式 */
.home-main {
  flex: 1;
}

/* 轮播图样式 */
.hero-section {
  margin-bottom: 40px;
}

.carousel-item {
  height: 400px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.carousel-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.carousel-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 1;
}

.carousel-content h2 {
  font-size: 36px;
  margin: 0 0 16px 0;
}

.carousel-content p {
  font-size: 18px;
  margin: 0 0 24px 0;
}

/* 内容区域样式 */
.live-section,
.categories-section {
  max-width: 1200px;
  margin: 0 auto 40px auto;
  padding: 0 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

/* 直播网格样式 */
.live-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.live-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.live-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.live-cover {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.live-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.live-status {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.viewer-count {
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.live-info {
  padding: 16px;
}

.live-info h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

/* 分类网格样式 */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.category-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.category-icon {
  margin-bottom: 16px;
  color: #409eff;
}

.category-card h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.category-card p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

/* 页脚样式 */
.home-footer {
  background: #f8f9fa;
  margin-top: 60px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
}

.footer-info h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.footer-info p {
  margin: 0;
  color: #606266;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
}

.link-group h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.link-group a {
  display: block;
  margin-bottom: 8px;
  color: #606266;
  text-decoration: none;
  transition: color 0.2s;
}

.link-group a:hover {
  color: #409eff;
}

.footer-bottom {
  border-top: 1px solid #e4e7ed;
  padding: 20px;
  text-align: center;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .nav-menu {
    display: none;
  }
  
  .carousel-content h2 {
    font-size: 24px;
  }
  
  .carousel-content p {
    font-size: 16px;
  }
  
  .live-grid,
  .categories-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}
</style>
