// 用户相关API

import { http } from './request'
import type {
  LoginRequest,
  RegisterRequest,
  UpdateProfileRequest,
  ChangePasswordRequest,
  AuthResponse,
  UserProfile,
  FollowListResponse,
  UserListResponse
} from '@/types/user'
import type { PaginationParams } from '@/types/common'

// 用户认证API
export const authApi = {
  // 用户注册
  register(data: RegisterRequest) {
    return http.post<AuthResponse>('/auth/register', data)
  },

  // 用户登录
  login(data: LoginRequest) {
    return http.post<AuthResponse>('/auth/login', data)
  },

  // 刷新token
  refreshToken() {
    return http.post<AuthResponse>('/auth/refresh')
  }
}

// 用户管理API
export const userApi = {
  // 获取当前用户资料
  getProfile() {
    return http.get<UserProfile>('/users/profile')
  },

  // 获取指定用户资料
  getUserProfile(userId: number) {
    return http.get<UserProfile>(`/users/${userId}/profile`)
  },

  // 更新用户资料
  updateProfile(data: UpdateProfileRequest) {
    return http.put('/users/profile', data)
  },

  // 修改密码
  changePassword(data: ChangePasswordRequest) {
    return http.put('/users/password', data)
  },

  // 搜索用户
  searchUsers(keyword: string, params?: PaginationParams) {
    return http.get<UserListResponse>('/users/search', {
      params: {
        keyword,
        ...params
      }
    })
  }
}

// 用户关系API
export const followApi = {
  // 关注用户
  follow(userId: number) {
    return http.post(`/users/${userId}/follow`)
  },

  // 取消关注
  unfollow(userId: number) {
    return http.post(`/users/${userId}/unfollow`)
  },

  // 获取当前用户的粉丝列表
  getFollowers(params?: PaginationParams) {
    return http.get<FollowListResponse>('/users/followers', { params })
  },

  // 获取指定用户的粉丝列表
  getUserFollowers(userId: number, params?: PaginationParams) {
    return http.get<FollowListResponse>(`/users/${userId}/followers`, { params })
  },

  // 获取当前用户的关注列表
  getFollowing(params?: PaginationParams) {
    return http.get<FollowListResponse>('/users/following', { params })
  },

  // 获取指定用户的关注列表
  getUserFollowing(userId: number, params?: PaginationParams) {
    return http.get<FollowListResponse>(`/users/${userId}/following`, { params })
  },

  // 检查是否关注某用户
  checkFollowing(userId: number) {
    return http.get<{ is_following: boolean }>(`/users/${userId}/is-following`)
  }
}
