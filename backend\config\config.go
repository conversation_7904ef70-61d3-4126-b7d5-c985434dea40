package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// Config 应用配置结构
type Config struct {
	Server    ServerConfig    `json:"server"`
	Database  DatabaseConfig  `json:"database"`
	Redis     RedisConfig     `json:"redis"`
	JWT       JWTConfig       `json:"jwt"`
	Tencent   TencentConfig   `json:"tencent"`
	Log       LogConfig       `json:"log"`
	CORS      CORSConfig      `json:"cors"`
	WebSocket WebSocketConfig `json:"websocket"`
	Upload    UploadConfig    `json:"upload"`
	Security  SecurityConfig  `json:"security"`
	Metrics   MetricsConfig   `json:"metrics"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `json:"port"`
	Host string `json:"host"`
	Mode string `json:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host        string `json:"host"`
	Port        string `json:"port"`
	User        string `json:"user"`
	Password    string `json:"password"`
	Name        string `json:"name"`
	Charset     string `json:"charset"`
	AutoMigrate bool   `json:"auto_migrate"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	DB       int    `json:"db"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret      string `json:"secret"`
	ExpireHours int    `json:"expire_hours"`
}

// TencentConfig 腾讯云配置
type TencentConfig struct {
	SecretID   string `json:"secret_id"`
	SecretKey  string `json:"secret_key"`
	Region     string `json:"region"`
	PushDomain string `json:"push_domain"`
	PlayDomain string `json:"play_domain"`
	PushKey    string `json:"push_key"`
	PlayKey    string `json:"play_key"`
	ExpireTime int64  `json:"expire_time"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level string `json:"level"`
	File  string `json:"file"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowOrigins     []string `json:"allow_origins"`
	AllowCredentials bool     `json:"allow_credentials"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	ReadBufferSize  int  `json:"read_buffer_size"`
	WriteBufferSize int  `json:"write_buffer_size"`
	CheckOrigin     bool `json:"check_origin"`
}

// UploadConfig 文件上传配置
type UploadConfig struct {
	MaxSize      int64    `json:"max_size"`
	Path         string   `json:"path"`
	AllowedTypes []string `json:"allowed_types"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	RateLimitRequests int `json:"rate_limit_requests"`
	RateLimitDuration int `json:"rate_limit_duration"`
	BcryptCost        int `json:"bcrypt_cost"`
}

// MetricsConfig 监控配置
type MetricsConfig struct {
	Enable bool   `json:"enable"`
	Port   string `json:"port"`
}

var AppConfig *Config

// Load 加载配置
func Load() error {
	// 加载.env文件
	if err := godotenv.Load("config/.env"); err != nil {
		// 如果.env文件不存在，尝试从环境变量读取
		fmt.Println("Warning: .env file not found, using environment variables")
	}

	AppConfig = &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			Host:        getEnv("DB_HOST", "localhost"),
			Port:        getEnv("DB_PORT", "3306"),
			User:        getEnv("DB_USER", "root"),
			Password:    getEnv("DB_PASSWORD", ""),
			Name:        getEnv("DB_NAME", "live_streaming"),
			Charset:     getEnv("DB_CHARSET", "utf8mb4"),
			AutoMigrate: getEnvAsBool("AUTO_MIGRATE", false),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Username: getEnv("REDIS_USERNAME", ""),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvAsInt("REDIS_DB", 0),
		},
		JWT: JWTConfig{
			Secret:      getEnv("JWT_SECRET", "default-secret-change-this"),
			ExpireHours: getEnvAsInt("JWT_EXPIRE_HOURS", 24),
		},
		Tencent: TencentConfig{
			SecretID:   getEnv("TENCENT_SECRET_ID", ""),
			SecretKey:  getEnv("TENCENT_SECRET_KEY", ""),
			Region:     getEnv("TENCENT_REGION", "ap-beijing"),
			PushDomain: getEnv("LIVE_PUSH_DOMAIN", ""),
			PlayDomain: getEnv("LIVE_PLAY_DOMAIN", ""),
			PushKey:    getEnv("LIVE_PUSH_KEY", ""),
			PlayKey:    getEnv("LIVE_PLAY_KEY", ""),
			ExpireTime: int64(getEnvAsInt("LIVE_EXPIRE_TIME", 3600)),
		},
		Log: LogConfig{
			Level: getEnv("LOG_LEVEL", "info"),
			File:  getEnv("LOG_FILE", "logs/app.log"),
		},
		CORS: CORSConfig{
			AllowOrigins:     strings.Split(getEnv("CORS_ALLOW_ORIGINS", "http://localhost:3000"), ","),
			AllowCredentials: getEnvAsBool("CORS_ALLOW_CREDENTIALS", true),
		},
		WebSocket: WebSocketConfig{
			ReadBufferSize:  getEnvAsInt("WS_READ_BUFFER_SIZE", 1024),
			WriteBufferSize: getEnvAsInt("WS_WRITE_BUFFER_SIZE", 1024),
			CheckOrigin:     getEnvAsBool("WS_CHECK_ORIGIN", false),
		},
		Upload: UploadConfig{
			MaxSize:      int64(getEnvAsInt("UPLOAD_MAX_SIZE", 10485760)),
			Path:         getEnv("UPLOAD_PATH", "uploads/"),
			AllowedTypes: strings.Split(getEnv("UPLOAD_ALLOWED_TYPES", "jpg,jpeg,png,gif"), ","),
		},
		Security: SecurityConfig{
			RateLimitRequests: getEnvAsInt("RATE_LIMIT_REQUESTS", 100),
			RateLimitDuration: getEnvAsInt("RATE_LIMIT_DURATION", 60),
			BcryptCost:        getEnvAsInt("BCRYPT_COST", 12),
		},
		Metrics: MetricsConfig{
			Enable: getEnvAsBool("ENABLE_METRICS", true),
			Port:   getEnv("METRICS_PORT", "9090"),
		},
	}

	return nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为int
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsBool 获取环境变量并转换为bool
func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		c.Database.User,
		c.Database.Password,
		c.Database.Host,
		c.Database.Port,
		c.Database.Name,
		c.Database.Charset,
	)
}

// GetRedisAddr 获取Redis地址
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", c.Redis.Host, c.Redis.Port)
}

// GetServerAddr 获取服务器地址
func (c *Config) GetServerAddr() string {
	return fmt.Sprintf("%s:%s", c.Server.Host, c.Server.Port)
}
