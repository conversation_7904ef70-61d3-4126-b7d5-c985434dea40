#!/bin/bash

# 腾讯云直播平台启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    print_step "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_message "依赖检查通过"
}

# 检查环境变量文件
check_env_files() {
    print_step "检查环境变量文件..."
    
    # 检查后端环境变量
    if [ ! -f "backend/config/.env" ]; then
        if [ -f "backend/config/.env.example" ]; then
            print_warning "后端 .env 文件不存在，从 .env.example 复制"
            cp backend/config/.env.example backend/config/.env
        else
            print_error "后端 .env.example 文件不存在"
            exit 1
        fi
    fi
    
    # 检查前端环境变量
    if [ ! -f "frontend/.env.local" ]; then
        if [ -f "frontend/.env.example" ]; then
            print_warning "前端 .env.local 文件不存在，从 .env.example 复制"
            cp frontend/.env.example frontend/.env.local
        else
            print_error "前端 .env.example 文件不存在"
            exit 1
        fi
    fi
    
    print_message "环境变量文件检查完成"
}

# 创建必要的目录
create_directories() {
    print_step "创建必要的目录..."
    
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p nginx/ssl
    
    print_message "目录创建完成"
}

# 构建和启动服务
start_services() {
    print_step "构建和启动服务..."
    
    # 停止现有服务
    docker-compose down
    
    # 构建并启动服务
    docker-compose up --build -d
    
    print_message "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    print_step "等待服务就绪..."
    
    # 等待MySQL就绪
    print_message "等待 MySQL 启动..."
    until docker-compose exec mysql mysqladmin ping -h"localhost" --silent; do
        sleep 2
    done
    
    # 等待Redis就绪
    print_message "等待 Redis 启动..."
    until docker-compose exec redis redis-cli ping; do
        sleep 2
    done
    
    # 等待后端服务就绪
    print_message "等待后端服务启动..."
    until curl -f http://localhost:8080/health; do
        sleep 5
    done
    
    # 等待前端服务就绪
    print_message "等待前端服务启动..."
    until curl -f http://localhost:80; do
        sleep 5
    done
    
    print_message "所有服务已就绪"
}

# 显示服务状态
show_status() {
    print_step "服务状态："
    docker-compose ps
    
    echo ""
    print_message "服务访问地址："
    echo "  前端应用: http://localhost"
    echo "  后端API: http://localhost:8080"
    echo "  API文档: http://localhost:8080/swagger/index.html"
    echo "  健康检查: http://localhost:8080/health"
    echo ""
    print_message "数据库连接信息："
    echo "  MySQL: localhost:3306"
    echo "  Redis: localhost:6379"
    echo ""
    print_message "默认管理员账号："
    echo "  用户名: admin"
    echo "  密码: admin123456"
}

# 显示日志
show_logs() {
    print_step "显示服务日志..."
    docker-compose logs -f
}

# 停止服务
stop_services() {
    print_step "停止服务..."
    docker-compose down
    print_message "服务已停止"
}

# 清理数据
clean_data() {
    print_step "清理数据..."
    docker-compose down -v
    docker system prune -f
    print_message "数据清理完成"
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            print_message "启动腾讯云直播平台..."
            check_dependencies
            check_env_files
            create_directories
            start_services
            wait_for_services
            show_status
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            main start
            ;;
        logs)
            show_logs
            ;;
        status)
            docker-compose ps
            ;;
        clean)
            clean_data
            ;;
        *)
            echo "用法: $0 {start|stop|restart|logs|status|clean}"
            echo ""
            echo "命令说明："
            echo "  start   - 启动所有服务（默认）"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  logs    - 查看服务日志"
            echo "  status  - 查看服务状态"
            echo "  clean   - 清理所有数据和容器"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
