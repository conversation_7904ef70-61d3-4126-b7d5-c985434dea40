// WebRTC 配置文件
export const WEBRTC_CONFIG = {
  // 腾讯云 TRTC 应用配置
  SDK_APP_ID: 1600091828, // 您的 SDKAppID
  
  // 推流域名配置
  PUSH_DOMAIN: '215131.push.tlivecloud.com',
  PLAY_DOMAIN: '215131.liveplay.myqcloud.com',
  
  // 默认推流配置
  DEFAULT_VIDEO_PROFILE: {
    width: 1280,
    height: 720,
    frameRate: 25,
    bitrate: 1000
  },
  
  // 音频配置
  DEFAULT_AUDIO_PROFILE: {
    sampleRate: 48000,
    bitrate: 64
  },
  
  // 支持的视频质量配置
  VIDEO_PROFILES: {
    '480p': {
      width: 640,
      height: 480,
      frameRate: 15,
      bitrate: 500,
      label: '流畅 (480p)'
    },
    '720p': {
      width: 1280,
      height: 720,
      frameRate: 25,
      bitrate: 1000,
      label: '标清 (720p)'
    },
    '1080p': {
      width: 1920,
      height: 1080,
      frameRate: 30,
      bitrate: 2000,
      label: '高清 (1080p)'
    },
    '4k': {
      width: 3840,
      height: 2160,
      frameRate: 30,
      bitrate: 8000,
      label: '超清 (4K)'
    }
  }
}

// 生成 UserSig 的函数
export async function generateUserSig(userId: string): Promise<string> {
  try {
    // 临时使用提供的 UserSig（生产环境应该从后端动态生成）
    // 注意：这个 UserSig 有时效性，过期后需要重新生成
    const userSig = 'eJwtjF0LgjAYRv-Lbgub83NCF15ECl0US4joZrEpL*lcc-RB9N9b6uVznsP5oOOOeQ9pUIaIh9Fy3CCkslDDiLnoQMFgDbe9mYVB3LjWIFDmxxhj6qcknR750mCk41EUEXdN1EL3Z0lInRhQOlegcf1rmlxW5ZmLvHgfYs7aan9XwfBk5ZadbBXXIq8Wbd70RbhZo*8PjIk1Aw__'

    console.log(`为用户 ${userId} 使用预配置的 UserSig`)
    return userSig

    // TODO: 在生产环境中，应该调用后端 API 生成 UserSig
    // const response = await fetch(`/api/auth/user-sig/${userId}`)
    // const data = await response.json()
    // return data.userSig
  } catch (error) {
    console.error('生成 UserSig 失败:', error)
    throw error
  }
}

// 检查 WebRTC 支持
export function checkWebRTCSupport(): {
  supported: boolean
  issues: string[]
} {
  const issues: string[] = []
  
  // 检查基本 WebRTC 支持
  if (!window.RTCPeerConnection) {
    issues.push('浏览器不支持 RTCPeerConnection')
  }
  
  // 检查 getUserMedia 支持
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    issues.push('浏览器不支持 getUserMedia')
  }
  
  // 检查 HTTPS
  if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
    issues.push('WebRTC 需要 HTTPS 协议（localhost 除外）')
  }
  
  return {
    supported: issues.length === 0,
    issues
  }
}

// 获取推荐的视频配置
export function getRecommendedVideoProfile(): string {
  // 根据设备性能和网络状况推荐配置
  const connection = (navigator as any).connection
  
  if (connection) {
    const effectiveType = connection.effectiveType
    
    switch (effectiveType) {
      case 'slow-2g':
      case '2g':
        return '480p'
      case '3g':
        return '720p'
      case '4g':
      default:
        return '1080p'
    }
  }
  
  // 默认返回 720p
  return '720p'
}
