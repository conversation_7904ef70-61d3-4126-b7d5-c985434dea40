@echo off
chcp 65001 >nul

echo ========================================
echo 腾讯云直播平台 - 最终测试
echo ========================================
echo.

echo [INFO] 检查环境...

:: 检查 Go 环境
go version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Go 未安装，请先安装 Go 1.21+
    pause
    exit /b 1
)
echo [OK] Go 环境检查通过

:: 检查 Node.js 环境
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)
echo [OK] Node.js 环境检查通过

echo.
echo [INFO] 准备后端...
cd backend

:: 检查配置文件
if not exist "config\.env" (
    echo [INFO] 复制配置文件...
    copy "config\.env.example" "config\.env" >nul
)

:: 创建必要目录
if not exist "bin" mkdir bin
if not exist "logs" mkdir logs

echo [INFO] 下载 Go 依赖...
go mod download
if errorlevel 1 (
    echo [ERROR] 下载依赖失败
    pause
    exit /b 1
)

echo [INFO] 编译后端...
go build -o bin\server.exe cmd\server\main.go
if errorlevel 1 (
    echo [ERROR] 编译失败
    pause
    exit /b 1
)
echo [OK] 后端编译成功

cd ..

echo.
echo [INFO] 准备前端...
cd frontend

:: 检查配置文件
if not exist ".env" (
    if exist ".env.example" (
        echo [INFO] 复制前端配置文件...
        copy ".env.example" ".env" >nul
    )
)

echo [INFO] 安装前端依赖...
npm install --silent
if errorlevel 1 (
    echo [ERROR] 安装前端依赖失败
    pause
    exit /b 1
)
echo [OK] 前端依赖安装成功

cd ..

echo.
echo ========================================
echo 🎉 测试完成！
echo ========================================
echo.
echo 📋 配置信息：
echo   腾讯云 SecretId: AKIDYw4vVpsfCSSBdojNkrjMiofFYbOu9CyU
echo   推流域名: 215131.push.tlivecloud.com
echo   播放域名: 215131.liveplay.myqcloud.com
echo.
echo 🚀 启动命令：
echo   后端: cd backend ^&^& bin\server.exe
echo   前端: cd frontend ^&^& npm run dev
echo.
echo 🌐 访问地址：
echo   前端: http://localhost:5173
echo   后端: http://localhost:8080
echo   健康检查: http://localhost:8080/health
echo.
echo 📝 API 端点：
echo   POST /api/auth/register - 用户注册
echo   POST /api/auth/login - 用户登录
echo   POST /api/rooms - 创建直播间
echo   GET /api/rooms/my - 获取我的直播间
echo   POST /api/rooms/{id}/start - 开始直播
echo   POST /api/rooms/{id}/stop - 结束直播
echo.

pause
