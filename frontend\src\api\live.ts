// 直播相关API

import { http } from './request'
import type {
  Room,
  CreateRoomRequest,
  UpdateRoomRequest,
  RoomListResponse,
  Category,
  Message,
  MessageListResponse,
  StreamStatus
} from '@/types/live'
import type { PaginationParams } from '@/types/common'

// 直播间管理API
export const roomApi = {
  // 创建直播间
  createRoom(data: CreateRoomRequest) {
    return http.post<Room>('/rooms', data)
  },

  // 获取当前用户的直播间
  getMyRoom() {
    return http.get<Room>('/rooms/my')
  },

  // 获取直播间详情
  getRoomDetail(roomId: number) {
    return http.get<Room>(`/rooms/${roomId}`)
  },

  // 更新直播间信息
  updateRoom(roomId: number, data: UpdateRoomRequest) {
    return http.put<Room>(`/rooms/${roomId}`, data)
  },

  // 删除直播间
  deleteRoom(roomId: number) {
    return http.delete(`/rooms/${roomId}`)
  },

  // 获取直播间列表
  getRoomList(params?: PaginationParams & {
    category_id?: number
    keyword?: string
    status?: number
  }) {
    return http.get<RoomListResponse>('/rooms', { params })
  },

  // 开始直播
  startLive(roomId: number) {
    return http.post(`/rooms/${roomId}/start`)
  },

  // 结束直播
  stopLive(roomId: number) {
    return http.post(`/rooms/${roomId}/stop`)
  },

  // 获取直播流状态
  getStreamStatus(roomId: number) {
    return http.get<StreamStatus>(`/rooms/${roomId}/status`)
  },

  // 生成新的推流密钥
  regenerateStreamKey(roomId: number) {
    return http.post<{ stream_key: string }>(`/rooms/${roomId}/regenerate-key`)
  }
}

// 分类管理API
export const categoryApi = {
  // 获取分类列表
  getCategories() {
    return http.get<Category[]>('/categories')
  },

  // 获取分类详情
  getCategoryDetail(categoryId: number) {
    return http.get<Category>(`/categories/${categoryId}`)
  }
}

// 聊天消息API
export const chatApi = {
  // 获取聊天消息列表
  getMessages(roomId: number, params?: PaginationParams) {
    return http.get<MessageListResponse>(`/rooms/${roomId}/messages`, { params })
  },

  // 发送消息
  sendMessage(roomId: number, data: {
    content: string
    type?: number
  }) {
    return http.post<Message>(`/rooms/${roomId}/messages`, data)
  },

  // 删除消息
  deleteMessage(roomId: number, messageId: number) {
    return http.delete(`/rooms/${roomId}/messages/${messageId}`)
  }
}

// 直播统计API
export const statsApi = {
  // 获取直播间统计数据
  getRoomStats(roomId: number, params?: {
    start_date?: string
    end_date?: string
  }) {
    return http.get(`/rooms/${roomId}/stats`, { params })
  },

  // 获取用户直播统计
  getUserStats(params?: {
    start_date?: string
    end_date?: string
  }) {
    return http.get('/users/stats', { params })
  }
}

// 直播互动API
export const interactionApi = {
  // 点赞直播间
  likeRoom(roomId: number) {
    return http.post(`/rooms/${roomId}/like`)
  },

  // 取消点赞
  unlikeRoom(roomId: number) {
    return http.delete(`/rooms/${roomId}/like`)
  },

  // 分享直播间
  shareRoom(roomId: number) {
    return http.post(`/rooms/${roomId}/share`)
  },

  // 举报直播间
  reportRoom(roomId: number, data: {
    reason: string
    description?: string
  }) {
    return http.post(`/rooms/${roomId}/report`, data)
  }
}

// 礼物系统API
export const giftApi = {
  // 获取礼物列表
  getGifts() {
    return http.get('/gifts')
  },

  // 发送礼物
  sendGift(roomId: number, data: {
    gift_id: number
    count: number
    target_user_id?: number
  }) {
    return http.post(`/rooms/${roomId}/gifts`, data)
  },

  // 获取礼物记录
  getGiftHistory(roomId: number, params?: PaginationParams) {
    return http.get(`/rooms/${roomId}/gifts`, { params })
  }
}
