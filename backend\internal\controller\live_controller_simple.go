package controller

import (
	"net/http"
	"strconv"

	"live-streaming-platform/internal/model"
	"live-streaming-platform/internal/service"

	"github.com/gin-gonic/gin"
)

// SimpleLiveController 简化的直播控制器
type SimpleLiveController struct {
	liveService *service.LiveService
}

// NewSimpleLiveController 创建简化的直播控制器
func NewSimpleLiveController(liveService *service.LiveService) *SimpleLiveController {
	return &SimpleLiveController{
		liveService: liveService,
	}
}

// CreateRoom 创建直播间
func (c *SimpleLiveController) CreateRoom(ctx *gin.Context) {
	userID := getUserID(ctx)
	if userID == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	var req model.CreateRoomRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	room, err := c.liveService.CreateRoom(userID, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": room})
}

// GetMyRoom 获取当前用户的直播间
func (c *SimpleLiveController) GetMyRoom(ctx *gin.Context) {
	userID := getUserID(ctx)
	if userID == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	room, err := c.liveService.GetUserRoom(userID)
	if err != nil {
		// 检查是否是用户没有直播间的情况
		if err.Error() == "获取用户直播间失败: 用户没有直播间" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "用户还没有创建直播间"})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": room})
}

// GetRoomDetail 获取直播间详情
func (c *SimpleLiveController) GetRoomDetail(ctx *gin.Context) {
	roomIDStr := ctx.Param("id")
	roomID, err := strconv.ParseUint(roomIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "直播间ID格式错误"})
		return
	}

	room, err := c.liveService.GetRoom(uint(roomID))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": room})
}

// StartLive 开始直播
func (c *SimpleLiveController) StartLive(ctx *gin.Context) {
	userID := getUserID(ctx)
	if userID == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	roomIDStr := ctx.Param("id")
	roomID, err := strconv.ParseUint(roomIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "直播间ID格式错误"})
		return
	}

	if err := c.liveService.StartLive(uint(roomID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "直播已开始"})
}

// StopLive 结束直播
func (c *SimpleLiveController) StopLive(ctx *gin.Context) {
	userID := getUserID(ctx)
	if userID == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "用户未登录"})
		return
	}

	roomIDStr := ctx.Param("id")
	roomID, err := strconv.ParseUint(roomIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "直播间ID格式错误"})
		return
	}

	if err := c.liveService.StopLive(uint(roomID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "直播已结束"})
}

// GetRoomList 获取直播间列表
func (c *SimpleLiveController) GetRoomList(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	categoryID, _ := strconv.ParseUint(ctx.Query("category_id"), 10, 32)
	keyword := ctx.Query("keyword")
	status, _ := strconv.Atoi(ctx.DefaultQuery("status", "-1"))

	rooms, total, err := c.liveService.GetRoomList(page, pageSize, uint(categoryID), keyword, status)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": gin.H{
			"rooms":     rooms,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// getUserID 从上下文中获取用户ID
func getUserID(ctx *gin.Context) uint {
	if userID, exists := ctx.Get("user_id"); exists {
		if id, ok := userID.(uint); ok {
			return id
		}
	}
	return 0
}
