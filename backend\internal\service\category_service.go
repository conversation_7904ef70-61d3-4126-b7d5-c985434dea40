package service

import (
	"fmt"
	"time"

	"live-streaming-platform/internal/model"
	"live-streaming-platform/internal/repository"
)

// CategoryService 分类服务
type CategoryService struct {
	categoryRepo repository.CategoryRepository
}

// NewCategoryService 创建分类服务
func NewCategoryService(categoryRepo repository.CategoryRepository) *CategoryService {
	return &CategoryService{
		categoryRepo: categoryRepo,
	}
}

// GetCategories 获取分类列表
func (s *CategoryService) GetCategories() ([]*model.Category, error) {
	categories, err := s.categoryRepo.GetActiveCategories()
	if err != nil {
		return nil, fmt.Errorf("获取分类列表失败: %w", err)
	}
	return categories, nil
}

// GetCategoryByID 根据ID获取分类
func (s *CategoryService) GetCategoryByID(categoryID uint) (*model.Category, error) {
	category, err := s.categoryRepo.GetByID(categoryID)
	if err != nil {
		return nil, fmt.Errorf("获取分类失败: %w", err)
	}
	return category, nil
}

// CreateCategory 创建分类
func (s *CategoryService) CreateCategory(category *model.Category) error {
	category.CreatedAt = time.Now()
	category.UpdatedAt = time.Now()

	if err := s.categoryRepo.Create(category); err != nil {
		return fmt.Errorf("创建分类失败: %w", err)
	}
	return nil
}

// UpdateCategory 更新分类
func (s *CategoryService) UpdateCategory(category *model.Category) error {
	category.UpdatedAt = time.Now()

	if err := s.categoryRepo.Update(category); err != nil {
		return fmt.Errorf("更新分类失败: %w", err)
	}
	return nil
}

// DeleteCategory 删除分类
func (s *CategoryService) DeleteCategory(categoryID uint) error {
	// 检查是否有直播间使用此分类
	count, err := s.categoryRepo.GetRoomCountByCategory(categoryID)
	if err != nil {
		return fmt.Errorf("检查分类使用情况失败: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("该分类下还有 %d 个直播间，无法删除", count)
	}

	if err := s.categoryRepo.Delete(categoryID); err != nil {
		return fmt.Errorf("删除分类失败: %w", err)
	}
	return nil
}

// InitDefaultCategories 初始化默认分类
func (s *CategoryService) InitDefaultCategories() error {
	// 检查是否已有分类
	categories, err := s.categoryRepo.GetActiveCategories()
	if err != nil {
		return fmt.Errorf("检查分类失败: %w", err)
	}

	if len(categories) > 0 {
		return nil // 已有分类，不需要初始化
	}

	// 创建默认分类
	defaultCategories := []*model.Category{
		{
			Name:        "游戏",
			Description: "游戏直播",
			Icon:        "game",
			Sort:        1,
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "娱乐",
			Description: "娱乐直播",
			Icon:        "entertainment",
			Sort:        2,
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "教育",
			Description: "教育直播",
			Icon:        "education",
			Sort:        3,
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "科技",
			Description: "科技直播",
			Icon:        "technology",
			Sort:        4,
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "生活",
			Description: "生活直播",
			Icon:        "lifestyle",
			Sort:        5,
			IsActive:    true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	for _, category := range defaultCategories {
		if err := s.categoryRepo.Create(category); err != nil {
			return fmt.Errorf("创建默认分类失败: %w", err)
		}
	}

	return nil
}
