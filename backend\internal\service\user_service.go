package service

import (
	"context"
	"fmt"
	"time"

	"live-streaming-platform/internal/model"
	"live-streaming-platform/internal/repository"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

// UserService 用户服务接口
type UserService interface {
	Register(ctx context.Context, req *RegisterRequest) (*AuthResponse, error)
	Login(ctx context.Context, req *LoginRequest) (*AuthResponse, error)
	GetProfile(ctx context.Context, userID uint) (*model.UserProfile, error)
	UpdateProfile(ctx context.Context, userID uint, req *UpdateProfileRequest) error
	ChangePassword(ctx context.Context, userID uint, req *ChangePasswordRequest) error
	Follow(ctx context.Context, followerID, followedID uint) error
	Unfollow(ctx context.Context, followerID, followedID uint) error
	GetFollowers(ctx context.Context, userID uint, page, pageSize int) (*FollowListResponse, error)
	GetFollowing(ctx context.Context, userID uint, page, pageSize int) (*FollowListResponse, error)
	SearchUsers(ctx context.Context, keyword string, page, pageSize int) (*UserListResponse, error)
	ValidateToken(ctx context.Context, tokenString string) (*TokenClaims, error)
	RefreshToken(ctx context.Context, tokenString string) (*AuthResponse, error)
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
	Nickname string `json:"nickname" validate:"max=50"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// UpdateProfileRequest 更新资料请求
type UpdateProfileRequest struct {
	Nickname string     `json:"nickname" validate:"max=50"`
	Avatar   string     `json:"avatar" validate:"url"`
	Gender   int        `json:"gender" validate:"min=0,max=2"`
	Birthday *time.Time `json:"birthday"`
	Bio      string     `json:"bio" validate:"max=500"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6"`
}

// AuthResponse 认证响应
type AuthResponse struct {
	Token     string             `json:"token"`
	ExpiresAt time.Time          `json:"expires_at"`
	User      *model.UserProfile `json:"user"`
}

// FollowListResponse 关注列表响应
type FollowListResponse struct {
	Users      []*model.UserProfile `json:"users"`
	Total      int64                `json:"total"`
	Page       int                  `json:"page"`
	PageSize   int                  `json:"page_size"`
	TotalPages int                  `json:"total_pages"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users      []*model.UserProfile `json:"users"`
	Total      int64                `json:"total"`
	Page       int                  `json:"page"`
	PageSize   int                  `json:"page_size"`
	TotalPages int                  `json:"total_pages"`
}

// TokenClaims JWT声明
type TokenClaims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// userService 用户服务实现
type userService struct {
	userRepo  repository.UserRepository
	jwtSecret string
	jwtExpire time.Duration
	bcryptCost int
}

// NewUserService 创建用户服务
func NewUserService(userRepo repository.UserRepository, jwtSecret string, jwtExpireHours int, bcryptCost int) UserService {
	return &userService{
		userRepo:   userRepo,
		jwtSecret:  jwtSecret,
		jwtExpire:  time.Duration(jwtExpireHours) * time.Hour,
		bcryptCost: bcryptCost,
	}
}

// Register 用户注册
func (s *userService) Register(ctx context.Context, req *RegisterRequest) (*AuthResponse, error) {
	// 检查用户名是否已存在
	existingUser, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		return nil, fmt.Errorf("检查用户名失败: %w", err)
	}
	if existingUser != nil {
		return nil, fmt.Errorf("用户名已存在")
	}

	// 检查邮箱是否已存在
	existingUser, err = s.userRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("检查邮箱失败: %w", err)
	}
	if existingUser != nil {
		return nil, fmt.Errorf("邮箱已存在")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), s.bcryptCost)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 创建用户
	user := &model.User{
		Username: req.Username,
		Email:    req.Email,
		Password: string(hashedPassword),
		Nickname: req.Nickname,
		Status:   1,
		Role:     "user",
		Level:    1,
	}

	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 生成JWT令牌
	token, expiresAt, err := s.generateToken(user)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	return &AuthResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User:      user.ToProfile(),
	}, nil
}

// Login 用户登录
func (s *userService) Login(ctx context.Context, req *LoginRequest) (*AuthResponse, error) {
	// 获取用户信息
	user, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, fmt.Errorf("用户账号已被禁用")
	}

	// 更新最后登录信息
	if err := s.userRepo.UpdateLastLogin(ctx, user.ID, ""); err != nil {
		// 记录日志但不影响登录
		fmt.Printf("更新最后登录信息失败: %v\n", err)
	}

	// 生成JWT令牌
	token, expiresAt, err := s.generateToken(user)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	return &AuthResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User:      user.ToProfile(),
	}, nil
}

// GetProfile 获取用户资料
func (s *userService) GetProfile(ctx context.Context, userID uint) (*model.UserProfile, error) {
	profile, err := s.userRepo.GetProfile(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户资料失败: %w", err)
	}
	if profile == nil {
		return nil, fmt.Errorf("用户不存在")
	}
	return profile, nil
}

// UpdateProfile 更新用户资料
func (s *userService) UpdateProfile(ctx context.Context, userID uint, req *UpdateProfileRequest) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 更新字段
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	if req.Gender >= 0 && req.Gender <= 2 {
		user.Gender = req.Gender
	}
	if req.Birthday != nil {
		user.Birthday = req.Birthday
	}
	if req.Bio != "" {
		user.Bio = req.Bio
	}

	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("更新用户资料失败: %w", err)
	}

	return nil
}

// ChangePassword 修改密码
func (s *userService) ChangePassword(ctx context.Context, userID uint, req *ChangePasswordRequest) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword)); err != nil {
		return fmt.Errorf("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), s.bcryptCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	user.Password = string(hashedPassword)
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	return nil
}

// Follow 关注用户
func (s *userService) Follow(ctx context.Context, followerID, followedID uint) error {
	return s.userRepo.Follow(ctx, followerID, followedID)
}

// Unfollow 取消关注
func (s *userService) Unfollow(ctx context.Context, followerID, followedID uint) error {
	return s.userRepo.Unfollow(ctx, followerID, followedID)
}

// GetFollowers 获取粉丝列表
func (s *userService) GetFollowers(ctx context.Context, userID uint, page, pageSize int) (*FollowListResponse, error) {
	offset := (page - 1) * pageSize
	users, total, err := s.userRepo.GetFollowers(ctx, userID, offset, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取粉丝列表失败: %w", err)
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &FollowListResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// GetFollowing 获取关注列表
func (s *userService) GetFollowing(ctx context.Context, userID uint, page, pageSize int) (*FollowListResponse, error) {
	offset := (page - 1) * pageSize
	users, total, err := s.userRepo.GetFollowing(ctx, userID, offset, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取关注列表失败: %w", err)
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &FollowListResponse{
		Users:      users,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// SearchUsers 搜索用户
func (s *userService) SearchUsers(ctx context.Context, keyword string, page, pageSize int) (*UserListResponse, error) {
	offset := (page - 1) * pageSize
	users, total, err := s.userRepo.Search(ctx, keyword, offset, pageSize)
	if err != nil {
		return nil, fmt.Errorf("搜索用户失败: %w", err)
	}

	// 转换为用户资料
	profiles := make([]*model.UserProfile, len(users))
	for i, user := range users {
		profiles[i] = user.ToProfile()
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &UserListResponse{
		Users:      profiles,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// ValidateToken 验证JWT令牌
func (s *userService) ValidateToken(ctx context.Context, tokenString string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.jwtSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("解析令牌失败: %w", err)
	}

	if claims, ok := token.Claims.(*TokenClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("无效的令牌")
}

// RefreshToken 刷新令牌
func (s *userService) RefreshToken(ctx context.Context, tokenString string) (*AuthResponse, error) {
	claims, err := s.ValidateToken(ctx, tokenString)
	if err != nil {
		return nil, err
	}

	user, err := s.userRepo.GetByID(ctx, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 生成新令牌
	token, expiresAt, err := s.generateToken(user)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	return &AuthResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User:      user.ToProfile(),
	}, nil
}

// generateToken 生成JWT令牌
func (s *userService) generateToken(user *model.User) (string, time.Time, error) {
	expiresAt := time.Now().Add(s.jwtExpire)
	
	claims := &TokenClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.jwtSecret))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expiresAt, nil
}
