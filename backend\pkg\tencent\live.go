package tencent

import (
	"crypto/md5"
	"fmt"
	"strings"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	live "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/live/v20180801"
)

// LiveClient 腾讯云直播客户端
type LiveClient struct {
	client     *live.Client
	secretID   string
	secretKey  string
	region     string
	pushDomain string
	playDomain string
	pushKey    string
	playKey    string
	expireTime int64
}

// LiveConfig 直播配置
type LiveConfig struct {
	SecretID   string
	SecretKey  string
	Region     string
	PushDomain string
	PlayDomain string
	PushKey    string
	PlayKey    string
	ExpireTime int64
}

// StreamInfo 流信息
type StreamInfo struct {
	StreamName    string `json:"stream_name"`
	PushURL       string `json:"push_url"`
	WebRTCPushURL string `json:"webrtc_push_url"`
	PlayURL       string `json:"play_url"`
	HLSPlayURL    string `json:"hls_play_url"`
	FLVPlayURL    string `json:"flv_play_url"`
	WebRTCPlayURL string `json:"webrtc_play_url"`
	ExpireTime    int64  `json:"expire_time"`
}

// StreamStatus 流状态
type StreamStatus struct {
	StreamName  string    `json:"stream_name"`
	Status      string    `json:"status"` // active, inactive
	StartTime   time.Time `json:"start_time"`
	ViewerCount int64     `json:"viewer_count"`
	Bandwidth   int64     `json:"bandwidth"`
	VideoCodec  string    `json:"video_codec"`
	AudioCodec  string    `json:"audio_codec"`
	Resolution  string    `json:"resolution"`
	FrameRate   int64     `json:"frame_rate"`
	Bitrate     int64     `json:"bitrate"`
}

// RecordInfo 录制信息
type RecordInfo struct {
	StreamName string    `json:"stream_name"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	Duration   int64     `json:"duration"`
	FileSize   int64     `json:"file_size"`
	VideoURL   string    `json:"video_url"`
	Format     string    `json:"format"`
}

// SnapshotInfo 截图信息
type SnapshotInfo struct {
	StreamName string    `json:"stream_name"`
	CreateTime time.Time `json:"create_time"`
	PicURL     string    `json:"pic_url"`
	Width      int64     `json:"width"`
	Height     int64     `json:"height"`
}

// NewLiveClient 创建直播客户端
func NewLiveClient(config *LiveConfig) (*LiveClient, error) {
	credential := common.NewCredential(config.SecretID, config.SecretKey)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "live.tencentcloudapi.com"

	client, err := live.NewClient(credential, regions.Beijing, cpf)
	if err != nil {
		return nil, fmt.Errorf("创建腾讯云直播客户端失败: %w", err)
	}

	return &LiveClient{
		client:     client,
		secretID:   config.SecretID,
		secretKey:  config.SecretKey,
		region:     config.Region,
		pushDomain: config.PushDomain,
		playDomain: config.PlayDomain,
		pushKey:    config.PushKey,
		playKey:    config.PlayKey,
		expireTime: config.ExpireTime,
	}, nil
}

// GenerateStreamInfo 生成流信息
func (c *LiveClient) GenerateStreamInfo(streamName string) (*StreamInfo, error) {
	if streamName == "" {
		return nil, fmt.Errorf("流名称不能为空")
	}

	expireTime := time.Now().Unix() + c.expireTime

	// 生成推流URL
	pushURL, err := c.generatePushURL(streamName, expireTime)
	if err != nil {
		return nil, fmt.Errorf("生成推流URL失败: %w", err)
	}

	// 生成 WebRTC 推流URL
	webrtcPushURL, err := c.generateWebRTCPushURL(streamName, expireTime)
	if err != nil {
		return nil, fmt.Errorf("生成WebRTC推流URL失败: %w", err)
	}

	// 生成播放URL
	playURL := c.generatePlayURL(streamName, "rtmp")
	hlsPlayURL := c.generatePlayURL(streamName, "hls")
	flvPlayURL := c.generatePlayURL(streamName, "flv")
	webrtcPlayURL := c.generatePlayURL(streamName, "webrtc")

	return &StreamInfo{
		StreamName:    streamName,
		PushURL:       pushURL,
		WebRTCPushURL: webrtcPushURL,
		PlayURL:       playURL,
		HLSPlayURL:    hlsPlayURL,
		FLVPlayURL:    flvPlayURL,
		WebRTCPlayURL: webrtcPlayURL,
		ExpireTime:    expireTime,
	}, nil
}

// generatePushURL 生成推流URL
func (c *LiveClient) generatePushURL(streamName string, expireTime int64) (string, error) {
	if c.pushDomain == "" {
		return "", fmt.Errorf("推流域名未配置")
	}

	// 构建推流路径
	streamPath := fmt.Sprintf("/live/%s", streamName)

	// 生成鉴权参数
	authString := ""
	if c.pushKey != "" {
		authString = c.generateAuthString(streamPath, expireTime, c.pushKey)
	}

	// 构建完整的推流URL
	pushURL := fmt.Sprintf("rtmp://%s%s", c.pushDomain, streamPath)
	if authString != "" {
		pushURL += "?" + authString
	}

	return pushURL, nil
}

// generateWebRTCPushURL 生成WebRTC推流URL
func (c *LiveClient) generateWebRTCPushURL(streamName string, expireTime int64) (string, error) {
	if c.pushDomain == "" {
		return "", fmt.Errorf("推流域名未配置")
	}

	// WebRTC 推流使用 WHIP 协议
	// 构建推流路径
	streamPath := fmt.Sprintf("/live/%s", streamName)

	// 生成鉴权参数
	authString := ""
	if c.pushKey != "" {
		authString = c.generateAuthString(streamPath, expireTime, c.pushKey)
	}

	// 构建完整的WebRTC推流URL
	webrtcPushURL := fmt.Sprintf("webrtc://%s%s", c.pushDomain, streamPath)
	if authString != "" {
		webrtcPushURL += "?" + authString
	}

	return webrtcPushURL, nil
}

// generatePlayURL 生成播放URL
func (c *LiveClient) generatePlayURL(streamName, protocol string) string {
	if c.playDomain == "" {
		return ""
	}

	switch protocol {
	case "rtmp":
		return fmt.Sprintf("rtmp://%s/live/%s", c.playDomain, streamName)
	case "hls":
		return fmt.Sprintf("http://%s/live/%s.m3u8", c.playDomain, streamName)
	case "flv":
		return fmt.Sprintf("http://%s/live/%s.flv", c.playDomain, streamName)
	case "webrtc":
		return fmt.Sprintf("webrtc://%s/live/%s", c.playDomain, streamName)
	default:
		return fmt.Sprintf("rtmp://%s/live/%s", c.playDomain, streamName)
	}
}

// generateAuthString 生成鉴权字符串
func (c *LiveClient) generateAuthString(streamPath string, expireTime int64, key string) string {
	// 腾讯云推流鉴权算法
	// txSecret = MD5(key + streamPath + expireTimeHex)
	// 最终鉴权URL: streamPath?txSecret=txSecret&txTime=expireTimeHex

	expireTimeHex := fmt.Sprintf("%x", expireTime)
	hashValue := fmt.Sprintf("%s%s%s", key, streamPath, expireTimeHex)

	h := md5.New()
	h.Write([]byte(hashValue))
	txSecret := fmt.Sprintf("%x", h.Sum(nil))

	return fmt.Sprintf("txSecret=%s&txTime=%s", txSecret, expireTimeHex)
}

// GetStreamStatus 获取流状态
func (c *LiveClient) GetStreamStatus(streamName string) (*StreamStatus, error) {
	// 简化实现：返回模拟的流状态
	// 在实际生产环境中，这里应该调用腾讯云 API 获取真实状态
	status := &StreamStatus{
		StreamName:  streamName,
		Status:      "active", // 假设流是活跃的
		StartTime:   time.Now(),
		ViewerCount: 0,
		Bandwidth:   1000,
		VideoCodec:  "H.264",
		AudioCodec:  "AAC",
		Resolution:  "1920x1080",
		FrameRate:   30,
		Bitrate:     2000,
	}

	return status, nil
}

// StopStream 停止推流
func (c *LiveClient) StopStream(streamName string) error {
	// 简化实现：在实际生产环境中，这里应该调用腾讯云 API 停止推流
	// 目前返回成功，表示停止推流操作已执行
	return nil
}

// CreateRecordTask 创建录制任务
func (c *LiveClient) CreateRecordTask(streamName string, templateID int64) error {
	request := live.NewCreateLiveRecordRequest()
	request.DomainName = &c.pushDomain
	request.StreamName = &streamName
	// 注意：根据实际 SDK 版本调整字段名
	// request.TemplateId = &templateID

	_, err := c.client.CreateLiveRecord(request)
	if err != nil {
		return fmt.Errorf("创建录制任务失败: %w", err)
	}

	return nil
}

// StopRecordTask 停止录制任务
func (c *LiveClient) StopRecordTask(streamName string, taskID int64) error {
	request := live.NewStopLiveRecordRequest()
	// 注意：根据实际 SDK 版本调整字段名
	// request.DomainName = &c.pushDomain
	request.StreamName = &streamName
	request.TaskId = &taskID

	_, err := c.client.StopLiveRecord(request)
	if err != nil {
		return fmt.Errorf("停止录制任务失败: %w", err)
	}

	return nil
}

// CreateSnapshotTask 创建截图任务
func (c *LiveClient) CreateSnapshotTask(streamName string, templateID int64) error {
	request := live.NewCreateLiveSnapshotRuleRequest()
	request.DomainName = &c.pushDomain
	request.StreamName = &streamName
	// 注意：根据实际 SDK 版本调整字段名
	// request.TemplateId = &templateID

	_, err := c.client.CreateLiveSnapshotRule(request)
	if err != nil {
		return fmt.Errorf("创建截图任务失败: %w", err)
	}

	return nil
}

// ValidateCallback 验证回调签名
func (c *LiveClient) ValidateCallback(timestamp, sign string, body []byte) bool {
	if c.pushKey == "" {
		return true // 如果没有配置密钥，则不验证
	}

	// 腾讯云回调验证算法
	// sign = MD5(key + timestamp + body)
	hashValue := fmt.Sprintf("%s%s%s", c.pushKey, timestamp, string(body))

	h := md5.New()
	h.Write([]byte(hashValue))
	expectedSign := fmt.Sprintf("%x", h.Sum(nil))

	return strings.ToLower(sign) == strings.ToLower(expectedSign)
}

// GenerateStreamKey 生成流密钥
func GenerateStreamKey(userID uint, roomID uint) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("user_%d_room_%d_%d", userID, roomID, timestamp)
}
