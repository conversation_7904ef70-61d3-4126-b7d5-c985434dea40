package service

import (
	"fmt"
	"time"

	"live-streaming-platform/config"
	"live-streaming-platform/internal/model"
	"live-streaming-platform/internal/repository"
	"live-streaming-platform/pkg/tencent"
)

// LiveService 直播服务
type LiveService struct {
	roomRepo   repository.RoomRepository
	liveClient *tencent.LiveClient
}

// NewLiveService 创建直播服务
func NewLiveService(roomRepo repository.RoomRepository) (*LiveService, error) {
	// 创建腾讯云直播客户端
	liveConfig := &tencent.LiveConfig{
		SecretID:   config.AppConfig.Tencent.SecretID,
		SecretKey:  config.AppConfig.Tencent.SecretKey,
		Region:     config.AppConfig.Tencent.Region,
		PushDomain: config.AppConfig.Tencent.PushDomain,
		PlayDomain: config.AppConfig.Tencent.PlayDomain,
		PushKey:    config.AppConfig.Tencent.PushKey,
		PlayKey:    config.AppConfig.Tencent.PlayKey,
		ExpireTime: config.AppConfig.Tencent.ExpireTime,
	}

	liveClient, err := tencent.NewLiveClient(liveConfig)
	if err != nil {
		return nil, fmt.Errorf("创建腾讯云直播客户端失败: %w", err)
	}

	return &LiveService{
		roomRepo:   roomRepo,
		liveClient: liveClient,
	}, nil
}

// CreateRoom 创建直播间
func (s *LiveService) CreateRoom(userID uint, req *model.CreateRoomRequest) (*model.Room, error) {
	// 检查用户是否已有直播间
	existingRoom, err := s.roomRepo.GetByUserID(userID)
	if err == nil && existingRoom != nil {
		return nil, fmt.Errorf("用户已有直播间")
	}

	// 生成流密钥
	streamKey := tencent.GenerateStreamKey(userID, 0) // roomID暂时为0，创建后更新

	// 创建直播间
	room := &model.Room{
		UserID:       userID,
		Title:        req.Title,
		Description:  req.Description,
		CategoryID:   req.CategoryID,
		Tags:         req.Tags,
		IsPrivate:    req.IsPrivate,
		Password:     req.Password,
		AllowChat:    req.AllowChat,
		AllowGift:    req.AllowGift,
		ChatMode:     req.ChatMode,
		QualityLevel: req.QualityLevel,
		StreamKey:    streamKey,
		Status:       0, // 未开播
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 保存到数据库
	if err := s.roomRepo.Create(room); err != nil {
		return nil, fmt.Errorf("创建直播间失败: %w", err)
	}

	// 更新流密钥（包含roomID）
	room.StreamKey = tencent.GenerateStreamKey(userID, room.ID)
	if err := s.roomRepo.Update(room); err != nil {
		return nil, fmt.Errorf("更新流密钥失败: %w", err)
	}

	// 生成推流和播放地址
	if err := s.generateStreamURLs(room); err != nil {
		return nil, fmt.Errorf("生成流地址失败: %w", err)
	}

	return room, nil
}

// GetRoom 获取直播间详情
func (s *LiveService) GetRoom(roomID uint) (*model.Room, error) {
	room, err := s.roomRepo.GetByID(roomID)
	if err != nil {
		return nil, fmt.Errorf("获取直播间失败: %w", err)
	}

	// 生成流地址（如果还没有）
	if room.PushURL == "" {
		if err := s.generateStreamURLs(room); err != nil {
			return nil, fmt.Errorf("生成流地址失败: %w", err)
		}
	}

	return room, nil
}

// GetUserRoom 获取用户的直播间
func (s *LiveService) GetUserRoom(userID uint) (*model.Room, error) {
	room, err := s.roomRepo.GetByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户直播间失败: %w", err)
	}

	// 生成流地址（如果还没有）
	if room.PushURL == "" {
		if err := s.generateStreamURLs(room); err != nil {
			return nil, fmt.Errorf("生成流地址失败: %w", err)
		}
	}

	return room, nil
}

// UpdateRoom 更新直播间信息
func (s *LiveService) UpdateRoom(roomID uint, req *model.UpdateRoomRequest) (*model.Room, error) {
	room, err := s.roomRepo.GetByID(roomID)
	if err != nil {
		return nil, fmt.Errorf("获取直播间失败: %w", err)
	}

	// 更新字段
	if req.Title != "" {
		room.Title = req.Title
	}
	if req.Description != "" {
		room.Description = req.Description
	}
	if req.CategoryID > 0 {
		room.CategoryID = req.CategoryID
	}
	if req.Tags != "" {
		room.Tags = req.Tags
	}
	if req.Cover != "" {
		room.Cover = req.Cover
	}
	room.UpdatedAt = time.Now()

	if err := s.roomRepo.Update(room); err != nil {
		return nil, fmt.Errorf("更新直播间失败: %w", err)
	}

	return room, nil
}

// StartLive 开始直播
func (s *LiveService) StartLive(roomID uint) error {
	room, err := s.roomRepo.GetByID(roomID)
	if err != nil {
		return fmt.Errorf("获取直播间失败: %w", err)
	}

	if room.Status == 1 {
		return fmt.Errorf("直播间已在直播中")
	}

	// 允许未开播(0)和已结束(3)状态开始直播
	if room.Status != 0 && room.Status != 3 {
		return fmt.Errorf("直播间状态不允许开始直播")
	}

	// 如果是重新开始直播，清空之前的结束时间和时长
	isRestart := room.Status == 3

	// 更新直播状态
	room.Status = 1
	now := time.Now()
	room.StartedAt = &now
	room.UpdatedAt = now

	if isRestart {
		room.EndedAt = nil
		room.Duration = 0
	}

	if err := s.roomRepo.Update(room); err != nil {
		return fmt.Errorf("更新直播状态失败: %w", err)
	}

	return nil
}

// StopLive 结束直播
func (s *LiveService) StopLive(roomID uint) error {
	room, err := s.roomRepo.GetByID(roomID)
	if err != nil {
		return fmt.Errorf("获取直播间失败: %w", err)
	}

	if room.Status != 1 {
		return fmt.Errorf("直播间未在直播中")
	}

	// 停止腾讯云推流
	if err := s.liveClient.StopStream(room.StreamKey); err != nil {
		// 记录错误但不阻止流程
		fmt.Printf("停止腾讯云推流失败: %v\n", err)
	}

	// 更新直播状态
	room.Status = 3 // 已结束
	now := time.Now()
	room.EndedAt = &now
	room.UpdatedAt = now

	// 计算直播时长
	if room.StartedAt != nil {
		duration := room.EndedAt.Sub(*room.StartedAt)
		room.Duration = int(duration.Seconds())
	}

	if err := s.roomRepo.Update(room); err != nil {
		return fmt.Errorf("更新直播状态失败: %w", err)
	}

	return nil
}

// GetStreamStatus 获取流状态
func (s *LiveService) GetStreamStatus(roomID uint) (*tencent.StreamStatus, error) {
	room, err := s.roomRepo.GetByID(roomID)
	if err != nil {
		return nil, fmt.Errorf("获取直播间失败: %w", err)
	}

	status, err := s.liveClient.GetStreamStatus(room.StreamKey)
	if err != nil {
		return nil, fmt.Errorf("获取流状态失败: %w", err)
	}

	return status, nil
}

// RegenerateStreamKey 重新生成流密钥
func (s *LiveService) RegenerateStreamKey(roomID uint) (string, error) {
	room, err := s.roomRepo.GetByID(roomID)
	if err != nil {
		return "", fmt.Errorf("获取直播间失败: %w", err)
	}

	// 生成新的流密钥
	newStreamKey := tencent.GenerateStreamKey(room.UserID, room.ID)
	room.StreamKey = newStreamKey
	room.UpdatedAt = time.Now()

	// 重新生成流地址
	if err := s.generateStreamURLs(room); err != nil {
		return "", fmt.Errorf("生成流地址失败: %w", err)
	}

	if err := s.roomRepo.Update(room); err != nil {
		return "", fmt.Errorf("更新流密钥失败: %w", err)
	}

	return newStreamKey, nil
}

// generateStreamURLs 生成推流和播放地址
func (s *LiveService) generateStreamURLs(room *model.Room) error {
	streamInfo, err := s.liveClient.GenerateStreamInfo(room.StreamKey)
	if err != nil {
		return fmt.Errorf("生成流信息失败: %w", err)
	}

	room.PushURL = streamInfo.PushURL
	room.WebRTCPushURL = streamInfo.WebRTCPushURL
	room.PlayURL = streamInfo.PlayURL
	room.HLSPlayURL = streamInfo.HLSPlayURL
	room.FLVPlayURL = streamInfo.FLVPlayURL
	room.WebRTCPlayURL = streamInfo.WebRTCPlayURL

	return nil
}

// GetRoomList 获取直播间列表
func (s *LiveService) GetRoomList(page, pageSize int, categoryID uint, keyword string, status int) ([]*model.Room, int64, error) {
	rooms, total, err := s.roomRepo.GetList(page, pageSize, categoryID, keyword, status)
	if err != nil {
		return nil, 0, fmt.Errorf("获取直播间列表失败: %w", err)
	}

	// 为每个直播间生成流地址
	for _, room := range rooms {
		if room.PushURL == "" {
			if err := s.generateStreamURLs(room); err != nil {
				fmt.Printf("为直播间 %d 生成流地址失败: %v\n", room.ID, err)
			}
		}
	}

	return rooms, total, nil
}

// DeleteRoom 删除直播间
func (s *LiveService) DeleteRoom(roomID uint) error {
	room, err := s.roomRepo.GetByID(roomID)
	if err != nil {
		return fmt.Errorf("获取直播间失败: %w", err)
	}

	// 如果正在直播，先停止直播
	if room.Status == 1 {
		if err := s.StopLive(roomID); err != nil {
			return fmt.Errorf("停止直播失败: %w", err)
		}
	}

	if err := s.roomRepo.Delete(roomID); err != nil {
		return fmt.Errorf("删除直播间失败: %w", err)
	}

	return nil
}
