// 用户状态管理

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserProfile, LoginRequest, RegisterRequest } from '@/types/user'
import { authApi, userApi } from '@/api/user'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const user = ref<UserProfile | null>(null)
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isStreamer = computed(() => user.value?.role === 'streamer' || user.value?.role === 'admin')

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      loading.value = true
      const response = await authApi.login(loginData)
      
      if (response.data) {
        token.value = response.data.token
        user.value = response.data.user
        
        // 保存到localStorage
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.user))
        
        ElMessage.success('登录成功')
        return response.data
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest) => {
    try {
      loading.value = true
      const response = await authApi.register(registerData)
      
      if (response.data) {
        token.value = response.data.token
        user.value = response.data.user
        
        // 保存到localStorage
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.user))
        
        ElMessage.success('注册成功')
        return response.data
      }
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    token.value = ''
    user.value = null
    
    // 清除localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    ElMessage.success('已退出登录')
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await authApi.refreshToken()
      
      if (response.data) {
        token.value = response.data.token
        user.value = response.data.user
        
        // 更新localStorage
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.user))
        
        return response.data
      }
    } catch (error) {
      console.error('刷新token失败:', error)
      logout()
      throw error
    }
  }

  // 获取用户资料
  const fetchProfile = async () => {
    try {
      loading.value = true
      const response = await userApi.getProfile()
      
      if (response.data) {
        user.value = response.data
        
        // 更新localStorage
        localStorage.setItem('user', JSON.stringify(response.data))
        
        return response.data
      }
    } catch (error) {
      console.error('获取用户资料失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新用户资料
  const updateProfile = async (profileData: any) => {
    try {
      loading.value = true
      await userApi.updateProfile(profileData)
      
      // 重新获取用户资料
      await fetchProfile()
      
      ElMessage.success('资料更新成功')
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData: any) => {
    try {
      loading.value = true
      await userApi.changePassword(passwordData)
      
      ElMessage.success('密码修改成功')
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 初始化用户状态（从localStorage恢复）
  const initializeAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('恢复用户状态失败:', error)
        logout()
      }
    }
  }

  // 检查token是否过期
  const checkTokenExpiry = () => {
    if (!token.value) return false
    
    try {
      // 解析JWT token获取过期时间
      const payload = JSON.parse(atob(token.value.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      
      if (payload.exp && payload.exp < currentTime) {
        logout()
        return false
      }
      
      return true
    } catch (error) {
      console.error('检查token过期时间失败:', error)
      logout()
      return false
    }
  }

  return {
    // 状态
    token,
    user,
    loading,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    isStreamer,
    
    // 方法
    login,
    register,
    logout,
    refreshToken,
    fetchProfile,
    updateProfile,
    changePassword,
    initializeAuth,
    checkTokenExpiry
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['token', 'user']
  }
})
