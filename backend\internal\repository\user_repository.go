package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"live-streaming-platform/internal/model"

	"gorm.io/gorm"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, id uint) (*model.User, error)
	GetByUsername(ctx context.Context, username string) (*model.User, error)
	GetByEmail(ctx context.Context, email string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id uint) error
	List(ctx context.Context, offset, limit int) ([]*model.User, int64, error)
	Search(ctx context.Context, keyword string, offset, limit int) ([]*model.User, int64, error)
	UpdateLastLogin(ctx context.Context, userID uint, ip string) error
	GetProfile(ctx context.Context, userID uint) (*model.UserProfile, error)
	GetStats(ctx context.Context, userID uint) (*model.UserStats, error)
	Follow(ctx context.Context, followerID, followedID uint) error
	Unfollow(ctx context.Context, followerID, followedID uint) error
	IsFollowing(ctx context.Context, followerID, followedID uint) (bool, error)
	GetFollowers(ctx context.Context, userID uint, offset, limit int) ([]*model.UserProfile, int64, error)
	GetFollowing(ctx context.Context, userID uint, offset, limit int) ([]*model.UserProfile, int64, error)
}

// userRepository 用户仓储实现
type userRepository struct {
	db *Database
}

// NewUserRepository 创建用户仓储
func NewUserRepository(db *Database) UserRepository {
	return &userRepository{db: db}
}

// Create 创建用户
func (r *userRepository) Create(ctx context.Context, user *model.User) error {
	if err := r.db.DB.WithContext(ctx).Create(user).Error; err != nil {
		return fmt.Errorf("创建用户失败: %w", err)
	}
	
	// 清除缓存
	r.clearUserCache(user.ID)
	return nil
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(ctx context.Context, id uint) (*model.User, error) {
	// 先从缓存获取
	if user := r.getUserFromCache(id); user != nil {
		return user, nil
	}

	var user model.User
	if err := r.db.DB.WithContext(ctx).First(&user, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}

	// 存入缓存
	r.setUserCache(&user)
	return &user, nil
}

// GetByUsername 根据用户名获取用户
func (r *userRepository) GetByUsername(ctx context.Context, username string) (*model.User, error) {
	var user model.User
	if err := r.db.DB.WithContext(ctx).Where("username = ?", username).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *userRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	if err := r.db.DB.WithContext(ctx).Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("获取用户失败: %w", err)
	}
	return &user, nil
}

// Update 更新用户
func (r *userRepository) Update(ctx context.Context, user *model.User) error {
	if err := r.db.DB.WithContext(ctx).Save(user).Error; err != nil {
		return fmt.Errorf("更新用户失败: %w", err)
	}
	
	// 清除缓存
	r.clearUserCache(user.ID)
	return nil
}

// Delete 删除用户
func (r *userRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.DB.WithContext(ctx).Delete(&model.User{}, id).Error; err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}
	
	// 清除缓存
	r.clearUserCache(id)
	return nil
}

// List 获取用户列表
func (r *userRepository) List(ctx context.Context, offset, limit int) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64

	// 获取总数
	if err := r.db.DB.WithContext(ctx).Model(&model.User{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 获取用户列表
	if err := r.db.DB.WithContext(ctx).Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("获取用户列表失败: %w", err)
	}

	return users, total, nil
}

// Search 搜索用户
func (r *userRepository) Search(ctx context.Context, keyword string, offset, limit int) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64

	query := r.db.DB.WithContext(ctx).Model(&model.User{}).Where(
		"username LIKE ? OR nickname LIKE ? OR email LIKE ?",
		"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%",
	)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("搜索用户总数失败: %w", err)
	}

	// 获取用户列表
	if err := query.Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("搜索用户失败: %w", err)
	}

	return users, total, nil
}

// UpdateLastLogin 更新最后登录信息
func (r *userRepository) UpdateLastLogin(ctx context.Context, userID uint, ip string) error {
	now := time.Now()
	if err := r.db.DB.WithContext(ctx).Model(&model.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"last_login_at": now,
		"last_login_ip": ip,
	}).Error; err != nil {
		return fmt.Errorf("更新最后登录信息失败: %w", err)
	}
	
	// 清除缓存
	r.clearUserCache(userID)
	return nil
}

// GetProfile 获取用户公开信息
func (r *userRepository) GetProfile(ctx context.Context, userID uint) (*model.UserProfile, error) {
	user, err := r.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, nil
	}
	return user.ToProfile(), nil
}

// GetStats 获取用户统计信息
func (r *userRepository) GetStats(ctx context.Context, userID uint) (*model.UserStats, error) {
	// 这里可以实现复杂的统计查询
	// 暂时返回基本统计信息
	stats := &model.UserStats{
		UserID: userID,
	}
	
	// 可以添加更多统计查询
	// 例如：总直播次数、总观看时长等
	
	return stats, nil
}

// Follow 关注用户
func (r *userRepository) Follow(ctx context.Context, followerID, followedID uint) error {
	if followerID == followedID {
		return fmt.Errorf("不能关注自己")
	}

	// 检查是否已经关注
	var count int64
	if err := r.db.DB.WithContext(ctx).Model(&model.Follow{}).Where(
		"follower_id = ? AND followed_id = ?", followerID, followedID,
	).Count(&count).Error; err != nil {
		return fmt.Errorf("检查关注状态失败: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("已经关注过该用户")
	}

	// 开始事务
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 创建关注记录
		follow := &model.Follow{
			FollowerID: followerID,
			FollowedID: followedID,
		}
		if err := tx.WithContext(ctx).Create(follow).Error; err != nil {
			return fmt.Errorf("创建关注记录失败: %w", err)
		}

		// 更新关注者的关注数
		if err := tx.WithContext(ctx).Model(&model.User{}).Where("id = ?", followerID).
			UpdateColumn("follow_count", gorm.Expr("follow_count + 1")).Error; err != nil {
			return fmt.Errorf("更新关注数失败: %w", err)
		}

		// 更新被关注者的粉丝数
		if err := tx.WithContext(ctx).Model(&model.User{}).Where("id = ?", followedID).
			UpdateColumn("follower_count", gorm.Expr("follower_count + 1")).Error; err != nil {
			return fmt.Errorf("更新粉丝数失败: %w", err)
		}

		return nil
	})
}

// Unfollow 取消关注
func (r *userRepository) Unfollow(ctx context.Context, followerID, followedID uint) error {
	// 开始事务
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除关注记录
		if err := tx.WithContext(ctx).Where(
			"follower_id = ? AND followed_id = ?", followerID, followedID,
		).Delete(&model.Follow{}).Error; err != nil {
			return fmt.Errorf("删除关注记录失败: %w", err)
		}

		// 更新关注者的关注数
		if err := tx.WithContext(ctx).Model(&model.User{}).Where("id = ?", followerID).
			UpdateColumn("follow_count", gorm.Expr("follow_count - 1")).Error; err != nil {
			return fmt.Errorf("更新关注数失败: %w", err)
		}

		// 更新被关注者的粉丝数
		if err := tx.WithContext(ctx).Model(&model.User{}).Where("id = ?", followedID).
			UpdateColumn("follower_count", gorm.Expr("follower_count - 1")).Error; err != nil {
			return fmt.Errorf("更新粉丝数失败: %w", err)
		}

		return nil
	})
}

// IsFollowing 检查是否关注
func (r *userRepository) IsFollowing(ctx context.Context, followerID, followedID uint) (bool, error) {
	var count int64
	if err := r.db.DB.WithContext(ctx).Model(&model.Follow{}).Where(
		"follower_id = ? AND followed_id = ?", followerID, followedID,
	).Count(&count).Error; err != nil {
		return false, fmt.Errorf("检查关注状态失败: %w", err)
	}
	return count > 0, nil
}

// GetFollowers 获取粉丝列表
func (r *userRepository) GetFollowers(ctx context.Context, userID uint, offset, limit int) ([]*model.UserProfile, int64, error) {
	var follows []model.Follow
	var total int64

	// 获取总数
	if err := r.db.DB.WithContext(ctx).Model(&model.Follow{}).Where("followed_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取粉丝总数失败: %w", err)
	}

	// 获取关注记录
	if err := r.db.DB.WithContext(ctx).Where("followed_id = ?", userID).
		Preload("Follower").Offset(offset).Limit(limit).Find(&follows).Error; err != nil {
		return nil, 0, fmt.Errorf("获取粉丝列表失败: %w", err)
	}

	// 转换为用户信息
	profiles := make([]*model.UserProfile, len(follows))
	for i, follow := range follows {
		if follow.Follower != nil {
			profiles[i] = follow.Follower.ToProfile()
		}
	}

	return profiles, total, nil
}

// GetFollowing 获取关注列表
func (r *userRepository) GetFollowing(ctx context.Context, userID uint, offset, limit int) ([]*model.UserProfile, int64, error) {
	var follows []model.Follow
	var total int64

	// 获取总数
	if err := r.db.DB.WithContext(ctx).Model(&model.Follow{}).Where("follower_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取关注总数失败: %w", err)
	}

	// 获取关注记录
	if err := r.db.DB.WithContext(ctx).Where("follower_id = ?", userID).
		Preload("Followed").Offset(offset).Limit(limit).Find(&follows).Error; err != nil {
		return nil, 0, fmt.Errorf("获取关注列表失败: %w", err)
	}

	// 转换为用户信息
	profiles := make([]*model.UserProfile, len(follows))
	for i, follow := range follows {
		if follow.Followed != nil {
			profiles[i] = follow.Followed.ToProfile()
		}
	}

	return profiles, total, nil
}

// 缓存相关方法
func (r *userRepository) getUserFromCache(userID uint) *model.User {
	key := fmt.Sprintf("user:%d", userID)
	val, err := r.db.Redis.Get(context.Background(), key).Result()
	if err != nil {
		return nil
	}

	var user model.User
	if err := json.Unmarshal([]byte(val), &user); err != nil {
		return nil
	}

	return &user
}

func (r *userRepository) setUserCache(user *model.User) {
	key := fmt.Sprintf("user:%d", user.ID)
	data, err := json.Marshal(user)
	if err != nil {
		return
	}

	r.db.Redis.Set(context.Background(), key, data, time.Hour)
}

func (r *userRepository) clearUserCache(userID uint) {
	key := fmt.Sprintf("user:%d", userID)
	r.db.Redis.Del(context.Background(), key)
}
