{"client_ip":"::1","error":"","latency":114370100,"level":"info","method":"GET","msg":"HTTP Request","path":"/health","request_id":"","status":200,"time":"2025-06-13T19:53:32+08:00","timestamp":"2025-06-13T19:53:32+08:00","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.1682"}
{"client_ip":"::1","error":"","latency":2187400,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/auth/register","request_id":"","status":204,"time":"2025-06-13T20:04:35+08:00","timestamp":"2025-06-13T20:04:35+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":728200,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/auth/register","request_id":"","status":204,"time":"2025-06-13T20:07:53+08:00","timestamp":"2025-06-13T20:07:53+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":281428400,"level":"info","method":"GET","msg":"HTTP Request","path":"/health","request_id":"","status":200,"time":"2025-06-13T20:09:45+08:00","timestamp":"2025-06-13T20:09:45+08:00","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.1682"}
{"client_ip":"::1","error":"","latency":856509500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/auth/register","request_id":"","status":200,"time":"2025-06-13T20:09:58+08:00","timestamp":"2025-06-13T20:09:58+08:00","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.1682"}
{"client_ip":"::1","error":"","latency":807693500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/auth/register","request_id":"","status":200,"time":"2025-06-13T20:15:16+08:00","timestamp":"2025-06-13T20:15:16+08:00","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.1682"}
{"client_ip":"::1","error":"","latency":505400,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/auth/register","request_id":"","status":204,"time":"2025-06-13T20:16:18+08:00","timestamp":"2025-06-13T20:16:18+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/auth/register","request_id":"","status":204,"time":"2025-06-13T20:16:59+08:00","timestamp":"2025-06-13T20:16:59+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":118649700,"level":"info","method":"GET","msg":"HTTP Request","path":"/health","request_id":"","status":200,"time":"2025-06-13T20:26:43+08:00","timestamp":"2025-06-13T20:26:43+08:00","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.1682"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/auth/register","request_id":"","status":204,"time":"2025-06-13T20:28:35+08:00","timestamp":"2025-06-13T20:28:35+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":872534400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/auth/register","request_id":"1749817715333-ls5xeoh7d","status":200,"time":"2025-06-13T20:28:36+08:00","timestamp":"2025-06-13T20:28:36+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/auth/login","request_id":"","status":204,"time":"2025-06-13T20:44:55+08:00","timestamp":"2025-06-13T20:44:55+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":933776300,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/auth/login","request_id":"1749818695464-hwa9cr78o","status":200,"time":"2025-06-13T20:44:56+08:00","timestamp":"2025-06-13T20:44:56+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:05:30+08:00","timestamp":"2025-06-13T22:05:30+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749823530000-inbffcbim","status":404,"time":"2025-06-13T22:05:30+08:00","timestamp":"2025-06-13T22:05:30+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":1528500,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:06:05+08:00","timestamp":"2025-06-13T22:06:05+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749823559390-ijri9zn4m","status":404,"time":"2025-06-13T22:06:05+08:00","timestamp":"2025-06-13T22:06:05+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:06:05+08:00","timestamp":"2025-06-13T22:06:05+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749823565346-v3qm4idcr","status":404,"time":"2025-06-13T22:06:05+08:00","timestamp":"2025-06-13T22:06:05+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:06:22+08:00","timestamp":"2025-06-13T22:06:22+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749823582322-kpk0czhnu","status":404,"time":"2025-06-13T22:06:22+08:00","timestamp":"2025-06-13T22:06:22+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:06:22+08:00","timestamp":"2025-06-13T22:06:22+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749823582614-0n7ckrgz5","status":404,"time":"2025-06-13T22:06:22+08:00","timestamp":"2025-06-13T22:06:22+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:06:54+08:00","timestamp":"2025-06-13T22:06:54+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749823614205-tnpx95vih","status":404,"time":"2025-06-13T22:06:54+08:00","timestamp":"2025-06-13T22:06:54+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:06:54+08:00","timestamp":"2025-06-13T22:06:54+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749823614260-4919vv8x9","status":404,"time":"2025-06-13T22:06:54+08:00","timestamp":"2025-06-13T22:06:54+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:34:56+08:00","timestamp":"2025-06-13T22:34:56+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":122772200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749825296120-gcn3uddkd","status":200,"time":"2025-06-13T22:34:56+08:00","timestamp":"2025-06-13T22:34:56+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:34:56+08:00","timestamp":"2025-06-13T22:34:56+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":122568400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749825296307-m52agdm5w","status":404,"time":"2025-06-13T22:34:56+08:00","timestamp":"2025-06-13T22:34:56+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms","request_id":"","status":204,"time":"2025-06-13T22:35:14+08:00","timestamp":"2025-06-13T22:35:14+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":626005200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/rooms","request_id":"1749825314395-n3ggmhv10","status":200,"time":"2025-06-13T22:35:15+08:00","timestamp":"2025-06-13T22:35:15+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:35:38+08:00","timestamp":"2025-06-13T22:35:38+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":123317000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749825338041-kg1h0u60g","status":200,"time":"2025-06-13T22:35:38+08:00","timestamp":"2025-06-13T22:35:38+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:35:38+08:00","timestamp":"2025-06-13T22:35:38+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":370155800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749825338320-s9xa4jkzk","status":200,"time":"2025-06-13T22:35:38+08:00","timestamp":"2025-06-13T22:35:38+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:36:46+08:00","timestamp":"2025-06-13T22:36:46+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":124982800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749825406724-sp8i0yf02","status":200,"time":"2025-06-13T22:36:46+08:00","timestamp":"2025-06-13T22:36:46+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":996700,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:36:46+08:00","timestamp":"2025-06-13T22:36:46+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":393910900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749825406911-81dm1esnt","status":200,"time":"2025-06-13T22:36:47+08:00","timestamp":"2025-06-13T22:36:47+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:36:52+08:00","timestamp":"2025-06-13T22:36:52+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":124209800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749825412101-zm0krk3sa","status":200,"time":"2025-06-13T22:36:52+08:00","timestamp":"2025-06-13T22:36:52+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:36:52+08:00","timestamp":"2025-06-13T22:36:52+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":368950300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749825412280-giz3twrsd","status":200,"time":"2025-06-13T22:36:52+08:00","timestamp":"2025-06-13T22:36:52+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/1/start","request_id":"","status":204,"time":"2025-06-13T22:36:56+08:00","timestamp":"2025-06-13T22:36:56+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":1070055100,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/rooms/1/start","request_id":"1749825416115-ctm2inlgx","status":200,"time":"2025-06-13T22:36:57+08:00","timestamp":"2025-06-13T22:36:57+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:36:57+08:00","timestamp":"2025-06-13T22:36:57+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":380410500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749825417418-8nq4o8vpg","status":200,"time":"2025-06-13T22:36:57+08:00","timestamp":"2025-06-13T22:36:57+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/1/stop","request_id":"","status":204,"time":"2025-06-13T22:37:04+08:00","timestamp":"2025-06-13T22:37:04+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":872120600,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/rooms/1/stop","request_id":"1749825424906-o47amxu3k","status":200,"time":"2025-06-13T22:37:05+08:00","timestamp":"2025-06-13T22:37:05+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:37:05+08:00","timestamp":"2025-06-13T22:37:05+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":367927600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749825425811-askrriqfm","status":200,"time":"2025-06-13T22:37:06+08:00","timestamp":"2025-06-13T22:37:06+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:37:11+08:00","timestamp":"2025-06-13T22:37:11+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":122915900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749825431468-735dxcsco","status":200,"time":"2025-06-13T22:37:11+08:00","timestamp":"2025-06-13T22:37:11+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:37:11+08:00","timestamp":"2025-06-13T22:37:11+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":366407500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749825431685-cjp7w77in","status":200,"time":"2025-06-13T22:37:12+08:00","timestamp":"2025-06-13T22:37:12+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":2996300,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:48:38+08:00","timestamp":"2025-06-13T22:48:38+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":138484500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749826118243-izgieylkc","status":200,"time":"2025-06-13T22:48:38+08:00","timestamp":"2025-06-13T22:48:38+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:48:38+08:00","timestamp":"2025-06-13T22:48:38+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":367399800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826118494-kwsctuugx","status":200,"time":"2025-06-13T22:48:38+08:00","timestamp":"2025-06-13T22:48:38+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":124546700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749826121919-xt4rhtddf","status":200,"time":"2025-06-13T22:48:42+08:00","timestamp":"2025-06-13T22:48:42+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":371888500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826122091-89q29jjv3","status":200,"time":"2025-06-13T22:48:42+08:00","timestamp":"2025-06-13T22:48:42+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:49:45+08:00","timestamp":"2025-06-13T22:49:45+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":132558500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749826185660-8h2vc48pf","status":200,"time":"2025-06-13T22:49:46+08:00","timestamp":"2025-06-13T22:49:46+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:49:46+08:00","timestamp":"2025-06-13T22:49:46+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":367949100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826186126-yt4zqirap","status":200,"time":"2025-06-13T22:49:46+08:00","timestamp":"2025-06-13T22:49:46+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:51:45+08:00","timestamp":"2025-06-13T22:51:45+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":137938900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749826305516-qrjoqhgz3","status":200,"time":"2025-06-13T22:51:45+08:00","timestamp":"2025-06-13T22:51:45+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:51:45+08:00","timestamp":"2025-06-13T22:51:45+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":381448300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826305816-gvhlmrigs","status":200,"time":"2025-06-13T22:51:46+08:00","timestamp":"2025-06-13T22:51:46+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:52:04+08:00","timestamp":"2025-06-13T22:52:04+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":126312400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749826324179-asy81lol7","status":200,"time":"2025-06-13T22:52:04+08:00","timestamp":"2025-06-13T22:52:04+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:52:04+08:00","timestamp":"2025-06-13T22:52:04+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":365999100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826324530-ie2k42w51","status":200,"time":"2025-06-13T22:52:04+08:00","timestamp":"2025-06-13T22:52:04+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:53:44+08:00","timestamp":"2025-06-13T22:53:44+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":126085800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749826424668-jnwasghay","status":200,"time":"2025-06-13T22:53:44+08:00","timestamp":"2025-06-13T22:53:44+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:53:44+08:00","timestamp":"2025-06-13T22:53:44+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":369641300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826424962-dac8mr2eg","status":200,"time":"2025-06-13T22:53:45+08:00","timestamp":"2025-06-13T22:53:45+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":997000,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:53:50+08:00","timestamp":"2025-06-13T22:53:50+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":126145800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749826430780-6kwutwney","status":200,"time":"2025-06-13T22:53:51+08:00","timestamp":"2025-06-13T22:53:51+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:53:51+08:00","timestamp":"2025-06-13T22:53:51+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":*********,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826431012-7w6tfnrif","status":200,"time":"2025-06-13T22:53:51+08:00","timestamp":"2025-06-13T22:53:51+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/categories","request_id":"","status":204,"time":"2025-06-13T22:56:21+08:00","timestamp":"2025-06-13T22:56:21+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":*********,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/categories","request_id":"1749826581039-otik9unju","status":200,"time":"2025-06-13T22:56:21+08:00","timestamp":"2025-06-13T22:56:21+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:56:21+08:00","timestamp":"2025-06-13T22:56:21+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":481925200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826581228-h9zgjxgi8","status":200,"time":"2025-06-13T22:56:21+08:00","timestamp":"2025-06-13T22:56:21+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/1/start","request_id":"","status":204,"time":"2025-06-13T22:58:19+08:00","timestamp":"2025-06-13T22:58:19+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":884258100,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/rooms/1/start","request_id":"1749826699759-nxrzrda0t","status":200,"time":"2025-06-13T22:58:20+08:00","timestamp":"2025-06-13T22:58:20+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T22:58:20+08:00","timestamp":"2025-06-13T22:58:20+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":371264100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826700677-8kvvoxoq1","status":200,"time":"2025-06-13T22:58:21+08:00","timestamp":"2025-06-13T22:58:21+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/1/stop","request_id":"","status":204,"time":"2025-06-13T23:00:56+08:00","timestamp":"2025-06-13T23:00:56+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":906585200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/rooms/1/stop","request_id":"1749826856470-hzz8r1ghm","status":200,"time":"2025-06-13T23:00:57+08:00","timestamp":"2025-06-13T23:00:57+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":0,"level":"info","method":"OPTIONS","msg":"HTTP Request","path":"/api/rooms/my","request_id":"","status":204,"time":"2025-06-13T23:00:57+08:00","timestamp":"2025-06-13T23:00:57+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"client_ip":"::1","error":"","latency":377419700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/rooms/my","request_id":"1749826857519-i9rq17kgr","status":200,"time":"2025-06-13T23:00:58+08:00","timestamp":"2025-06-13T23:00:58+08:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
