package repository

import (
	"fmt"

	"live-streaming-platform/internal/model"
	"gorm.io/gorm"
)

// CategoryRepository 分类仓库接口
type CategoryRepository interface {
	Create(category *model.Category) error
	Update(category *model.Category) error
	Delete(id uint) error
	GetByID(id uint) (*model.Category, error)
	GetByName(name string) (*model.Category, error)
	GetActiveCategories() ([]*model.Category, error)
	GetAllCategories() ([]*model.Category, error)
	GetRoomCountByCategory(categoryID uint) (int64, error)
}

// categoryRepository 分类仓库实现
type categoryRepository struct {
	db *gorm.DB
}

// NewCategoryRepository 创建分类仓库
func NewCategoryRepository(db *gorm.DB) CategoryRepository {
	return &categoryRepository{db: db}
}

// Create 创建分类
func (r *categoryRepository) Create(category *model.Category) error {
	if err := r.db.Create(category).Error; err != nil {
		return fmt.Errorf("创建分类失败: %w", err)
	}
	return nil
}

// Update 更新分类
func (r *categoryRepository) Update(category *model.Category) error {
	if err := r.db.Save(category).Error; err != nil {
		return fmt.Errorf("更新分类失败: %w", err)
	}
	return nil
}

// Delete 删除分类
func (r *categoryRepository) Delete(id uint) error {
	if err := r.db.Delete(&model.Category{}, id).Error; err != nil {
		return fmt.Errorf("删除分类失败: %w", err)
	}
	return nil
}

// GetByID 根据ID获取分类
func (r *categoryRepository) GetByID(id uint) (*model.Category, error) {
	var category model.Category
	if err := r.db.First(&category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("分类不存在")
		}
		return nil, fmt.Errorf("获取分类失败: %w", err)
	}
	return &category, nil
}

// GetByName 根据名称获取分类
func (r *categoryRepository) GetByName(name string) (*model.Category, error) {
	var category model.Category
	if err := r.db.Where("name = ?", name).First(&category).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("分类不存在")
		}
		return nil, fmt.Errorf("获取分类失败: %w", err)
	}
	return &category, nil
}

// GetActiveCategories 获取活跃的分类列表
func (r *categoryRepository) GetActiveCategories() ([]*model.Category, error) {
	var categories []*model.Category
	if err := r.db.Where("is_active = ?", true).Order("sort ASC, created_at ASC").Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("获取活跃分类列表失败: %w", err)
	}
	return categories, nil
}

// GetAllCategories 获取所有分类列表
func (r *categoryRepository) GetAllCategories() ([]*model.Category, error) {
	var categories []*model.Category
	if err := r.db.Order("sort ASC, created_at ASC").Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("获取分类列表失败: %w", err)
	}
	return categories, nil
}

// GetRoomCountByCategory 获取分类下的直播间数量
func (r *categoryRepository) GetRoomCountByCategory(categoryID uint) (int64, error) {
	var count int64
	if err := r.db.Model(&model.Room{}).Where("category_id = ?", categoryID).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("获取分类下直播间数量失败: %w", err)
	}
	return count, nil
}
