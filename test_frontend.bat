@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 腾讯云直播平台前端测试脚本

echo [INFO] 开始测试前端服务...

:: 进入前端目录
cd frontend

:: 检查Node.js环境
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装或未添加到 PATH
    echo [INFO] 请安装 Node.js 18+ 版本
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm 未安装或未添加到 PATH
    pause
    exit /b 1
)

echo [INFO] Node.js 环境检查通过

:: 检查配置文件
if not exist ".env" (
    if exist ".env.example" (
        echo [INFO] 复制 .env.example 到 .env
        copy ".env.example" ".env" >nul
    ) else (
        echo [ERROR] .env.example 文件不存在
        pause
        exit /b 1
    )
)

:: 安装依赖
echo [INFO] 安装前端依赖...
npm install
if errorlevel 1 (
    echo [ERROR] 安装依赖失败
    pause
    exit /b 1
)

echo [INFO] 依赖安装完成

echo [INFO] 启动前端开发服务器...
echo [INFO] 服务将在 http://localhost:5173 启动
echo [INFO] 按 Ctrl+C 停止服务
echo.

:: 启动开发服务器
npm run dev

pause
