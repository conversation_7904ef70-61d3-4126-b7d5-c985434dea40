// WebRTC 推流服务
import TRTC from 'trtc-sdk-v5'

export interface WebRTCConfig {
  sdkAppId: number
  userId: string
  userSig: string
  roomId: string
  streamId: string
  pushUrl: string
}

export interface StreamQuality {
  width: number
  height: number
  frameRate: number
  bitrate: number
}

export interface PushStats {
  bitrate: number
  frameRate: number
  resolution: string
  packetLoss: number
  rtt: number
  audioBitrate: number
  videoBitrate: number
}

export class WebRTCPusher {
  private trtc: any = null
  private isPublishing = false
  private config: WebRTCConfig | null = null
  private onStatsCallback?: (stats: PushStats) => void
  private onErrorCallback?: (error: any) => void
  private statsTimer?: NodeJS.Timeout

  constructor() {
    // 初始化 TRTC 实例
    this.trtc = TRTC.create()
    this.setupEventListeners()
  }

  private setupEventListeners() {
    // 监听远端用户进入房间
    this.trtc.on('remote-user-enter', (event: any) => {
      console.log('远端用户进入房间:', event)
    })

    // 监听远端用户离开房间
    this.trtc.on('remote-user-leave', (event: any) => {
      console.log('远端用户离开房间:', event)
    })

    // 监听网络质量
    this.trtc.on('network-quality', (event: any) => {
      console.log('网络质量:', event)
    })

    // 监听错误事件
    this.trtc.on('error', (error: any) => {
      console.error('TRTC 错误:', error)
      this.onErrorCallback?.(error)
    })

    // 监听连接状态变化
    this.trtc.on('connection-state-changed', (event: any) => {
      console.log('连接状态变化:', event)
    })
  }

  // 初始化推流器
  async init(config: WebRTCConfig): Promise<void> {
    try {
      this.config = config
      console.log('WebRTC 推流器初始化成功')
    } catch (error) {
      console.error('WebRTC 推流器初始化失败:', error)
      throw error
    }
  }

  // 获取媒体设备权限并创建本地流
  async createLocalStream(options: {
    video: boolean | StreamQuality
    audio: boolean
    screen?: boolean
  }): Promise<void> {
    try {
      // 如果是屏幕共享
      if (options.screen) {
        await this.trtc.startScreenShare()
      } else {
        // 启动摄像头
        if (options.video) {
          const videoConfig = typeof options.video === 'object' ? {
            width: options.video.width,
            height: options.video.height,
            frameRate: options.video.frameRate,
            bitrate: options.video.bitrate
          } : true

          await this.trtc.startLocalVideo({ option: videoConfig })
        }

        // 启动麦克风
        if (options.audio) {
          await this.trtc.startLocalAudio()
        }
      }

      console.log('本地流创建成功')
    } catch (error) {
      console.error('创建本地流失败:', error)
      throw error
    }
  }

  // 开始推流
  async startPush(): Promise<void> {
    if (!this.config) {
      throw new Error('推流器未初始化')
    }

    try {
      // 进入房间
      await this.trtc.enterRoom({
        roomId: parseInt(this.config.roomId),
        sdkAppId: this.config.sdkAppId,
        userId: this.config.userId,
        userSig: this.config.userSig,
        role: 'anchor' // 主播角色
      })

      this.isPublishing = true

      // 开始统计监控
      this.startStatsMonitoring()

      console.log('推流开始成功')
    } catch (error) {
      console.error('开始推流失败:', error)
      throw error
    }
  }

  // 停止推流
  async stopPush(): Promise<void> {
    try {
      if (this.isPublishing) {
        // 停止本地音视频
        await this.trtc.stopLocalVideo()
        await this.trtc.stopLocalAudio()

        // 退出房间
        await this.trtc.exitRoom()
        this.isPublishing = false
      }

      this.stopStatsMonitoring()
      console.log('推流停止成功')
    } catch (error) {
      console.error('停止推流失败:', error)
      throw error
    }
  }

  // 切换摄像头
  async switchCamera(): Promise<void> {
    try {
      const cameras = await TRTC.getCameraList()
      if (cameras.length > 1) {
        // 简单的切换逻辑，实际应用中可以提供设备选择
        await this.trtc.updateLocalVideo({ cameraId: cameras[1].deviceId })
      }
    } catch (error) {
      console.error('切换摄像头失败:', error)
      throw error
    }
  }

  // 切换麦克风
  async switchMicrophone(): Promise<void> {
    try {
      const microphones = await TRTC.getMicrophoneList()
      if (microphones.length > 1) {
        await this.trtc.updateLocalAudio({ microphoneId: microphones[1].deviceId })
      }
    } catch (error) {
      console.error('切换麦克风失败:', error)
      throw error
    }
  }

  // 静音/取消静音
  async muteAudio(mute: boolean): Promise<void> {
    try {
      if (mute) {
        await this.trtc.stopLocalAudio()
      } else {
        await this.trtc.startLocalAudio()
      }
    } catch (error) {
      console.error('音频控制失败:', error)
      throw error
    }
  }

  // 关闭/开启视频
  async muteVideo(mute: boolean): Promise<void> {
    try {
      if (mute) {
        await this.trtc.stopLocalVideo()
      } else {
        await this.trtc.startLocalVideo()
      }
    } catch (error) {
      console.error('视频控制失败:', error)
      throw error
    }
  }

  // 播放本地流到指定元素
  async playLocalStream(elementId: string): Promise<void> {
    try {
      const element = document.getElementById(elementId)
      if (element) {
        await this.trtc.startLocalVideo({ view: element })
      }
    } catch (error) {
      console.error('播放本地流失败:', error)
      throw error
    }
  }

  // 停止播放本地流
  async stopLocalStream(): Promise<void> {
    try {
      await this.trtc.stopLocalVideo()
    } catch (error) {
      console.error('停止本地流失败:', error)
      throw error
    }
  }

  // 开始统计监控
  private startStatsMonitoring(): void {
    this.statsTimer = setInterval(async () => {
      if (this.trtc && this.isPublishing) {
        try {
          const stats = await this.trtc.getLocalVideoStats()
          const audioStats = await this.trtc.getLocalAudioStats()

          const pushStats: PushStats = {
            bitrate: (stats.bytesSent || 0) / 1000, // 转换为 kbps
            frameRate: stats.frameRate || 0,
            resolution: `${stats.frameWidth || 0}x${stats.frameHeight || 0}`,
            packetLoss: stats.packetsLost || 0,
            rtt: stats.rtt || 0,
            audioBitrate: (audioStats.bytesSent || 0) / 1000,
            videoBitrate: (stats.bytesSent || 0) / 1000
          }

          this.onStatsCallback?.(pushStats)
        } catch (error) {
          console.warn('获取推流统计失败:', error)
        }
      }
    }, 2000) // 每2秒更新一次统计
  }

  // 停止统计监控
  private stopStatsMonitoring(): void {
    if (this.statsTimer) {
      clearInterval(this.statsTimer)
      this.statsTimer = undefined
    }
  }

  // 设置统计回调
  onStats(callback: (stats: PushStats) => void): void {
    this.onStatsCallback = callback
  }

  // 设置错误回调
  onError(callback: (error: any) => void): void {
    this.onErrorCallback = callback
  }

  // 检查浏览器兼容性
  static checkBrowserSupport(): boolean {
    return TRTC.isSupported()
  }

  // 获取设备列表
  static async getDevices(): Promise<{
    cameras: MediaDeviceInfo[]
    microphones: MediaDeviceInfo[]
    speakers: MediaDeviceInfo[]
  }> {
    const cameras = await TRTC.getCameraList()
    const microphones = await TRTC.getMicrophoneList()
    const speakers = await TRTC.getSpeakerList()

    return {
      cameras,
      microphones,
      speakers
    }
  }

  // 销毁推流器
  async destroy(): Promise<void> {
    try {
      await this.stopPush()

      if (this.trtc) {
        await this.trtc.destroy()
        this.trtc = null
      }

      this.config = null
      this.isPublishing = false
    } catch (error) {
      console.error('销毁推流器失败:', error)
    }
  }

  // 获取推流状态
  isActive(): boolean {
    return this.isPublishing
  }
}
