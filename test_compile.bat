@echo off
chcp 65001 >nul

echo [INFO] 测试编译腾讯云直播平台...

cd backend

echo [INFO] 检查 Go 环境...
go version
if errorlevel 1 (
    echo [ERROR] Go 未安装
    pause
    exit /b 1
)

echo [INFO] 检查配置文件...
if not exist "config\.env" (
    echo [INFO] 复制配置文件...
    copy "config\.env.example" "config\.env"
)

echo [INFO] 创建 bin 目录...
if not exist "bin" mkdir bin

echo [INFO] 下载依赖...
go mod download
if errorlevel 1 (
    echo [ERROR] 下载依赖失败
    pause
    exit /b 1
)

echo [INFO] 编译项目...
go build -o bin\server.exe cmd\server\main.go
if errorlevel 1 (
    echo [ERROR] 编译失败
    pause
    exit /b 1
)

echo [INFO] 编译成功！
echo [INFO] 可执行文件位置: backend\bin\server.exe
echo [INFO] 要启动服务器，请运行: backend\bin\server.exe

pause
