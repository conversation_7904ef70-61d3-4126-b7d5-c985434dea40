<template>
  <div class="webrtc-pusher">
    <!-- 浏览器兼容性检查 -->
    <el-alert
      v-if="!browserSupported"
      title="浏览器不支持"
      type="error"
      :closable="false"
      show-icon
    >
      <template #default>
        您的浏览器不支持 WebRTC 推流功能。请使用 Chrome 90+、Firefox 88+、Safari 14+ 或 Edge 90+ 浏览器。
      </template>
    </el-alert>

    <!-- 设备权限检查 -->
    <el-alert
      v-if="!permissionsGranted && browserSupported"
      title="需要设备权限"
      type="warning"
      :closable="false"
      show-icon
    >
      <template #default>
        需要获取摄像头和麦克风权限才能开始推流。
        <el-button type="primary" size="small" @click="requestPermissions">
          获取权限
        </el-button>
      </template>
    </el-alert>

    <!-- 设备选择 -->
    <el-card v-if="permissionsGranted && !isPushing" class="device-config">
      <template #header>
        <span>设备配置</span>
      </template>

      <el-form :model="deviceConfig" label-width="80px">
        <el-form-item label="摄像头">
          <el-select v-model="deviceConfig.cameraId" placeholder="选择摄像头">
            <el-option
              v-for="camera in devices.cameras"
              :key="camera.deviceId"
              :label="camera.label"
              :value="camera.deviceId"
            />
          </el-select>
          <el-button @click="testCamera" style="margin-left: 8px;">测试</el-button>
        </el-form-item>

        <el-form-item label="麦克风">
          <el-select v-model="deviceConfig.microphoneId" placeholder="选择麦克风">
            <el-option
              v-for="mic in devices.microphones"
              :key="mic.deviceId"
              :label="mic.label"
              :value="mic.deviceId"
            />
          </el-select>
          <el-button @click="testMicrophone" style="margin-left: 8px;">测试</el-button>
        </el-form-item>

        <el-form-item label="画质">
          <el-select v-model="deviceConfig.quality" placeholder="选择画质">
            <el-option
              v-for="(profile, key) in qualityProfiles"
              :key="key"
              :label="profile.label"
              :value="key"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="推流方式">
          <el-radio-group v-model="deviceConfig.streamType">
            <el-radio value="camera">摄像头</el-radio>
            <el-radio value="screen">屏幕共享</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预览区域 -->
    <el-card v-if="permissionsGranted" class="preview-card">
      <template #header>
        <div class="preview-header">
          <span>{{ isPushing ? '直播预览' : '设备预览' }}</span>
          <div class="preview-controls">
            <el-button
              v-if="!isPushing"
              type="primary"
              :loading="starting"
              @click="startPush"
              :disabled="!canStartPush"
            >
              开始推流
            </el-button>
            <el-button
              v-else
              type="danger"
              :loading="stopping"
              @click="stopPush"
            >
              停止推流
            </el-button>
          </div>
        </div>
      </template>

      <div class="preview-container">
        <video
          ref="previewVideo"
          class="preview-video"
          autoplay
          muted
          playsinline
        />
        
        <!-- 推流控制 -->
        <div v-if="isPushing" class="stream-controls">
          <el-button-group>
            <el-button
              :type="audioMuted ? 'danger' : 'primary'"
              @click="toggleAudio"
            >
              <el-icon><Microphone /></el-icon>
              {{ audioMuted ? '开启麦克风' : '关闭麦克风' }}
            </el-button>
            <el-button
              :type="videoMuted ? 'danger' : 'primary'"
              @click="toggleVideo"
            >
              <el-icon><VideoCamera /></el-icon>
              {{ videoMuted ? '开启摄像头' : '关闭摄像头' }}
            </el-button>
            <el-button @click="switchCamera">
              <el-icon><RefreshRight /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 推流统计 -->
    <el-card v-if="isPushing" class="stats-card">
      <template #header>
        <span>推流统计</span>
      </template>

      <div class="stats-grid">
        <div class="stat-item">
          <span class="label">码率:</span>
          <span class="value">{{ stats.bitrate }} kbps</span>
        </div>
        <div class="stat-item">
          <span class="label">帧率:</span>
          <span class="value">{{ stats.frameRate }} fps</span>
        </div>
        <div class="stat-item">
          <span class="label">分辨率:</span>
          <span class="value">{{ stats.resolution }}</span>
        </div>
        <div class="stat-item">
          <span class="label">丢包率:</span>
          <span class="value">{{ stats.packetLoss }}%</span>
        </div>
        <div class="stat-item">
          <span class="label">延迟:</span>
          <span class="value">{{ stats.rtt }} ms</span>
        </div>
        <div class="stat-item">
          <span class="label">网络质量:</span>
          <el-tag :type="networkQualityType">{{ networkQualityText }}</el-tag>
        </div>
      </div>
    </el-card>

    <!-- 错误提示 -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      style="margin-top: 16px;"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Microphone,
  VideoCamera,
  RefreshRight
} from '@element-plus/icons-vue'
import { WebRTCPusher, type WebRTCConfig, type PushStats } from '@/services/webrtc-pusher'
import { DeviceManager, type DeviceInfo } from '@/services/device-manager'
import { generateUserSig } from '@/config/webrtc'

interface Props {
  config: WebRTCConfig
}

const props = defineProps<Props>()

// 响应式数据
const browserSupported = ref(false)
const permissionsGranted = ref(false)
const isPushing = ref(false)
const starting = ref(false)
const stopping = ref(false)
const audioMuted = ref(false)
const videoMuted = ref(false)
const error = ref('')

const previewVideo = ref<HTMLVideoElement>()
const pusher = ref<WebRTCPusher>()
const deviceManager = ref<DeviceManager>()

const devices = reactive({
  cameras: [] as DeviceInfo[],
  microphones: [] as DeviceInfo[],
  speakers: [] as DeviceInfo[]
})

const deviceConfig = reactive({
  cameraId: '',
  microphoneId: '',
  quality: '720p',
  streamType: 'camera' as 'camera' | 'screen'
})

const stats = reactive<PushStats>({
  bitrate: 0,
  frameRate: 0,
  resolution: '0x0',
  packetLoss: 0,
  rtt: 0,
  audioBitrate: 0,
  videoBitrate: 0
})

// 计算属性
const qualityProfiles = computed(() => {
  return deviceManager.value?.getVideoQualityProfiles() || {}
})

const canStartPush = computed(() => {
  return permissionsGranted.value && !isPushing.value && deviceConfig.cameraId
})

const networkQualityType = computed(() => {
  if (stats.packetLoss > 5) return 'danger'
  if (stats.packetLoss > 2) return 'warning'
  if (stats.rtt > 200) return 'warning'
  return 'success'
})

const networkQualityText = computed(() => {
  if (stats.packetLoss > 5 || stats.rtt > 300) return '差'
  if (stats.packetLoss > 2 || stats.rtt > 200) return '一般'
  if (stats.rtt < 50) return '优秀'
  return '良好'
})

// 方法
const init = async () => {
  try {
    // 检查浏览器支持
    browserSupported.value = WebRTCPusher.checkBrowserSupport()
    if (!browserSupported.value) {
      return
    }

    // 初始化设备管理器
    deviceManager.value = DeviceManager.getInstance()

    // 获取 UserSig
    const userSig = await generateUserSig(props.config.userId)
    const configWithUserSig = {
      ...props.config,
      userSig
    }

    // 初始化推流器
    pusher.value = new WebRTCPusher()
    await pusher.value.init(configWithUserSig)

    // 设置回调
    pusher.value.onStats((newStats) => {
      Object.assign(stats, newStats)
    })

    pusher.value.onError((err) => {
      error.value = err.message || '推流发生错误'
      ElMessage.error(error.value)
    })

    // 检查权限
    await checkPermissions()

  } catch (err: any) {
    error.value = err.message || '初始化失败'
    ElMessage.error(error.value)
  }
}

const checkPermissions = async () => {
  try {
    const permissions = await deviceManager.value!.checkPermissions()
    permissionsGranted.value = permissions.camera && permissions.microphone

    if (permissionsGranted.value) {
      await loadDevices()
    }
  } catch (err: any) {
    console.error('权限检查失败:', err)
  }
}

const requestPermissions = async () => {
  try {
    const permissions = await deviceManager.value!.requestPermissions()
    permissionsGranted.value = permissions.camera && permissions.microphone

    if (permissionsGranted.value) {
      await loadDevices()
      ElMessage.success('权限获取成功')
    } else {
      ElMessage.error('权限获取失败')
    }
  } catch (err: any) {
    error.value = err.message || '权限获取失败'
    ElMessage.error(error.value)
  }
}

const loadDevices = async () => {
  try {
    const deviceList = await deviceManager.value!.getDevices()
    Object.assign(devices, deviceList)

    // 设置默认设备
    if (devices.cameras.length > 0 && !deviceConfig.cameraId) {
      deviceConfig.cameraId = devices.cameras[0].deviceId
    }
    if (devices.microphones.length > 0 && !deviceConfig.microphoneId) {
      deviceConfig.microphoneId = devices.microphones[0].deviceId
    }
  } catch (err: any) {
    error.value = err.message || '获取设备列表失败'
    ElMessage.error(error.value)
  }
}

const testCamera = async () => {
  try {
    const stream = await deviceManager.value!.testCamera(deviceConfig.cameraId)
    if (previewVideo.value) {
      previewVideo.value.srcObject = stream
    }
    setTimeout(() => {
      stream.getTracks().forEach(track => track.stop())
    }, 3000)
    ElMessage.success('摄像头测试成功')
  } catch (err: any) {
    ElMessage.error('摄像头测试失败: ' + err.message)
  }
}

const testMicrophone = async () => {
  try {
    const stream = await deviceManager.value!.testMicrophone(deviceConfig.microphoneId)
    setTimeout(() => {
      stream.getTracks().forEach(track => track.stop())
    }, 3000)
    ElMessage.success('麦克风测试成功')
  } catch (err: any) {
    ElMessage.error('麦克风测试失败: ' + err.message)
  }
}

const startPush = async () => {
  if (!pusher.value) return

  try {
    starting.value = true
    error.value = ''

    // 创建本地流
    const quality = qualityProfiles.value[deviceConfig.quality]
    const streamOptions = {
      video: deviceConfig.streamType === 'camera' ? quality : false,
      audio: true,
      screen: deviceConfig.streamType === 'screen'
    }

    await pusher.value.createLocalStream(streamOptions)

    // 播放预览
    if (previewVideo.value) {
      pusher.value.playLocalStream(previewVideo.value.id || 'preview')
    }

    // 开始推流
    await pusher.value.startPush()
    isPushing.value = true

    ElMessage.success('推流开始成功')
  } catch (err: any) {
    error.value = err.message || '开始推流失败'
    ElMessage.error(error.value)
  } finally {
    starting.value = false
  }
}

const stopPush = async () => {
  if (!pusher.value) return

  try {
    stopping.value = true
    await pusher.value.stopPush()
    isPushing.value = false
    audioMuted.value = false
    videoMuted.value = false

    ElMessage.success('推流停止成功')
  } catch (err: any) {
    error.value = err.message || '停止推流失败'
    ElMessage.error(error.value)
  } finally {
    stopping.value = false
  }
}

const toggleAudio = async () => {
  if (pusher.value) {
    audioMuted.value = !audioMuted.value
    await pusher.value.muteAudio(audioMuted.value)
  }
}

const toggleVideo = async () => {
  if (pusher.value) {
    videoMuted.value = !videoMuted.value
    await pusher.value.muteVideo(videoMuted.value)
  }
}

const switchCamera = async () => {
  if (pusher.value) {
    try {
      await pusher.value.switchCamera()
      ElMessage.success('摄像头切换成功')
    } catch (err: any) {
      ElMessage.error('摄像头切换失败: ' + err.message)
    }
  }
}

// 生命周期
onMounted(() => {
  init()
})

onUnmounted(() => {
  if (pusher.value) {
    pusher.value.destroy()
  }
})
</script>

<style scoped>
.webrtc-pusher {
  max-width: 800px;
  margin: 0 auto;
}

.device-config,
.preview-card,
.stats-card {
  margin-bottom: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-container {
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.stream-controls {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stat-item .label {
  font-weight: 500;
  color: #606266;
}

.stat-item .value {
  font-weight: 600;
  color: #303133;
}
</style>
