!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).TRTCVideoDecoder=e()}(this,(function(){"use strict";function t(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function e(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function r(t){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=t.apply(r,n);function s(t){e(a,o,i,s,c,"next",t)}function c(t){e(a,o,i,s,c,"throw",t)}s(void 0)}))}}function n(t,e,r){return e=u(e),function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,l()?Reflect.construct(e,r||[],u(t).constructor):e.apply(t,r))}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function a(t,e,r){return e&&i(t.prototype,e),r&&i(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function s(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=y(t))||e){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){s=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function c(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u(t){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},u(t)}function f(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h(t,e)}function l(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(l=function(){return!!t})()}function d(){d=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof g?e:g,a=Object.create(i.prototype),s=new F(n||[]);return o(a,"_invoke",{value:S(t,r,s)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h="suspendedStart",p="suspendedYield",v="executing",m="completed",y={};function g(){}function w(){}function b(){}var E={};u(E,a,(function(){return this}));var k=Object.getPrototypeOf,_=k&&k(k(O([])));_&&_!==r&&n.call(_,a)&&(E=_);var P=b.prototype=g.prototype=Object.create(E);function T(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(o,i,a,s){var c=l(t[o],t,i);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,s)}))}s(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function S(e,r,n){var o=h;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=A(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var u=l(e,r,n);if("normal"===u.type){if(o=n.done?m:p,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=m,n.method="throw",n.arg=u.arg)}}}function A(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,A(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function F(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function O(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return w.prototype=b,o(P,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=u(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,u(t,c,"GeneratorFunction")),t.prototype=Object.create(P),t},e.awrap=function(t){return{__await:t}},T(C.prototype),u(C.prototype,s,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new C(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},T(P),u(P,c,"Generator"),u(P,a,(function(){return this})),u(P,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=O,F.prototype={constructor:F,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(x),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),x(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;x(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:O(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function h(t,e){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},h(t,e)}function p(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}function m(t){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},m(t)}function y(e,r){if(e){if("string"==typeof e)return t(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}function g(t){var e="function"==typeof Map?new Map:void 0;return g=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch(e){return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,r)}function r(){return function(t,e,r){if(l())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,e);var o=new(t.bind.apply(t,n));return r&&h(o,r.prototype),o}(t,arguments,u(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),h(r,t)},g(t)}var w,b,E=Object.create,k=Object.defineProperty,_=Object.defineProperties,P=Object.getOwnPropertyDescriptor,T=Object.getOwnPropertyDescriptors,C=Object.getOwnPropertyNames,S=Object.getOwnPropertySymbols,A=Object.getPrototypeOf,D=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable,F=function(t,e,r){return e in t?k(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r},O=function(t,e){for(var r in e||(e={}))D.call(e,r)&&F(t,r,e[r]);if(S){var n,o=s(S(e));try{for(o.s();!(n=o.n()).done;){r=n.value;x.call(e,r)&&F(t,r,e[r])}}catch(t){o.e(t)}finally{o.f()}}return t},j=function(t,e){return _(t,T(e))},M=function(t,e,r,n){for(var o,i=P(e,r),a=t.length-1;a>=0;a--)(o=t[a])&&(i=o(e,r,i)||i);return i&&k(e,r,i),i},$=function(t,e,r){return F(t,"symbol"!==m(e)?e+"":e,r)},R=(w={"../node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js":function(t,e){var r=Object.prototype.hasOwnProperty,n="~";function o(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function a(t,e,r,o,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,o||t,a),c=n?n+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],s]:t._events[c].push(s):(t._events[c]=s,t._eventsCount++),t}function s(t,e){0==--t._eventsCount?t._events=new o:delete t._events[e]}function c(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),c.prototype.eventNames=function(){var t,e,o=[];if(0===this._eventsCount)return o;for(e in t=this._events)r.call(t,e)&&o.push(n?e.slice(1):e);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=n?n+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,a=new Array(i);o<i;o++)a[o]=r[o].fn;return a},c.prototype.listenerCount=function(t){var e=n?n+t:t,r=this._events[e];return r?r.fn?1:r.length:0},c.prototype.emit=function(t,e,r,o,i,a){var s=n?n+t:t;if(!this._events[s])return!1;var c,u,f=this._events[s],l=arguments.length;if(f.fn){switch(f.once&&this.removeListener(t,f.fn,void 0,!0),l){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,e),!0;case 3:return f.fn.call(f.context,e,r),!0;case 4:return f.fn.call(f.context,e,r,o),!0;case 5:return f.fn.call(f.context,e,r,o,i),!0;case 6:return f.fn.call(f.context,e,r,o,i,a),!0}for(u=1,c=new Array(l-1);u<l;u++)c[u-1]=arguments[u];f.fn.apply(f.context,c)}else{var d,h=f.length;for(u=0;u<h;u++)switch(f[u].once&&this.removeListener(t,f[u].fn,void 0,!0),l){case 1:f[u].fn.call(f[u].context);break;case 2:f[u].fn.call(f[u].context,e);break;case 3:f[u].fn.call(f[u].context,e,r);break;case 4:f[u].fn.call(f[u].context,e,r,o);break;default:if(!c)for(d=1,c=new Array(l-1);d<l;d++)c[d-1]=arguments[d];f[u].fn.apply(f[u].context,c)}}return!0},c.prototype.on=function(t,e,r){return a(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return a(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,r,o){var i=n?n+t:t;if(!this._events[i])return this;if(!e)return s(this,i),this;var a=this._events[i];if(a.fn)a.fn!==e||o&&!a.once||r&&a.context!==r||s(this,i);else{for(var c=0,u=[],f=a.length;c<f;c++)(a[c].fn!==e||o&&!a[c].once||r&&a[c].context!==r)&&u.push(a[c]);u.length?this._events[i]=1===u.length?u[0]:u:s(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=n?n+t:t,this._events[e]&&s(this,e)):(this._events=new o,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=n,c.EventEmitter=c,void 0!==e&&(e.exports=c)}},function(){return b||(0,w[C(w)[0]])((b={exports:{}}).exports,b),b.exports}),L=function(t,e,r){return r=null!=t?E(A(t)):{},function(t,e,r,n){if(e&&"object"===m(e)||"function"==typeof e){var o,i=s(C(e));try{var a=function(){var i=o.value;D.call(t,i)||i===r||k(t,i,{get:function(){return e[i]},enumerable:!(n=P(e,i))||n.enumerable})};for(i.s();!(o=i.n()).done;)a()}catch(t){i.e(t)}finally{i.f()}}return t}(k(r,"default",{value:t,enumerable:!0}),t)}(R()),z=Symbol("instance"),N=Symbol("cacheResult"),I=function(){return a((function t(e,r,n){o(this,t),this.oldState=e,this.newState=r,this.action=n,this.aborted=!1}),[{key:"abort",value:function(t){this.aborted=!0,q.call(t,this.oldState,new Error("action '".concat(this.action,"' aborted")))}},{key:"toString",value:function(){return"".concat(this.action,"ing")}}])}(),B=function(t){function e(t,r,i){var a;return o(this,e),(a=n(this,e,[r])).state=t,a.message=r,a.cause=i,a}return f(e,t),a(e)}(g(Error));var U=new Map;function W(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(n,o,i){var a=r.action||o;if(!r.context){var s=U.get(n)||[];U.has(n)||U.set(n,s),s.push({from:t,to:e,action:a})}var c=i.value;i.value=function(){for(var n,o=this,i=this,s=arguments.length,u=new Array(s),f=0;f<s;f++)u[f]=arguments[f];r.context&&(i=X.get("function"==typeof r.context?(n=r.context).call.apply(n,[this].concat(u)):r.context));if(i.state===e)return r.sync?i[N]:Promise.resolve(i[N]);i.state instanceof I&&i.state.action==r.abortAction&&i.state.abort(i);var l=null;Array.isArray(t)?0==t.length?i.state instanceof I&&i.state.abort(i):"string"==typeof i.state&&t.includes(i.state)||(l=new B(i._state,"".concat(i.name," ").concat(a," to ").concat(e," failed: current state ").concat(i._state," not from ").concat(t.join("|")))):t!==i.state&&(l=new B(i._state,"".concat(i.name," ").concat(a," to ").concat(e," failed: current state ").concat(i._state," not from ").concat(t)));var d=function(t){if(r.fail&&r.fail.call(o,t),r.sync){if(r.ignoreError)return t;throw t}return r.ignoreError?Promise.resolve(t):Promise.reject(t)};if(l)return d(l);var h=i.state,p=new I(h,e,a);q.call(i,p);var v,y=function(t){var n;return i[N]=t,p.aborted||(q.call(i,e),null===(n=r.success)||void 0===n||n.call(o,i[N])),t},g=function(t){return q.call(i,h,t),d(t)};try{var w=c.apply(this,u);return"object"===m(v=w)&&v&&"then"in v?w.then(y).catch(g):r.sync?y(w):Promise.resolve(y(w))}catch(r){return g(new B(i._state,"".concat(i.name," ").concat(a," from ").concat(t," to ").concat(e," failed: ").concat(r),r instanceof Error?r:new Error(String(r))))}}}}var H,V,G=(H="undefined"!=typeof window&&window.__AFSM__,V="undefined"!=typeof importScripts,H?function(t,e){window.dispatchEvent(new CustomEvent(t,{detail:e}))}:V?function(t,e){postMessage({type:t,payload:e})}:function(){});function q(t,e){var r=this._state;this._state=t;var n=t.toString();t&&this.emit(n,r),this.emit(X.STATECHANGED,t,r,e),this.updateDevTools({value:t,old:r,err:e instanceof Error?e.message:String(e)})}var X=function(t){function e(t,r,i){var a;o(this,e),(a=n(this,e)).name=t,a.groupName=r,a._state=e.INIT,t||(t=Date.now().toString(36)),i?Object.setPrototypeOf(a,i):i=Object.getPrototypeOf(a),r||(a.groupName=a.constructor.name);var s=i[z];return s?a.name=s.name+"-"+s.count++:i[z]={name:a.name,count:0},a.updateDevTools({diagram:a.stateDiagram}),a}return f(e,t),a(e,[{key:"stateDiagram",get:function(){var t=Object.getPrototypeOf(this),e=U.get(t)||[],r=new Set,n=[],o=[],i=new Set,a=Object.getPrototypeOf(t);U.has(a)&&(a.stateDiagram.forEach((function(t){return r.add(t)})),a.allStates.forEach((function(t){return i.add(t)}))),e.forEach((function(t){var e=t.from,r=t.to,i=t.action;"string"==typeof e?n.push({from:e,to:r,action:i}):e.length?e.forEach((function(t){n.push({from:t,to:r,action:i})})):o.push({to:r,action:i})})),n.forEach((function(t){var e=t.from,n=t.to,o=t.action;i.add(e),i.add(n),i.add(o+"ing"),r.add("".concat(e," --\x3e ").concat(o,"ing : ").concat(o)),r.add("".concat(o,"ing --\x3e ").concat(n," : ").concat(o," 🟢")),r.add("".concat(o,"ing --\x3e ").concat(e," : ").concat(o," 🔴"))})),o.forEach((function(t){var e=t.to,n=t.action;r.add("".concat(n,"ing --\x3e ").concat(e," : ").concat(n," 🟢")),i.forEach((function(t){t!==e&&r.add("".concat(t," --\x3e ").concat(n,"ing : ").concat(n))}))}));var s=p(r);return Object.defineProperties(t,{stateDiagram:{value:s},allStates:{value:i}}),s}},{key:"updateDevTools",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};G(e.UPDATEAFSM,Object.assign({name:this.name,group:this.groupName},t))}},{key:"state",get:function(){return this._state},set:function(t){q.call(this,t)}}],[{key:"get",value:function(t){var r;return"string"==typeof t?(r=e.instances.get(t))||e.instances.set(t,r=new e(t,void 0,Object.create(e.prototype))):(r=e.instances2.get(t))||e.instances2.set(t,r=new e(t.constructor.name,void 0,Object.create(e.prototype))),r}},{key:"getState",value:function(t){var r;return null===(r=e.get(t))||void 0===r?void 0:r.state}}])}(L.default);X.STATECHANGED="stateChanged",X.UPDATEAFSM="updateAFSM",X.INIT="[*]",X.ON="on",X.OFF="off",X.instances=new Map,X.instances2=new WeakMap;var K=function(t){function e(){var t;return o(this,e),t=n(this,e,arguments),$(t,"decoder"),$(t,"config"),t}return f(e,t),a(e,[{key:"initialize",value:(i=r(d().mark((function t(){var e=this;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.decoder=new VideoDecoder({output:function(t){e.emit("videoFrame",t)},error:function(t){e.close(),e.emit("error",t)}});case 1:case"end":return t.stop()}}),t,this)}))),function(){return i.apply(this,arguments)})},{key:"configure",value:function(t){this.config=t,this.decoder.configure(j(O({},t),{codec:this.getCodec(t)}))}},{key:"getCodec",value:function(t){switch(t.codec){case"hevc":return"hvc1.1.6.L0.12.34.56.78.9A.BC";case"av1":return"av01.0.05M.08";case"avc":return"avc1.420028";default:return t.codec}}},{key:"decode",value:function(t){"configured"===this.decoder.state&&this.decoder.decode(new EncodedVideoChunk(t))}},{key:"flush",value:function(){this.decoder.flush()}},{key:"reset",value:function(){this.decoder.reset()}},{key:"close",value:function(){"closed"!==this.decoder.state&&this.decoder.close()}}]);var i}(X);function J(){var t;self.onmessage=function(e){if("init"===e.data.type){var r=e.data,n=r.canvas,o=r.wasmScript,i=r.wasmBinary,a=null==n?void 0:n.getContext("2d"),s=0,c=0,u={wasmBinary:i,postRun:function(){t=new u.VideoDecoder({videoInfo:function(t,e){s=t,c=e,console.log("video info",t,e)},yuvData:function(t,e){var r=s*c,o=r>>2,i=u.HEAPU32[t>>2],f=u.HEAPU32[1+(t>>2)],l=u.HEAPU32[2+(t>>2)],d=u.HEAPU8.subarray(i,i+r),h=u.HEAPU8.subarray(f,f+o),p=u.HEAPU8.subarray(l,l+o),v=new Uint8Array(r+o+o);v.set(d),v.set(h,r),v.set(p,r+o);var m=new VideoFrame(v,{codedWidth:s,codedHeight:c,format:"I420",timestamp:e});n?(null==a||a.drawImage(m,0,0,n.width,n.height),null==a||a.commit()):self.postMessage({type:"yuvData",videoFrame:m},[m])}}),self.postMessage({type:"ready"})}};Function("var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;return "+o)()(u)}else if("decode"===e.data.type){var f=e.data.packet;null==t||t.decode(f.data,"key"==f.type,f.timestamp)}else if("setCodec"===e.data.type){var l=e.data,d=l.codec,h=l.format,p=l.description;null==t||t.setCodec(d,h,null!=p?p:"")}}}M([W([X.INIT,"closed"],"initialized")],K.prototype,"initialize"),M([W("initialized","configured",{sync:!0})],K.prototype,"configure"),M([function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return function(t,r,n){var o=n.value,i=r;n.value=function(){if(!e.includes(this.state.toString()))throw new B(this.state,"".concat(this.name," ").concat(i," failed: current state ").concat(this.state," not in ").concat(e));for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return o.apply(this,r)}}}("configured")],K.prototype,"decode"),M([W([],X.INIT,{sync:!0})],K.prototype,"reset"),M([W([],"closed",{ignoreError:!0,sync:!0})],K.prototype,"close");var Y=function(t){function e(t,r){var i,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=arguments.length>3?arguments[3]:void 0,c=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return o(this,e),(i=n(this,e)).createModule=t,i.wasmBinary=r,i.workerMode=a,i.canvas=s,i.yuvMode=c,$(i,"worker"),$(i,"decoder"),$(i,"config"),$(i,"module",{}),$(i,"width",0),$(i,"height",0),i}return f(e,t),a(e,[{key:"initialize",value:(i=r(d().mark((function t(e){var r,n,o,i,a,s=this;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.workerMode){t.next=10;break}return n=new RegExp("\\{(.+)\\}","s").exec(J.toString())[1],this.worker=new Worker(URL.createObjectURL(new Blob([n],{type:"text/javascript"}))),o=null==(r=this.canvas)?void 0:r.transferControlToOffscreen(),t.next=6,this.wasmBinary;case 6:return i=t.sent,console.warn("worker mode",i),this.worker.postMessage({type:"init",canvas:o,wasmScript:this.createModule.toString(),wasmBinary:i},o?[o,i]:[i]),t.abrupt("return",new Promise((function(t){s.worker.onmessage=function(e){if("ready"===e.data.type)delete s.wasmBinary,t(),console.warn("worker mode initialize success");else if("yuvData"===e.data.type){var r=e.data.videoFrame;s.emit("videoFrame",r)}}})));case 10:if(a=this.module,!this.wasmBinary){t.next=15;break}return t.next=14,this.wasmBinary;case 14:a.wasmBinary=t.sent;case 15:return a.print=function(t){return console.log(t)},a.printErr=function(t){return console.log("[JS] ERROR: ".concat(t))},a.onAbort=function(){return console.log("[JS] FATAL: WASM ABORTED")},t.abrupt("return",new Promise((function(t){a.postRun=function(e){s.decoder=new s.module.VideoDecoder(s),console.log("video soft decoder initialize success"),t()},e&&Object.assign(a,e),s.createModule(a)})));case 19:case"end":return t.stop()}}),t,this)}))),function(t){return i.apply(this,arguments)})},{key:"configure",value:function(t){var e,r,n;this.config=t;var o=this.config.codec.startsWith("avc")?"avc":"hevc",i=this.config.description?"avc"==o?"avcc":"hvcc":"annexb";null==(r=this.decoder)||r.setCodec(o,i,null!=(e=this.config.description)?e:""),null==(n=this.worker)||n.postMessage({type:"setCodec",codec:o,format:i,description:this.config.description})}},{key:"decode",value:function(t){var e,r;null==(e=this.decoder)||e.decode(t.data,"key"==t.type,t.timestamp),"configured"===this.state&&(null==(r=this.worker)||r.postMessage({type:"decode",packet:t}))}},{key:"flush",value:function(){}},{key:"reset",value:function(){this.config=void 0,this.decoder&&this.decoder.clear()}},{key:"close",value:function(){this.removeAllListeners(),this.decoder&&(this.decoder.clear(),this.decoder.delete())}},{key:"videoInfo",value:function(t,e){this.width=t,this.height=e;var r={width:t,height:e};this.emit("videoCodecInfo",r)}},{key:"yuvData",value:function(t,e){if(this.module){var r=this.width*this.height,n=r>>2,o=this.module.HEAPU32[t>>2],i=this.module.HEAPU32[1+(t>>2)],a=this.module.HEAPU32[2+(t>>2)],s=this.module.HEAPU8.subarray(o,o+r),c=this.module.HEAPU8.subarray(i,i+n),u=this.module.HEAPU8.subarray(a,a+n);if(this.yuvMode)this.emit("videoFrame",{y:s,u:c,v:u,timestamp:e});else{var f=new Uint8Array(r+n+n);f.set(s),f.set(c,r),f.set(u,r+n),this.emit("videoFrame",new VideoFrame(f,{codedWidth:this.width,codedHeight:this.height,format:"I420",timestamp:e}))}}}},{key:"errorInfo",value:function(t){var e={errMsg:t};this.emit("error",e)}}]);var i}(X);M([W([X.INIT,"closed"],"initialized")],Y.prototype,"initialize"),M([W("initialized","configured",{sync:!0})],Y.prototype,"configure"),M([W([],X.INIT,{sync:!0})],Y.prototype,"reset"),M([W([],"closed",{sync:!0})],Y.prototype,"close");var Z,Q=(Z="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(){var t,e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=r;i.ready=new Promise((function(r,n){t=r,e=n}));var s=Object.assign({},i),u="./this.program",l="object"==("undefined"==typeof window?"undefined":m(window)),d="function"==typeof importScripts;"object"==("undefined"==typeof process?"undefined":m(process))&&"object"==m(process.versions)&&process.versions.node;var h,p,v,y="";(l||d)&&(d?y=self.location.href:"undefined"!=typeof document&&document.currentScript&&(y=document.currentScript.src),Z&&(y=Z),y=0!==y.indexOf("blob:")?y.substr(0,y.replace(/[?#].*/,"").lastIndexOf("/")+1):"",h=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},d&&(v=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}),p=function(t,e,r){var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?e(n.response):r()},n.onerror=r,n.send(null)});var w,b,E,k=i.print||console.log.bind(console),_=i.printErr||console.error.bind(console);Object.assign(i,s),s=null,i.arguments&&i.arguments,i.thisProgram&&(u=i.thisProgram),i.quit&&i.quit,i.wasmBinary&&(w=i.wasmBinary),i.noExitRuntime,"object"!=("undefined"==typeof WebAssembly?"undefined":m(WebAssembly))&&q("no native wasm support detected");var P,T,C,S,A,D,x,F,O,j=!1,M=[],$=[],R=[];function L(t){M.unshift(t)}function z(t){R.unshift(t)}var N,I,B,U,W=0,H=null;function V(t){W++,i.monitorRunDependencies&&i.monitorRunDependencies(W)}function G(t){if(W--,i.monitorRunDependencies&&i.monitorRunDependencies(W),0==W&&H){var e=H;H=null,e()}}function q(t){i.onAbort&&i.onAbort(t),_(t="Aborted("+t+")"),j=!0,t+=". Build with -sASSERTIONS for more info.";var r=new WebAssembly.RuntimeError(t);throw e(r),r}function X(t){return t.startsWith("data:application/octet-stream;base64,")}function K(t){if(t==N&&w)return new Uint8Array(w);if(v)return v(t);throw"both async and sync fetching of the wasm failed"}function J(t,e,r){return function(t){return w||!l&&!d||"function"!=typeof fetch?Promise.resolve().then((function(){return K(t)})):fetch(t,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+t+"'";return e.arrayBuffer()})).catch((function(){return K(t)}))}(t).then((function(t){return WebAssembly.instantiate(t,e)})).then((function(t){return t})).then(r,(function(t){_("failed to asynchronously prepare wasm: "+t),q(t)}))}X(N="videodec.wasm")||(I=N,N=i.locateFile?i.locateFile(I,y):y+I);var Y=function(t){for(;t.length>0;)t.shift()(i)};function Q(t){this.excPtr=t,this.ptr=t-24,this.set_type=function(t){D[this.ptr+4>>2]=t},this.get_type=function(){return D[this.ptr+4>>2]},this.set_destructor=function(t){D[this.ptr+8>>2]=t},this.get_destructor=function(){return D[this.ptr+8>>2]},this.set_caught=function(t){t=t?1:0,P[this.ptr+12|0]=t},this.get_caught=function(){return 0!=P[this.ptr+12|0]},this.set_rethrown=function(t){t=t?1:0,P[this.ptr+13|0]=t},this.get_rethrown=function(){return 0!=P[this.ptr+13|0]},this.init=function(t,e){this.set_adjusted_ptr(0),this.set_type(t),this.set_destructor(e)},this.set_adjusted_ptr=function(t){D[this.ptr+16>>2]=t},this.get_adjusted_ptr=function(){return D[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Qe(this.get_type()))return D[this.excPtr>>2];var t=this.get_adjusted_ptr();return 0!==t?t:this.excPtr}}var tt={isAbs:function(t){return"/"===t.charAt(0)},splitPath:function(t){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(t).slice(1)},normalizeArray:function(t,e){for(var r=0,n=t.length-1;n>=0;n--){var o=t[n];"."===o?t.splice(n,1):".."===o?(t.splice(n,1),r++):r&&(t.splice(n,1),r--)}if(e)for(;r;r--)t.unshift("..");return t},normalize:function(t){var e=tt.isAbs(t),r="/"===t.substr(-1);return(t=tt.normalizeArray(t.split("/").filter((function(t){return!!t})),!e).join("/"))||e||(t="."),t&&r&&(t+="/"),(e?"/":"")+t},dirname:function(t){var e=tt.splitPath(t),r=e[0],n=e[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."},basename:function(t){if("/"===t)return"/";var e=(t=(t=tt.normalize(t)).replace(/\/$/,"")).lastIndexOf("/");return-1===e?t:t.substr(e+1)},join:function(){var t=Array.prototype.slice.call(arguments);return tt.normalize(t.join("/"))},join2:function(t,e){return tt.normalize(t+"/"+e)}},et=function(t){return(et=function(){if("object"==("undefined"==typeof crypto?"undefined":m(crypto))&&"function"==typeof crypto.getRandomValues)return function(t){return crypto.getRandomValues(t)};q("initRandomDevice")}())(t)},rt={resolve:function(){for(var t="",e=!1,r=arguments.length-1;r>=-1&&!e;r--){var n=r>=0?arguments[r]:vt.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";t=n+"/"+t,e=tt.isAbs(n)}return(e?"/":"")+(t=tt.normalizeArray(t.split("/").filter((function(t){return!!t})),!e).join("/"))||"."},relative:function(t,e){function r(t){for(var e=0;e<t.length&&""===t[e];e++);for(var r=t.length-1;r>=0&&""===t[r];r--);return e>r?[]:t.slice(e,r-e+1)}t=rt.resolve(t).substr(1),e=rt.resolve(e).substr(1);for(var n=r(t.split("/")),o=r(e.split("/")),i=Math.min(n.length,o.length),a=i,s=0;s<i;s++)if(n[s]!==o[s]){a=s;break}var c=[];for(s=a;s<n.length;s++)c.push("..");return(c=c.concat(o.slice(a))).join("/")}},nt="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,ot=function(t,e,r){for(var n=e+r,o=e;t[o]&&!(o>=n);)++o;if(o-e>16&&t.buffer&&nt)return nt.decode(t.subarray(e,o));for(var i="";e<o;){var a=t[e++];if(128&a){var s=63&t[e++];if(192!=(224&a)){var c=63&t[e++];if((a=224==(240&a)?(15&a)<<12|s<<6|c:(7&a)<<18|s<<12|c<<6|63&t[e++])<65536)i+=String.fromCharCode(a);else{var u=a-65536;i+=String.fromCharCode(55296|u>>10,56320|1023&u)}}else i+=String.fromCharCode((31&a)<<6|s)}else i+=String.fromCharCode(a)}return i},it=[],at=function(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n<=127?e++:n<=2047?e+=2:n>=55296&&n<=57343?(e+=4,++r):e+=3}return e},st=function(t,e,r,n){if(!(n>0))return 0;for(var o=r,i=r+n-1,a=0;a<t.length;++a){var s=t.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&t.charCodeAt(++a)),s<=127){if(r>=i)break;e[r++]=s}else if(s<=2047){if(r+1>=i)break;e[r++]=192|s>>6,e[r++]=128|63&s}else if(s<=65535){if(r+2>=i)break;e[r++]=224|s>>12,e[r++]=128|s>>6&63,e[r++]=128|63&s}else{if(r+3>=i)break;e[r++]=240|s>>18,e[r++]=128|s>>12&63,e[r++]=128|s>>6&63,e[r++]=128|63&s}}return e[r]=0,r-o};function ct(t,e,r){var n=at(t)+1,o=new Array(n),i=st(t,o,0,o.length);return o.length=i,o}var ut={ttys:[],init:function(){},shutdown:function(){},register:function(t,e){ut.ttys[t]={input:[],output:[],ops:e},vt.registerDevice(t,ut.stream_ops)},stream_ops:{open:function(t){var e=ut.ttys[t.node.rdev];if(!e)throw new vt.ErrnoError(43);t.tty=e,t.seekable=!1},close:function(t){t.tty.ops.fsync(t.tty)},fsync:function(t){t.tty.ops.fsync(t.tty)},read:function(t,e,r,n,o){if(!t.tty||!t.tty.ops.get_char)throw new vt.ErrnoError(60);for(var i=0,a=0;a<n;a++){var s;try{s=t.tty.ops.get_char(t.tty)}catch(t){throw new vt.ErrnoError(29)}if(void 0===s&&0===i)throw new vt.ErrnoError(6);if(null==s)break;i++,e[r+a]=s}return i&&(t.node.timestamp=Date.now()),i},write:function(t,e,r,n,o){if(!t.tty||!t.tty.ops.put_char)throw new vt.ErrnoError(60);try{for(var i=0;i<n;i++)t.tty.ops.put_char(t.tty,e[r+i])}catch(t){throw new vt.ErrnoError(29)}return n&&(t.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(t){return function(){if(!it.length){var t=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n"),!t)return null;it=ct(t)}return it.shift()}()},put_char:function(t,e){null===e||10===e?(k(ot(t.output,0)),t.output=[]):0!=e&&t.output.push(e)},fsync:function(t){t.output&&t.output.length>0&&(k(ot(t.output,0)),t.output=[])},ioctl_tcgets:function(t){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets:function(t,e,r){return 0},ioctl_tiocgwinsz:function(t){return[24,80]}},default_tty1_ops:{put_char:function(t,e){null===e||10===e?(_(ot(t.output,0)),t.output=[]):0!=e&&t.output.push(e)},fsync:function(t){t.output&&t.output.length>0&&(_(ot(t.output,0)),t.output=[])}}},ft=function(t){q()},lt={ops_table:null,mount:function(t){return lt.createNode(null,"/",16895,0)},createNode:function(t,e,r,n){if(vt.isBlkdev(r)||vt.isFIFO(r))throw new vt.ErrnoError(63);lt.ops_table||(lt.ops_table={dir:{node:{getattr:lt.node_ops.getattr,setattr:lt.node_ops.setattr,lookup:lt.node_ops.lookup,mknod:lt.node_ops.mknod,rename:lt.node_ops.rename,unlink:lt.node_ops.unlink,rmdir:lt.node_ops.rmdir,readdir:lt.node_ops.readdir,symlink:lt.node_ops.symlink},stream:{llseek:lt.stream_ops.llseek}},file:{node:{getattr:lt.node_ops.getattr,setattr:lt.node_ops.setattr},stream:{llseek:lt.stream_ops.llseek,read:lt.stream_ops.read,write:lt.stream_ops.write,allocate:lt.stream_ops.allocate,mmap:lt.stream_ops.mmap,msync:lt.stream_ops.msync}},link:{node:{getattr:lt.node_ops.getattr,setattr:lt.node_ops.setattr,readlink:lt.node_ops.readlink},stream:{}},chrdev:{node:{getattr:lt.node_ops.getattr,setattr:lt.node_ops.setattr},stream:vt.chrdev_stream_ops}});var o=vt.createNode(t,e,r,n);return vt.isDir(o.mode)?(o.node_ops=lt.ops_table.dir.node,o.stream_ops=lt.ops_table.dir.stream,o.contents={}):vt.isFile(o.mode)?(o.node_ops=lt.ops_table.file.node,o.stream_ops=lt.ops_table.file.stream,o.usedBytes=0,o.contents=null):vt.isLink(o.mode)?(o.node_ops=lt.ops_table.link.node,o.stream_ops=lt.ops_table.link.stream):vt.isChrdev(o.mode)&&(o.node_ops=lt.ops_table.chrdev.node,o.stream_ops=lt.ops_table.chrdev.stream),o.timestamp=Date.now(),t&&(t.contents[e]=o,t.timestamp=o.timestamp),o},getFileDataAsTypedArray:function(t){return t.contents?t.contents.subarray?t.contents.subarray(0,t.usedBytes):new Uint8Array(t.contents):new Uint8Array(0)},expandFileStorage:function(t,e){var r=t.contents?t.contents.length:0;if(!(r>=e)){e=Math.max(e,r*(r<1048576?2:1.125)>>>0),0!=r&&(e=Math.max(e,256));var n=t.contents;t.contents=new Uint8Array(e),t.usedBytes>0&&t.contents.set(n.subarray(0,t.usedBytes),0)}},resizeFileStorage:function(t,e){if(t.usedBytes!=e)if(0==e)t.contents=null,t.usedBytes=0;else{var r=t.contents;t.contents=new Uint8Array(e),r&&t.contents.set(r.subarray(0,Math.min(e,t.usedBytes))),t.usedBytes=e}},node_ops:{getattr:function(t){var e={};return e.dev=vt.isChrdev(t.mode)?t.id:1,e.ino=t.id,e.mode=t.mode,e.nlink=1,e.uid=0,e.gid=0,e.rdev=t.rdev,vt.isDir(t.mode)?e.size=4096:vt.isFile(t.mode)?e.size=t.usedBytes:vt.isLink(t.mode)?e.size=t.link.length:e.size=0,e.atime=new Date(t.timestamp),e.mtime=new Date(t.timestamp),e.ctime=new Date(t.timestamp),e.blksize=4096,e.blocks=Math.ceil(e.size/e.blksize),e},setattr:function(t,e){void 0!==e.mode&&(t.mode=e.mode),void 0!==e.timestamp&&(t.timestamp=e.timestamp),void 0!==e.size&&lt.resizeFileStorage(t,e.size)},lookup:function(t,e){throw vt.genericErrors[44]},mknod:function(t,e,r,n){return lt.createNode(t,e,r,n)},rename:function(t,e,r){if(vt.isDir(t.mode)){var n;try{n=vt.lookupNode(e,r)}catch(t){}if(n)for(var o in n.contents)throw new vt.ErrnoError(55)}delete t.parent.contents[t.name],t.parent.timestamp=Date.now(),t.name=r,e.contents[r]=t,e.timestamp=t.parent.timestamp,t.parent=e},unlink:function(t,e){delete t.contents[e],t.timestamp=Date.now()},rmdir:function(t,e){var r=vt.lookupNode(t,e);for(var n in r.contents)throw new vt.ErrnoError(55);delete t.contents[e],t.timestamp=Date.now()},readdir:function(t){var e=[".",".."];for(var r in t.contents)t.contents.hasOwnProperty(r)&&e.push(r);return e},symlink:function(t,e,r){var n=lt.createNode(t,e,41471,0);return n.link=r,n},readlink:function(t){if(!vt.isLink(t.mode))throw new vt.ErrnoError(28);return t.link}},stream_ops:{read:function(t,e,r,n,o){var i=t.node.contents;if(o>=t.node.usedBytes)return 0;var a=Math.min(t.node.usedBytes-o,n);if(a>8&&i.subarray)e.set(i.subarray(o,o+a),r);else for(var s=0;s<a;s++)e[r+s]=i[o+s];return a},write:function(t,e,r,n,o,i){if(!n)return 0;var a=t.node;if(a.timestamp=Date.now(),e.subarray&&(!a.contents||a.contents.subarray)){if(i)return a.contents=e.subarray(r,r+n),a.usedBytes=n,n;if(0===a.usedBytes&&0===o)return a.contents=e.slice(r,r+n),a.usedBytes=n,n;if(o+n<=a.usedBytes)return a.contents.set(e.subarray(r,r+n),o),n}if(lt.expandFileStorage(a,o+n),a.contents.subarray&&e.subarray)a.contents.set(e.subarray(r,r+n),o);else for(var s=0;s<n;s++)a.contents[o+s]=e[r+s];return a.usedBytes=Math.max(a.usedBytes,o+n),n},llseek:function(t,e,r){var n=e;if(1===r?n+=t.position:2===r&&vt.isFile(t.node.mode)&&(n+=t.node.usedBytes),n<0)throw new vt.ErrnoError(28);return n},allocate:function(t,e,r){lt.expandFileStorage(t.node,e+r),t.node.usedBytes=Math.max(t.node.usedBytes,e+r)},mmap:function(t,e,r,n,o){if(!vt.isFile(t.node.mode))throw new vt.ErrnoError(43);var i,a,s=t.node.contents;if(2&o||s.buffer!==P.buffer){if((r>0||r+e<s.length)&&(s=s.subarray?s.subarray(r,r+e):Array.prototype.slice.call(s,r,r+e)),a=!0,!(i=ft()))throw new vt.ErrnoError(48);P.set(s,i)}else a=!1,i=s.byteOffset;return{ptr:i,allocated:a}},msync:function(t,e,r,n,o){return lt.stream_ops.write(t,e,0,n,r,!1),0}}},dt=function(t,e,r,n){var o="al ".concat(t);p(t,(function(r){var n,i;n=r,i='Loading data file "'.concat(t,'" failed (no arrayBuffer).'),n||q(i),e(new Uint8Array(r)),o&&G()}),(function(e){if(!r)throw'Loading data file "'.concat(t,'" failed.');r()})),o&&V()},ht=i.preloadPlugins||[];function pt(t,e){var r=0;return t&&(r|=365),e&&(r|=146),r}var vt={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(t=rt.resolve(t)))return{path:"",node:null};if((e=Object.assign({follow_mount:!0,recurse_count:0},e)).recurse_count>8)throw new vt.ErrnoError(32);for(var r=t.split("/").filter((function(t){return!!t})),n=vt.root,o="/",i=0;i<r.length;i++){var a=i===r.length-1;if(a&&e.parent)break;if(n=vt.lookupNode(n,r[i]),o=tt.join2(o,r[i]),vt.isMountpoint(n)&&(!a||a&&e.follow_mount)&&(n=n.mounted.root),!a||e.follow)for(var s=0;vt.isLink(n.mode);){var c=vt.readlink(o);if(o=rt.resolve(tt.dirname(o),c),n=vt.lookupPath(o,{recurse_count:e.recurse_count+1}).node,s++>40)throw new vt.ErrnoError(32)}}return{path:o,node:n}},getPath:function(t){for(var e;;){if(vt.isRoot(t)){var r=t.mount.mountpoint;return e?"/"!==r[r.length-1]?"".concat(r,"/").concat(e):r+e:r}e=e?"".concat(t.name,"/").concat(e):t.name,t=t.parent}},hashName:function(t,e){for(var r=0,n=0;n<e.length;n++)r=(r<<5)-r+e.charCodeAt(n)|0;return(t+r>>>0)%vt.nameTable.length},hashAddNode:function(t){var e=vt.hashName(t.parent.id,t.name);t.name_next=vt.nameTable[e],vt.nameTable[e]=t},hashRemoveNode:function(t){var e=vt.hashName(t.parent.id,t.name);if(vt.nameTable[e]===t)vt.nameTable[e]=t.name_next;else for(var r=vt.nameTable[e];r;){if(r.name_next===t){r.name_next=t.name_next;break}r=r.name_next}},lookupNode:function(t,e){var r=vt.mayLookup(t);if(r)throw new vt.ErrnoError(r,t);for(var n=vt.hashName(t.id,e),o=vt.nameTable[n];o;o=o.name_next){var i=o.name;if(o.parent.id===t.id&&i===e)return o}return vt.lookup(t,e)},createNode:function(t,e,r,n){var o=new vt.FSNode(t,e,r,n);return vt.hashAddNode(o),o},destroyNode:function(t){vt.hashRemoveNode(t)},isRoot:function(t){return t===t.parent},isMountpoint:function(t){return!!t.mounted},isFile:function(t){return 32768==(61440&t)},isDir:function(t){return 16384==(61440&t)},isLink:function(t){return 40960==(61440&t)},isChrdev:function(t){return 8192==(61440&t)},isBlkdev:function(t){return 24576==(61440&t)},isFIFO:function(t){return 4096==(61440&t)},isSocket:function(t){return!(49152&~t)},flagsToPermissionString:function(t){var e=["r","w","rw"][3&t];return 512&t&&(e+="w"),e},nodePermissions:function(t,e){return vt.ignorePermissions||(!e.includes("r")||292&t.mode)&&(!e.includes("w")||146&t.mode)&&(!e.includes("x")||73&t.mode)?0:2},mayLookup:function(t){var e=vt.nodePermissions(t,"x");return e||(t.node_ops.lookup?0:2)},mayCreate:function(t,e){try{return vt.lookupNode(t,e),20}catch(t){}return vt.nodePermissions(t,"wx")},mayDelete:function(t,e,r){var n;try{n=vt.lookupNode(t,e)}catch(t){return t.errno}var o=vt.nodePermissions(t,"wx");if(o)return o;if(r){if(!vt.isDir(n.mode))return 54;if(vt.isRoot(n)||vt.getPath(n)===vt.cwd())return 10}else if(vt.isDir(n.mode))return 31;return 0},mayOpen:function(t,e){return t?vt.isLink(t.mode)?32:vt.isDir(t.mode)&&("r"!==vt.flagsToPermissionString(e)||512&e)?31:vt.nodePermissions(t,vt.flagsToPermissionString(e)):44},MAX_OPEN_FDS:4096,nextfd:function(){for(var t=0;t<=vt.MAX_OPEN_FDS;t++)if(!vt.streams[t])return t;throw new vt.ErrnoError(33)},getStreamChecked:function(t){var e=vt.getStream(t);if(!e)throw new vt.ErrnoError(8);return e},getStream:function(t){return vt.streams[t]},createStream:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;return vt.FSStream||(vt.FSStream=function(){this.shared={}},vt.FSStream.prototype={},Object.defineProperties(vt.FSStream.prototype,{object:{get:function(){return this.node},set:function(t){this.node=t}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return!!(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(t){this.shared.flags=t}},position:{get:function(){return this.shared.position},set:function(t){this.shared.position=t}}})),t=Object.assign(new vt.FSStream,t),-1==e&&(e=vt.nextfd()),t.fd=e,vt.streams[e]=t,t},closeStream:function(t){vt.streams[t]=null},chrdev_stream_ops:{open:function(t){var e=vt.getDevice(t.node.rdev);t.stream_ops=e.stream_ops,t.stream_ops.open&&t.stream_ops.open(t)},llseek:function(){throw new vt.ErrnoError(70)}},major:function(t){return t>>8},minor:function(t){return 255&t},makedev:function(t,e){return t<<8|e},registerDevice:function(t,e){vt.devices[t]={stream_ops:e}},getDevice:function(t){return vt.devices[t]},getMounts:function(t){for(var e=[],r=[t];r.length;){var n=r.pop();e.push(n),r.push.apply(r,n.mounts)}return e},syncfs:function(t,e){"function"==typeof t&&(e=t,t=!1),vt.syncFSRequests++,vt.syncFSRequests>1&&_("warning: ".concat(vt.syncFSRequests," FS.syncfs operations in flight at once, probably just doing extra work"));var r=vt.getMounts(vt.root.mount),n=0;function o(t){return vt.syncFSRequests--,e(t)}function i(t){if(t)return i.errored?void 0:(i.errored=!0,o(t));++n>=r.length&&o(null)}r.forEach((function(e){if(!e.type.syncfs)return i(null);e.type.syncfs(e,t,i)}))},mount:function(t,e,r){var n,o="/"===r,i=!r;if(o&&vt.root)throw new vt.ErrnoError(10);if(!o&&!i){var a=vt.lookupPath(r,{follow_mount:!1});if(r=a.path,n=a.node,vt.isMountpoint(n))throw new vt.ErrnoError(10);if(!vt.isDir(n.mode))throw new vt.ErrnoError(54)}var s={type:t,opts:e,mountpoint:r,mounts:[]},c=t.mount(s);return c.mount=s,s.root=c,o?vt.root=c:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),c},unmount:function(t){var e=vt.lookupPath(t,{follow_mount:!1});if(!vt.isMountpoint(e.node))throw new vt.ErrnoError(28);var r=e.node,n=r.mounted,o=vt.getMounts(n);Object.keys(vt.nameTable).forEach((function(t){for(var e=vt.nameTable[t];e;){var r=e.name_next;o.includes(e.mount)&&vt.destroyNode(e),e=r}})),r.mounted=null;var i=r.mount.mounts.indexOf(n);r.mount.mounts.splice(i,1)},lookup:function(t,e){return t.node_ops.lookup(t,e)},mknod:function(t,e,r){var n=vt.lookupPath(t,{parent:!0}).node,o=tt.basename(t);if(!o||"."===o||".."===o)throw new vt.ErrnoError(28);var i=vt.mayCreate(n,o);if(i)throw new vt.ErrnoError(i);if(!n.node_ops.mknod)throw new vt.ErrnoError(63);return n.node_ops.mknod(n,o,e,r)},create:function(t,e){return e=void 0!==e?e:438,e&=4095,e|=32768,vt.mknod(t,e,0)},mkdir:function(t,e){return e=void 0!==e?e:511,e&=1023,e|=16384,vt.mknod(t,e,0)},mkdirTree:function(t,e){for(var r=t.split("/"),n="",o=0;o<r.length;++o)if(r[o]){n+="/"+r[o];try{vt.mkdir(n,e)}catch(t){if(20!=t.errno)throw t}}},mkdev:function(t,e,r){return void 0===r&&(r=e,e=438),e|=8192,vt.mknod(t,e,r)},symlink:function(t,e){if(!rt.resolve(t))throw new vt.ErrnoError(44);var r=vt.lookupPath(e,{parent:!0}).node;if(!r)throw new vt.ErrnoError(44);var n=tt.basename(e),o=vt.mayCreate(r,n);if(o)throw new vt.ErrnoError(o);if(!r.node_ops.symlink)throw new vt.ErrnoError(63);return r.node_ops.symlink(r,n,t)},rename:function(t,e){var r,n,o=tt.dirname(t),i=tt.dirname(e),a=tt.basename(t),s=tt.basename(e);if(r=vt.lookupPath(t,{parent:!0}).node,n=vt.lookupPath(e,{parent:!0}).node,!r||!n)throw new vt.ErrnoError(44);if(r.mount!==n.mount)throw new vt.ErrnoError(75);var c,u=vt.lookupNode(r,a),f=rt.relative(t,i);if("."!==f.charAt(0))throw new vt.ErrnoError(28);if("."!==(f=rt.relative(e,o)).charAt(0))throw new vt.ErrnoError(55);try{c=vt.lookupNode(n,s)}catch(t){}if(u!==c){var l=vt.isDir(u.mode),d=vt.mayDelete(r,a,l);if(d)throw new vt.ErrnoError(d);if(d=c?vt.mayDelete(n,s,l):vt.mayCreate(n,s))throw new vt.ErrnoError(d);if(!r.node_ops.rename)throw new vt.ErrnoError(63);if(vt.isMountpoint(u)||c&&vt.isMountpoint(c))throw new vt.ErrnoError(10);if(n!==r&&(d=vt.nodePermissions(r,"w")))throw new vt.ErrnoError(d);vt.hashRemoveNode(u);try{r.node_ops.rename(u,n,s)}catch(t){throw t}finally{vt.hashAddNode(u)}}},rmdir:function(t){var e=vt.lookupPath(t,{parent:!0}).node,r=tt.basename(t),n=vt.lookupNode(e,r),o=vt.mayDelete(e,r,!0);if(o)throw new vt.ErrnoError(o);if(!e.node_ops.rmdir)throw new vt.ErrnoError(63);if(vt.isMountpoint(n))throw new vt.ErrnoError(10);e.node_ops.rmdir(e,r),vt.destroyNode(n)},readdir:function(t){var e=vt.lookupPath(t,{follow:!0}).node;if(!e.node_ops.readdir)throw new vt.ErrnoError(54);return e.node_ops.readdir(e)},unlink:function(t){var e=vt.lookupPath(t,{parent:!0}).node;if(!e)throw new vt.ErrnoError(44);var r=tt.basename(t),n=vt.lookupNode(e,r),o=vt.mayDelete(e,r,!1);if(o)throw new vt.ErrnoError(o);if(!e.node_ops.unlink)throw new vt.ErrnoError(63);if(vt.isMountpoint(n))throw new vt.ErrnoError(10);e.node_ops.unlink(e,r),vt.destroyNode(n)},readlink:function(t){var e=vt.lookupPath(t).node;if(!e)throw new vt.ErrnoError(44);if(!e.node_ops.readlink)throw new vt.ErrnoError(28);return rt.resolve(vt.getPath(e.parent),e.node_ops.readlink(e))},stat:function(t,e){var r=vt.lookupPath(t,{follow:!e}).node;if(!r)throw new vt.ErrnoError(44);if(!r.node_ops.getattr)throw new vt.ErrnoError(63);return r.node_ops.getattr(r)},lstat:function(t){return vt.stat(t,!0)},chmod:function(t,e,r){var n;if(!(n="string"==typeof t?vt.lookupPath(t,{follow:!r}).node:t).node_ops.setattr)throw new vt.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&e|-4096&n.mode,timestamp:Date.now()})},lchmod:function(t,e){vt.chmod(t,e,!0)},fchmod:function(t,e){var r=vt.getStreamChecked(t);vt.chmod(r.node,e)},chown:function(t,e,r,n){var o;if(!(o="string"==typeof t?vt.lookupPath(t,{follow:!n}).node:t).node_ops.setattr)throw new vt.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown:function(t,e,r){vt.chown(t,e,r,!0)},fchown:function(t,e,r){var n=vt.getStreamChecked(t);vt.chown(n.node,e,r)},truncate:function(t,e){if(e<0)throw new vt.ErrnoError(28);var r;if(!(r="string"==typeof t?vt.lookupPath(t,{follow:!0}).node:t).node_ops.setattr)throw new vt.ErrnoError(63);if(vt.isDir(r.mode))throw new vt.ErrnoError(31);if(!vt.isFile(r.mode))throw new vt.ErrnoError(28);var n=vt.nodePermissions(r,"w");if(n)throw new vt.ErrnoError(n);r.node_ops.setattr(r,{size:e,timestamp:Date.now()})},ftruncate:function(t,e){var r=vt.getStreamChecked(t);if(!(2097155&r.flags))throw new vt.ErrnoError(28);vt.truncate(r.node,e)},utime:function(t,e,r){var n=vt.lookupPath(t,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(e,r)})},open:function(t,e,r){if(""===t)throw new vt.ErrnoError(44);var n;if(r=void 0===r?438:r,r=64&(e="string"==typeof e?function(t){var e={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[t];if(void 0===e)throw new Error("Unknown file open mode: ".concat(t));return e}(e):e)?4095&r|32768:0,"object"==m(t))n=t;else{t=tt.normalize(t);try{n=vt.lookupPath(t,{follow:!(131072&e)}).node}catch(t){}}var o=!1;if(64&e)if(n){if(128&e)throw new vt.ErrnoError(20)}else n=vt.mknod(t,r,0),o=!0;if(!n)throw new vt.ErrnoError(44);if(vt.isChrdev(n.mode)&&(e&=-513),65536&e&&!vt.isDir(n.mode))throw new vt.ErrnoError(54);if(!o){var a=vt.mayOpen(n,e);if(a)throw new vt.ErrnoError(a)}512&e&&!o&&vt.truncate(n,0),e&=-131713;var s=vt.createStream({node:n,path:vt.getPath(n),flags:e,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return s.stream_ops.open&&s.stream_ops.open(s),!i.logReadFiles||1&e||(vt.readFiles||(vt.readFiles={}),t in vt.readFiles||(vt.readFiles[t]=1)),s},close:function(t){if(vt.isClosed(t))throw new vt.ErrnoError(8);t.getdents&&(t.getdents=null);try{t.stream_ops.close&&t.stream_ops.close(t)}catch(t){throw t}finally{vt.closeStream(t.fd)}t.fd=null},isClosed:function(t){return null===t.fd},llseek:function(t,e,r){if(vt.isClosed(t))throw new vt.ErrnoError(8);if(!t.seekable||!t.stream_ops.llseek)throw new vt.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new vt.ErrnoError(28);return t.position=t.stream_ops.llseek(t,e,r),t.ungotten=[],t.position},read:function(t,e,r,n,o){if(n<0||o<0)throw new vt.ErrnoError(28);if(vt.isClosed(t))throw new vt.ErrnoError(8);if(1==(2097155&t.flags))throw new vt.ErrnoError(8);if(vt.isDir(t.node.mode))throw new vt.ErrnoError(31);if(!t.stream_ops.read)throw new vt.ErrnoError(28);var i=void 0!==o;if(i){if(!t.seekable)throw new vt.ErrnoError(70)}else o=t.position;var a=t.stream_ops.read(t,e,r,n,o);return i||(t.position+=a),a},write:function(t,e,r,n,o,i){if(n<0||o<0)throw new vt.ErrnoError(28);if(vt.isClosed(t))throw new vt.ErrnoError(8);if(!(2097155&t.flags))throw new vt.ErrnoError(8);if(vt.isDir(t.node.mode))throw new vt.ErrnoError(31);if(!t.stream_ops.write)throw new vt.ErrnoError(28);t.seekable&&1024&t.flags&&vt.llseek(t,0,2);var a=void 0!==o;if(a){if(!t.seekable)throw new vt.ErrnoError(70)}else o=t.position;var s=t.stream_ops.write(t,e,r,n,o,i);return a||(t.position+=s),s},allocate:function(t,e,r){if(vt.isClosed(t))throw new vt.ErrnoError(8);if(e<0||r<=0)throw new vt.ErrnoError(28);if(!(2097155&t.flags))throw new vt.ErrnoError(8);if(!vt.isFile(t.node.mode)&&!vt.isDir(t.node.mode))throw new vt.ErrnoError(43);if(!t.stream_ops.allocate)throw new vt.ErrnoError(138);t.stream_ops.allocate(t,e,r)},mmap:function(t,e,r,n,o){if(2&n&&!(2&o)&&2!=(2097155&t.flags))throw new vt.ErrnoError(2);if(1==(2097155&t.flags))throw new vt.ErrnoError(2);if(!t.stream_ops.mmap)throw new vt.ErrnoError(43);return t.stream_ops.mmap(t,e,r,n,o)},msync:function(t,e,r,n,o){return t.stream_ops.msync?t.stream_ops.msync(t,e,r,n,o):0},munmap:function(t){return 0},ioctl:function(t,e,r){if(!t.stream_ops.ioctl)throw new vt.ErrnoError(59);return t.stream_ops.ioctl(t,e,r)},readFile:function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r.flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'.concat(r.encoding,'"'));var n=vt.open(t,r.flags),o=vt.stat(t).size,i=new Uint8Array(o);return vt.read(n,i,0,o,0),"utf8"===r.encoding?e=ot(i,0):"binary"===r.encoding&&(e=i),vt.close(n),e},writeFile:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};r.flags=r.flags||577;var n=vt.open(t,r.flags,r.mode);if("string"==typeof e){var o=new Uint8Array(at(e)+1),i=st(e,o,0,o.length);vt.write(n,o,0,i,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(e))throw new Error("Unsupported data type");vt.write(n,e,0,e.byteLength,void 0,r.canOwn)}vt.close(n)},cwd:function(){return vt.currentPath},chdir:function(t){var e=vt.lookupPath(t,{follow:!0});if(null===e.node)throw new vt.ErrnoError(44);if(!vt.isDir(e.node.mode))throw new vt.ErrnoError(54);var r=vt.nodePermissions(e.node,"x");if(r)throw new vt.ErrnoError(r);vt.currentPath=e.path},createDefaultDirectories:function(){vt.mkdir("/tmp"),vt.mkdir("/home"),vt.mkdir("/home/<USER>")},createDefaultDevices:function(){vt.mkdir("/dev"),vt.registerDevice(vt.makedev(1,3),{read:function(){return 0},write:function(t,e,r,n,o){return n}}),vt.mkdev("/dev/null",vt.makedev(1,3)),ut.register(vt.makedev(5,0),ut.default_tty_ops),ut.register(vt.makedev(6,0),ut.default_tty1_ops),vt.mkdev("/dev/tty",vt.makedev(5,0)),vt.mkdev("/dev/tty1",vt.makedev(6,0));var t=new Uint8Array(1024),e=0,r=function(){return 0===e&&(e=et(t).byteLength),t[--e]};vt.createDevice("/dev","random",r),vt.createDevice("/dev","urandom",r),vt.mkdir("/dev/shm"),vt.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){vt.mkdir("/proc");var t=vt.mkdir("/proc/self");vt.mkdir("/proc/self/fd"),vt.mount({mount:function(){var e=vt.createNode(t,"fd",16895,73);return e.node_ops={lookup:function(t,e){var r=+e,n=vt.getStreamChecked(r),o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return o.parent=o,o}},e}},{},"/proc/self/fd")},createStandardStreams:function(){i.stdin?vt.createDevice("/dev","stdin",i.stdin):vt.symlink("/dev/tty","/dev/stdin"),i.stdout?vt.createDevice("/dev","stdout",null,i.stdout):vt.symlink("/dev/tty","/dev/stdout"),i.stderr?vt.createDevice("/dev","stderr",null,i.stderr):vt.symlink("/dev/tty1","/dev/stderr"),vt.open("/dev/stdin",0),vt.open("/dev/stdout",1),vt.open("/dev/stderr",1)},ensureErrnoError:function(){vt.ErrnoError||(vt.ErrnoError=function(t,e){this.name="ErrnoError",this.node=e,this.setErrno=function(t){this.errno=t},this.setErrno(t),this.message="FS error"},vt.ErrnoError.prototype=new Error,vt.ErrnoError.prototype.constructor=vt.ErrnoError,[44].forEach((function(t){vt.genericErrors[t]=new vt.ErrnoError(t),vt.genericErrors[t].stack="<generic error, no stack>"})))},staticInit:function(){vt.ensureErrnoError(),vt.nameTable=new Array(4096),vt.mount(lt,{},"/"),vt.createDefaultDirectories(),vt.createDefaultDevices(),vt.createSpecialDirectories(),vt.filesystems={MEMFS:lt}},init:function(t,e,r){vt.init.initialized=!0,vt.ensureErrnoError(),i.stdin=t||i.stdin,i.stdout=e||i.stdout,i.stderr=r||i.stderr,vt.createStandardStreams()},quit:function(){vt.init.initialized=!1;for(var t=0;t<vt.streams.length;t++){var e=vt.streams[t];e&&vt.close(e)}},findObject:function(t,e){var r=vt.analyzePath(t,e);return r.exists?r.object:null},analyzePath:function(t,e){try{t=(n=vt.lookupPath(t,{follow:!e})).path}catch(t){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=vt.lookupPath(t,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=tt.basename(t),n=vt.lookupPath(t,{follow:!e}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(t){r.error=t.errno}return r},createPath:function(t,e,r,n){t="string"==typeof t?t:vt.getPath(t);for(var o=e.split("/").reverse();o.length;){var i=o.pop();if(i){var a=tt.join2(t,i);try{vt.mkdir(a)}catch(t){}t=a}}return a},createFile:function(t,e,r,n,o){var i=tt.join2("string"==typeof t?t:vt.getPath(t),e),a=pt(n,o);return vt.create(i,a)},createDataFile:function(t,e,r,n,o,i){var a=e;t&&(t="string"==typeof t?t:vt.getPath(t),a=e?tt.join2(t,e):t);var s=pt(n,o),c=vt.create(a,s);if(r){if("string"==typeof r){for(var u=new Array(r.length),f=0,l=r.length;f<l;++f)u[f]=r.charCodeAt(f);r=u}vt.chmod(c,146|s);var d=vt.open(c,577);vt.write(d,r,0,r.length,0,i),vt.close(d),vt.chmod(c,s)}return c},createDevice:function(t,e,r,n){var o=tt.join2("string"==typeof t?t:vt.getPath(t),e),i=pt(!!r,!!n);vt.createDevice.major||(vt.createDevice.major=64);var a=vt.makedev(vt.createDevice.major++,0);return vt.registerDevice(a,{open:function(t){t.seekable=!1},close:function(t){n&&n.buffer&&n.buffer.length&&n(10)},read:function(t,e,n,o,i){for(var a=0,s=0;s<o;s++){var c;try{c=r()}catch(t){throw new vt.ErrnoError(29)}if(void 0===c&&0===a)throw new vt.ErrnoError(6);if(null==c)break;a++,e[n+s]=c}return a&&(t.node.timestamp=Date.now()),a},write:function(t,e,r,o,i){for(var a=0;a<o;a++)try{n(e[r+a])}catch(t){throw new vt.ErrnoError(29)}return o&&(t.node.timestamp=Date.now()),a}}),vt.mkdev(o,i,a)},forceLoadFile:function(t){if(t.isDevice||t.isFolder||t.link||t.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!h)throw new Error("Cannot load without read() or XMLHttpRequest.");try{t.contents=ct(h(t.url)),t.usedBytes=t.contents.length}catch(t){throw new vt.ErrnoError(29)}},createLazyFile:function(t,e,r,n,o){function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(t){if(!(t>this.length-1||t<0)){var e=t%this.chunkSize,r=t/this.chunkSize|0;return this.getter(r)[e]}},i.prototype.setDataGetter=function(t){this.getter=t},i.prototype.cacheLength=function(){var t=new XMLHttpRequest;if(t.open("HEAD",r,!1),t.send(null),!(t.status>=200&&t.status<300||304===t.status))throw new Error("Couldn't load "+r+". Status: "+t.status);var e,n=Number(t.getResponseHeader("Content-length")),o=(e=t.getResponseHeader("Accept-Ranges"))&&"bytes"===e,i=(e=t.getResponseHeader("Content-Encoding"))&&"gzip"===e,a=1048576;o||(a=n);var s=this;s.setDataGetter((function(t){var e=t*a,o=(t+1)*a-1;if(o=Math.min(o,n-1),void 0===s.chunks[t]&&(s.chunks[t]=function(t,e){if(t>e)throw new Error("invalid range ("+t+", "+e+") or no bytes requested!");if(e>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",r,!1),n!==a&&o.setRequestHeader("Range","bytes="+t+"-"+e),o.responseType="arraybuffer",o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+r+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):ct(o.responseText||"")}(e,o)),void 0===s.chunks[t])throw new Error("doXHR failed!");return s.chunks[t]})),!i&&n||(a=n=1,n=this.getter(0).length,a=n,k("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=a,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!d)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new i;Object.defineProperties(a,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var s={isDevice:!1,contents:a}}else s={isDevice:!1,url:r};var c=vt.createFile(t,e,s,n,o);s.contents?c.contents=s.contents:s.url&&(c.contents=null,c.url=s.url),Object.defineProperties(c,{usedBytes:{get:function(){return this.contents.length}}});var u={};function f(t,e,r,n,o){var i=t.node.contents;if(o>=i.length)return 0;var a=Math.min(i.length-o,n);if(i.slice)for(var s=0;s<a;s++)e[r+s]=i[o+s];else for(s=0;s<a;s++)e[r+s]=i.get(o+s);return a}return Object.keys(c.stream_ops).forEach((function(t){var e=c.stream_ops[t];u[t]=function(){return vt.forceLoadFile(c),e.apply(null,arguments)}})),u.read=function(t,e,r,n,o){return vt.forceLoadFile(c),f(t,e,r,n,o)},u.mmap=function(t,e,r,n,o){vt.forceLoadFile(c);var i=ft();if(!i)throw new vt.ErrnoError(48);return f(t,P,i,e,r),{ptr:i,allocated:!0}},c.stream_ops=u,c}},mt=function(t,e){return t?ot(T,t,e):""},yt={DEFAULT_POLLMASK:5,calculateAt:function(t,e,r){if(tt.isAbs(e))return e;var n;if(n=-100===t?vt.cwd():yt.getStreamFromFD(t).path,0==e.length){if(!r)throw new vt.ErrnoError(44);return n}return tt.join2(n,e)},doStat:function(t,e,r){try{var n=t(e)}catch(t){if(t&&t.node&&tt.normalize(e)!==tt.normalize(vt.getPath(t.node)))return-54;throw t}A[r>>2]=n.dev,A[r+4>>2]=n.mode,D[r+8>>2]=n.nlink,A[r+12>>2]=n.uid,A[r+16>>2]=n.gid,A[r+20>>2]=n.rdev,U=[n.size>>>0,(B=n.size,+Math.abs(B)>=1?B>0?+Math.floor(B/4294967296)>>>0:~~+Math.ceil((B-+(~~B>>>0))/4294967296)>>>0:0)],A[r+24>>2]=U[0],A[r+28>>2]=U[1],A[r+32>>2]=4096,A[r+36>>2]=n.blocks;var o=n.atime.getTime(),i=n.mtime.getTime(),a=n.ctime.getTime();return U=[Math.floor(o/1e3)>>>0,(B=Math.floor(o/1e3),+Math.abs(B)>=1?B>0?+Math.floor(B/4294967296)>>>0:~~+Math.ceil((B-+(~~B>>>0))/4294967296)>>>0:0)],A[r+40>>2]=U[0],A[r+44>>2]=U[1],D[r+48>>2]=o%1e3*1e3,U=[Math.floor(i/1e3)>>>0,(B=Math.floor(i/1e3),+Math.abs(B)>=1?B>0?+Math.floor(B/4294967296)>>>0:~~+Math.ceil((B-+(~~B>>>0))/4294967296)>>>0:0)],A[r+56>>2]=U[0],A[r+60>>2]=U[1],D[r+64>>2]=i%1e3*1e3,U=[Math.floor(a/1e3)>>>0,(B=Math.floor(a/1e3),+Math.abs(B)>=1?B>0?+Math.floor(B/4294967296)>>>0:~~+Math.ceil((B-+(~~B>>>0))/4294967296)>>>0:0)],A[r+72>>2]=U[0],A[r+76>>2]=U[1],D[r+80>>2]=a%1e3*1e3,U=[n.ino>>>0,(B=n.ino,+Math.abs(B)>=1?B>0?+Math.floor(B/4294967296)>>>0:~~+Math.ceil((B-+(~~B>>>0))/4294967296)>>>0:0)],A[r+88>>2]=U[0],A[r+92>>2]=U[1],0},doMsync:function(t,e,r,n,o){if(!vt.isFile(e.node.mode))throw new vt.ErrnoError(43);if(2&n)return 0;var i=T.slice(t,t+r);vt.msync(e,i,o,r,n)},varargs:void 0,get:function(){return yt.varargs+=4,A[yt.varargs-4>>2]},getStr:function(t){return mt(t)},getStreamFromFD:function(t){return vt.getStreamChecked(t)}};function gt(t){switch(t){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: ".concat(t))}}var wt=void 0;function bt(t){for(var e="",r=t;T[r];)e+=wt[T[r++]];return e}var Et={},kt={},_t={},Pt=void 0;function Tt(t){throw new Pt(t)}var Ct=void 0;function St(t){throw new Ct(t)}function At(t,e,r){function n(e){var n=r(e);n.length!==t.length&&St("Mismatched type converter count");for(var o=0;o<t.length;++o)Dt(t[o],n[o])}t.forEach((function(t){_t[t]=e}));var o=new Array(e.length),i=[],a=0;e.forEach((function(t,e){kt.hasOwnProperty(t)?o[e]=kt[t]:(i.push(t),Et.hasOwnProperty(t)||(Et[t]=[]),Et[t].push((function(){o[e]=kt[t],++a===i.length&&n(o)})))})),0===i.length&&n(o)}function Dt(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in e))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=e.name;if(t||Tt('type "'.concat(n,'" must have a positive integer typeid pointer')),kt.hasOwnProperty(t)){if(r.ignoreDuplicateRegistrations)return;Tt("Cannot register type '".concat(n,"' twice"))}if(kt[t]=e,delete _t[t],Et.hasOwnProperty(t)){var o=Et[t];delete Et[t],o.forEach((function(t){return t()}))}}(t,e,r)}function xt(t){if(!(this instanceof Zt))return!1;if(!(t instanceof Zt))return!1;for(var e=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=t.$$.ptrType.registeredClass,o=t.$$.ptr;e.baseClass;)r=e.upcast(r),e=e.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return e===n&&r===o}function Ft(t){Tt(t.$$.ptrType.registeredClass.name+" instance already deleted")}var Ot=!1;function jt(t){}function Mt(t){t.count.value-=1,0===t.count.value&&function(t){t.smartPtr?t.smartPtrType.rawDestructor(t.smartPtr):t.ptrType.registeredClass.rawDestructor(t.ptr)}(t)}function $t(t,e,r){if(e===r)return t;if(void 0===r.baseClass)return null;var n=$t(t,e,r.baseClass);return null===n?null:r.downcast(n)}var Rt={};function Lt(){return Object.keys(Wt).length}function zt(){var t=[];for(var e in Wt)Wt.hasOwnProperty(e)&&t.push(Wt[e]);return t}var Nt=[];function It(){for(;Nt.length;){var t=Nt.pop();t.$$.deleteScheduled=!1,t.delete()}}var Bt=void 0;function Ut(t){Bt=t,Nt.length&&Bt&&Bt(It)}var Wt={};function Ht(t,e){return e=function(t,e){for(void 0===e&&Tt("ptr should not be undefined");t.baseClass;)e=t.upcast(e),t=t.baseClass;return e}(t,e),Wt[e]}function Vt(t,e){return e.ptrType&&e.ptr||St("makeClassHandle requires ptr and ptrType"),!!e.smartPtrType!=!!e.smartPtr&&St("Both smartPtrType and smartPtr must be specified"),e.count={value:1},qt(Object.create(t,{$$:{value:e}}))}function Gt(t){var e=this.getPointee(t);if(!e)return this.destructor(t),null;var r=Ht(this.registeredClass,e);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=e,r.$$.smartPtr=t,r.clone();var n=r.clone();return this.destructor(t),n}function o(){return this.isSmartPointer?Vt(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:e,smartPtrType:this,smartPtr:t}):Vt(this.registeredClass.instancePrototype,{ptrType:this,ptr:t})}var i,a=this.registeredClass.getActualType(e),s=Rt[a];if(!s)return o.call(this);i=this.isConst?s.constPointerType:s.pointerType;var c=$t(e,this.registeredClass,i.registeredClass);return null===c?o.call(this):this.isSmartPointer?Vt(i.registeredClass.instancePrototype,{ptrType:i,ptr:c,smartPtrType:this,smartPtr:t}):Vt(i.registeredClass.instancePrototype,{ptrType:i,ptr:c})}var qt=function(t){return"undefined"==typeof FinalizationRegistry?(qt=function(t){return t},t):(Ot=new FinalizationRegistry((function(t){Mt(t.$$)})),jt=function(t){return Ot.unregister(t)},(qt=function(t){var e=t.$$;if(e.smartPtr){var r={$$:e};Ot.register(t,r,t)}return t})(t))};function Xt(){if(this.$$.ptr||Ft(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var t,e=qt(Object.create(Object.getPrototypeOf(this),{$$:{value:(t=this.$$,{count:t.count,deleteScheduled:t.deleteScheduled,preservePointerOnDelete:t.preservePointerOnDelete,ptr:t.ptr,ptrType:t.ptrType,smartPtr:t.smartPtr,smartPtrType:t.smartPtrType})}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e}function Kt(){this.$$.ptr||Ft(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Tt("Object already scheduled for deletion"),jt(this),Mt(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Jt(){return!this.$$.ptr}function Yt(){return this.$$.ptr||Ft(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Tt("Object already scheduled for deletion"),Nt.push(this),1===Nt.length&&Bt&&Bt(It),this.$$.deleteScheduled=!0,this}function Zt(){}function Qt(t){if(void 0===t)return"_unknown";var e=(t=t.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return e>=48&&e<=57?"_".concat(t):t}function te(t,e){return c({},t=Qt(t),(function(){return e.apply(this,arguments)}))[t]}function ee(t,e,r){if(void 0===t[e].overloadTable){var n=t[e];t[e]=function(){return t[e].overloadTable.hasOwnProperty(arguments.length)||Tt("Function '".concat(r,"' called with an invalid number of arguments (").concat(arguments.length,") - expects one of (").concat(t[e].overloadTable,")!")),t[e].overloadTable[arguments.length].apply(this,arguments)},t[e].overloadTable=[],t[e].overloadTable[n.argCount]=n}}function re(t,e,r,n,o,i,a,s){this.name=t,this.constructor=e,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}function ne(t,e,r){for(;e!==r;)e.upcast||Tt("Expected null or instance of ".concat(r.name,", got an instance of ").concat(e.name)),t=e.upcast(t),e=e.baseClass;return t}function oe(t,e){if(null===e)return this.isReference&&Tt("null is not a valid ".concat(this.name)),0;e.$$||Tt('Cannot pass "'.concat(Ae(e),'" as a ').concat(this.name)),e.$$.ptr||Tt("Cannot pass deleted object as a pointer of type ".concat(this.name));var r=e.$$.ptrType.registeredClass;return ne(e.$$.ptr,r,this.registeredClass)}function ie(t,e){var r;if(null===e)return this.isReference&&Tt("null is not a valid ".concat(this.name)),this.isSmartPointer?(r=this.rawConstructor(),null!==t&&t.push(this.rawDestructor,r),r):0;e.$$||Tt('Cannot pass "'.concat(Ae(e),'" as a ').concat(this.name)),e.$$.ptr||Tt("Cannot pass deleted object as a pointer of type ".concat(this.name)),!this.isConst&&e.$$.ptrType.isConst&&Tt("Cannot convert argument of type ".concat(e.$$.smartPtrType?e.$$.smartPtrType.name:e.$$.ptrType.name," to parameter type ").concat(this.name));var n=e.$$.ptrType.registeredClass;if(r=ne(e.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===e.$$.smartPtr&&Tt("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:e.$$.smartPtrType===this?r=e.$$.smartPtr:Tt("Cannot convert argument of type ".concat(e.$$.smartPtrType?e.$$.smartPtrType.name:e.$$.ptrType.name," to parameter type ").concat(this.name));break;case 1:r=e.$$.smartPtr;break;case 2:if(e.$$.smartPtrType===this)r=e.$$.smartPtr;else{var o=e.clone();r=this.rawShare(r,Se.toHandle((function(){o.delete()}))),null!==t&&t.push(this.rawDestructor,r)}break;default:Tt("Unsupporting sharing policy")}return r}function ae(t,e){if(null===e)return this.isReference&&Tt("null is not a valid ".concat(this.name)),0;e.$$||Tt('Cannot pass "'.concat(Ae(e),'" as a ').concat(this.name)),e.$$.ptr||Tt("Cannot pass deleted object as a pointer of type ".concat(this.name)),e.$$.ptrType.isConst&&Tt("Cannot convert argument of type ".concat(e.$$.ptrType.name," to parameter type ").concat(this.name));var r=e.$$.ptrType.registeredClass;return ne(e.$$.ptr,r,this.registeredClass)}function se(t){return this.fromWireType(A[t>>2])}function ce(t){return this.rawGetPointee&&(t=this.rawGetPointee(t)),t}function ue(t){this.rawDestructor&&this.rawDestructor(t)}function fe(t){null!==t&&t.delete()}function le(t,e,r,n,o,i,a,s,c,u,f){this.name=t,this.registeredClass=e,this.isReference=r,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=c,this.rawShare=u,this.rawDestructor=f,o||void 0!==e.baseClass?this.toWireType=ie:n?(this.toWireType=oe,this.destructorFunction=null):(this.toWireType=ae,this.destructorFunction=null)}var de=[],he=function(t){var e=de[t];return e||(t>=de.length&&(de.length=t+1),de[t]=e=O.get(t)),e},pe=function(t,e,r){return t.includes("j")?function(t,e,r){var n=i["dynCall_"+t];return r&&r.length?n.apply(null,[e].concat(r)):n.call(null,e)}(t,e,r):he(e).apply(null,r)};function ve(t,e){var r,n,o,i=(t=bt(t)).includes("j")?(r=t,n=e,o=[],function(){return o.length=0,Object.assign(o,arguments),pe(r,n,o)}):he(e);return"function"!=typeof i&&Tt("unknown function pointer with signature ".concat(t,": ").concat(e)),i}var me=void 0;function ye(t){var e=Ye(t),r=bt(e);return Ke(e),r}function ge(t,e){var r=[],n={};throw e.forEach((function t(e){n[e]||kt[e]||(_t[e]?_t[e].forEach(t):(r.push(e),n[e]=!0))})),new me("".concat(t,": ")+r.map(ye).join([", "]))}function we(t,e){for(var r=[],n=0;n<t;n++)r.push(D[e+4*n>>2]);return r}function be(t){for(;t.length;){var e=t.pop();t.pop()(e)}}function Ee(t,e){if(!(t instanceof Function))throw new TypeError("new_ called with constructor type ".concat(m(t)," which is not a function"));var r=te(t.name||"unknownFunctionName",(function(){}));r.prototype=t.prototype;var n=new r,o=t.apply(n,e);return o instanceof Object?o:n}function ke(t,e,r,n,o,i){var a=e.length;a<2&&Tt("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==e[1]&&null!==r,c=!1,u=1;u<e.length;++u)if(null!==e[u]&&void 0===e[u].destructorFunction){c=!0;break}var f="void"!==e[0].name,l="",d="";for(u=0;u<a-2;++u)l+=(0!==u?", ":"")+"arg"+u,d+=(0!==u?", ":"")+"arg"+u+"Wired";var h="\n        return function ".concat(Qt(t),"(").concat(l,") {\n        if (arguments.length !== ").concat(a-2,") {\n          throwBindingError('function ").concat(t," called with ").concat(arguments.length," arguments, expected ").concat(a-2," args!');\n        }");c&&(h+="var destructors = [];\n");var p=c?"destructors":"null",v=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[Tt,n,o,be,e[0],e[1]];for(s&&(h+="var thisWired = classParam.toWireType("+p+", this);\n"),u=0;u<a-2;++u)h+="var arg"+u+"Wired = argType"+u+".toWireType("+p+", arg"+u+"); // "+e[u+2].name+"\n",v.push("argType"+u),m.push(e[u+2]);if(s&&(d="thisWired"+(d.length>0?", ":"")+d),h+=(f||i?"var rv = ":"")+"invoker(fn"+(d.length>0?", ":"")+d+");\n",c)h+="runDestructors(destructors);\n";else for(u=s?1:2;u<e.length;++u){var y=1===u?"thisWired":"arg"+(u-2)+"Wired";null!==e[u].destructorFunction&&(h+=y+"_dtor("+y+"); // "+e[u].name+"\n",v.push(y+"_dtor"),m.push(e[u].destructorFunction))}return f&&(h+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),h+="}\n",v.push(h),Ee(Function,v).apply(null,m)}function _e(){this.allocated=[void 0],this.freelist=[]}var Pe=new _e;function Te(t){t>=Pe.reserved&&0==--Pe.get(t).refcount&&Pe.free(t)}function Ce(){for(var t=0,e=Pe.reserved;e<Pe.allocated.length;++e)void 0!==Pe.allocated[e]&&++t;return t}var Se={toValue:function(t){return t||Tt("Cannot use deleted val. handle = "+t),Pe.get(t).value},toHandle:function(t){switch(t){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return Pe.allocate({refcount:1,value:t})}}};function Ae(t){if(null===t)return"null";var e=m(t);return"object"===e||"array"===e||"function"===e?t.toString():""+t}function De(t,e){switch(e){case 2:return function(t){return this.fromWireType(x[t>>2])};case 3:return function(t){return this.fromWireType(F[t>>3])};default:throw new TypeError("Unknown float type: "+t)}}function xe(t,e,r){switch(e){case 0:return r?function(t){return P[t]}:function(t){return T[t]};case 1:return r?function(t){return C[t>>1]}:function(t){return S[t>>1]};case 2:return r?function(t){return A[t>>2]}:function(t){return D[t>>2]};default:throw new TypeError("Unknown integer type: "+t)}}var Fe,Oe,je,Me="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,$e=function(t,e){for(var r=t,n=r>>1,o=n+e/2;!(n>=o)&&S[n];)++n;if((r=n<<1)-t>32&&Me)return Me.decode(T.subarray(t,r));for(var i="",a=0;!(a>=e/2);++a){var s=C[t+2*a>>1];if(0==s)break;i+=String.fromCharCode(s)}return i},Re=function(t,e,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=e,o=(r-=2)<2*t.length?r/2:t.length,i=0;i<o;++i){var a=t.charCodeAt(i);C[e>>1]=a,e+=2}return C[e>>1]=0,e-n},Le=function(t){return 2*t.length},ze=function(t,e){for(var r=0,n="";!(r>=e/4);){var o=A[t+4*r>>2];if(0==o)break;if(++r,o>=65536){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n},Ne=function(t,e,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=e,o=n+r-4,i=0;i<t.length;++i){var a=t.charCodeAt(i);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&t.charCodeAt(++i)),A[e>>2]=a,(e+=4)+4>o)break}return A[e>>2]=0,e-n},Ie=function(t){for(var e=0,r=0;r<t.length;++r){var n=t.charCodeAt(r);n>=55296&&n<=57343&&++r,e+=4}return e},Be={},Ue=[],We=[],He={},Ve=function(){if(!Ve.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==("undefined"==typeof navigator?"undefined":m(navigator))&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:u||"./this.program"};for(var e in He)void 0===He[e]?delete t[e]:t[e]=He[e];var r=[];for(var e in t)r.push("".concat(e,"=").concat(t[e]));Ve.strings=r}return Ve.strings},Ge=function(t,e,r,n){t||(t=this),this.parent=t,this.mount=t.mount,this.mounted=null,this.id=vt.nextInode++,this.name=e,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n};Object.defineProperties(Ge.prototype,{read:{get:function(){return!(365&~this.mode)},set:function(t){t?this.mode|=365:this.mode&=-366}},write:{get:function(){return!(146&~this.mode)},set:function(t){t?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return vt.isDir(this.mode)}},isDevice:{get:function(){return vt.isChrdev(this.mode)}}}),vt.FSNode=Ge,vt.createPreloadedFile=function(t,e,r,n,o,i,a,s,c,u){var f=e?rt.resolve(tt.join2(t,e)):t;function l(r){function l(r){u&&u(),s||vt.createDataFile(t,e,r,n,o,c),i&&i(),G()}(function(t,e,r,n){"undefined"!=typeof Browser&&Browser.init();var o=!1;return ht.forEach((function(i){o||i.canHandle(e)&&(i.handle(t,e,r,n),o=!0)})),o})(r,f,l,(function(){a&&a(),G()}))||l(r)}V(),"string"==typeof r?dt(r,(function(t){return l(t)}),a):l(r)},vt.staticInit(),function(){for(var t=new Array(256),e=0;e<256;++e)t[e]=String.fromCharCode(e);wt=t}(),Pt=i.BindingError=function(t){function e(t){var r;return o(this,e),(r=n(this,e,[t])).name="BindingError",r}return f(e,t),a(e)}(g(Error)),Ct=i.InternalError=function(t){function e(t){var r;return o(this,e),(r=n(this,e,[t])).name="InternalError",r}return f(e,t),a(e)}(g(Error)),Zt.prototype.isAliasOf=xt,Zt.prototype.clone=Xt,Zt.prototype.delete=Kt,Zt.prototype.isDeleted=Jt,Zt.prototype.deleteLater=Yt,i.getInheritedInstanceCount=Lt,i.getLiveInheritedInstances=zt,i.flushPendingDeletes=It,i.setDelayFunction=Ut,le.prototype.getPointee=ce,le.prototype.destructor=ue,le.prototype.argPackAdvance=8,le.prototype.readValueFromPointer=se,le.prototype.deleteObject=fe,le.prototype.fromWireType=Gt,me=i.UnboundTypeError=(Fe=Error,(je=te(Oe="UnboundTypeError",(function(t){this.name=Oe,this.message=t;var e=new Error(t).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(Fe.prototype),je.prototype.constructor=je,je.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},je),Object.assign(_e.prototype,{get:function(t){return this.allocated[t]},has:function(t){return void 0!==this.allocated[t]},allocate:function(t){var e=this.freelist.pop()||this.allocated.length;return this.allocated[e]=t,e},free:function(t){this.allocated[t]=void 0,this.freelist.push(t)}}),Pe.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),Pe.reserved=Pe.allocated.length,i.count_emval_handles=Ce;var qe={p:function(t,e,r){throw new Q(t).init(e,r),t},C:function(t,e,r){yt.varargs=r;try{var n=yt.getStreamFromFD(t);switch(e){case 0:return(o=yt.get())<0?-28:vt.createStream(n,o).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var o=yt.get();return n.flags|=o,0;case 5:return o=yt.get(),C[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return i=28,A[Je()>>2]=i,-1}}catch(t){if(void 0===vt||"ErrnoError"!==t.name)throw t;return-t.errno}var i},w:function(t,e,r,n){yt.varargs=n;try{e=yt.getStr(e),e=yt.calculateAt(t,e);var o=n?yt.get():0;return vt.open(e,r,o).fd}catch(t){if(void 0===vt||"ErrnoError"!==t.name)throw t;return-t.errno}},t:function(t,e,r,n,o){},n:function(t,e,r,n,o){var i=gt(r);Dt(t,{name:e=bt(e),fromWireType:function(t){return!!t},toWireType:function(t,e){return e?n:o},argPackAdvance:8,readValueFromPointer:function(t){var n;if(1===r)n=P;else if(2===r)n=C;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+e);n=A}return this.fromWireType(n[t>>i])},destructorFunction:null})},r:function(t,e,r,n,o,a,s,c,u,f,l,d,h){l=bt(l),a=ve(o,a),c&&(c=ve(s,c)),f&&(f=ve(u,f)),h=ve(d,h);var p=Qt(l);!function(t,e,r){i.hasOwnProperty(t)?(Tt("Cannot register public name '".concat(t,"' twice")),ee(i,t,t),i.hasOwnProperty(r)&&Tt("Cannot register multiple overloads of a function with the same number of arguments (".concat(r,")!")),i[t].overloadTable[r]=e):i[t]=e}(p,(function(){ge("Cannot construct ".concat(l," due to unbound types"),[n])})),At([t,e,r],n?[n]:[],(function(e){var r,o;e=e[0],o=n?(r=e.registeredClass).instancePrototype:Zt.prototype;var s=te(p,(function(){if(Object.getPrototypeOf(this)!==u)throw new Pt("Use 'new' to construct "+l);if(void 0===d.constructor_body)throw new Pt(l+" has no accessible constructor");var t=d.constructor_body[arguments.length];if(void 0===t)throw new Pt("Tried to invoke ctor of ".concat(l," with invalid number of parameters (").concat(arguments.length,") - expected (").concat(Object.keys(d.constructor_body).toString(),") parameters instead!"));return t.apply(this,arguments)})),u=Object.create(o,{constructor:{value:s}});s.prototype=u;var d=new re(l,s,u,h,r,a,c,f);d.baseClass&&(void 0===d.baseClass.__derivedClasses&&(d.baseClass.__derivedClasses=[]),d.baseClass.__derivedClasses.push(d));var v=new le(l,d,!0,!1,!1),m=new le(l+"*",d,!1,!1,!1),y=new le(l+" const*",d,!1,!0,!1);return Rt[t]={pointerType:m,constPointerType:y},function(t,e,r){i.hasOwnProperty(t)||St("Replacing nonexistant public symbol"),void 0!==i[t].overloadTable&&void 0!==r||(i[t]=e,i[t].argCount=r)}(p,s),[v,m,y]}))},q:function(t,e,r,n,o,i){var a=we(e,r);o=ve(n,o),At([],[t],(function(t){t=t[0];var r="constructor ".concat(t.name);if(void 0===t.registeredClass.constructor_body&&(t.registeredClass.constructor_body=[]),void 0!==t.registeredClass.constructor_body[e-1])throw new Pt("Cannot register multiple constructors with identical number of parameters (".concat(e-1,") for class '").concat(t.name,"'! Overload resolution is currently only performed using the parameter count, not actual type info!"));return t.registeredClass.constructor_body[e-1]=function(){ge("Cannot construct ".concat(t.name," due to unbound types"),a)},At([],a,(function(n){return n.splice(1,0,null),t.registeredClass.constructor_body[e-1]=ke(r,n,null,o,i),[]})),[]}))},d:function(t,e,r,n,o,i,a,s,c){var u=we(r,n);e=bt(e),i=ve(o,i),At([],[t],(function(t){t=t[0];var n="".concat(t.name,".").concat(e);function o(){ge("Cannot call ".concat(n," due to unbound types"),u)}e.startsWith("@@")&&(e=Symbol[e.substring(2)]),s&&t.registeredClass.pureVirtualFunctions.push(e);var f=t.registeredClass.instancePrototype,l=f[e];return void 0===l||void 0===l.overloadTable&&l.className!==t.name&&l.argCount===r-2?(o.argCount=r-2,o.className=t.name,f[e]=o):(ee(f,e,n),f[e].overloadTable[r-2]=o),At([],u,(function(o){var s=ke(n,o,t,i,a,c);return void 0===f[e].overloadTable?(s.argCount=r-2,f[e]=s):f[e].overloadTable[r-2]=s,[]})),[]}))},D:function(t,e){Dt(t,{name:e=bt(e),fromWireType:function(t){var e=Se.toValue(t);return Te(t),e},toWireType:function(t,e){return Se.toHandle(e)},argPackAdvance:8,readValueFromPointer:se,destructorFunction:null})},k:function(t,e,r){var n=gt(r);Dt(t,{name:e=bt(e),fromWireType:function(t){return t},toWireType:function(t,e){return e},argPackAdvance:8,readValueFromPointer:De(e,n),destructorFunction:null})},c:function(t,e,r,n,o){e=bt(e);var i=gt(r),a=function(t){return t};if(0===n){var s=32-8*r;a=function(t){return t<<s>>>s}}var c=e.includes("unsigned");Dt(t,{name:e,fromWireType:a,toWireType:c?function(t,e){return this.name,e>>>0}:function(t,e){return this.name,e},argPackAdvance:8,readValueFromPointer:xe(e,i,0!==n),destructorFunction:null})},b:function(t,e,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][e];function o(t){var e=D,r=e[t>>=2],o=e[t+1];return new n(e.buffer,o,r)}Dt(t,{name:r=bt(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},j:function(t,e){var r="std::string"===(e=bt(e));Dt(t,{name:e,fromWireType:function(t){var e,n=D[t>>2],o=t+4;if(r)for(var i=o,a=0;a<=n;++a){var s=o+a;if(a==n||0==T[s]){var c=mt(i,s-i);void 0===e?e=c:(e+=String.fromCharCode(0),e+=c),i=s+1}}else{var u=new Array(n);for(a=0;a<n;++a)u[a]=String.fromCharCode(T[o+a]);e=u.join("")}return Ke(t),e},toWireType:function(t,e){var n;e instanceof ArrayBuffer&&(e=new Uint8Array(e));var o="string"==typeof e;o||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array||Tt("Cannot pass non-string to std::string"),n=r&&o?at(e):e.length;var i=Xe(4+n+1),a=i+4;if(D[i>>2]=n,r&&o)st(e,T,a,n+1);else if(o)for(var s=0;s<n;++s){var c=e.charCodeAt(s);c>255&&(Ke(a),Tt("String has UTF-16 code units that do not fit in 8 bits")),T[a+s]=c}else for(s=0;s<n;++s)T[a+s]=e[s];return null!==t&&t.push(Ke,i),i},argPackAdvance:8,readValueFromPointer:se,destructorFunction:function(t){Ke(t)}})},f:function(t,e,r){var n,o,i,a,s;r=bt(r),2===e?(n=$e,o=Re,a=Le,i=function(){return S},s=1):4===e&&(n=ze,o=Ne,a=Ie,i=function(){return D},s=2),Dt(t,{name:r,fromWireType:function(t){for(var r,o=D[t>>2],a=i(),c=t+4,u=0;u<=o;++u){var f=t+4+u*e;if(u==o||0==a[f>>s]){var l=n(c,f-c);void 0===r?r=l:(r+=String.fromCharCode(0),r+=l),c=f+e}}return Ke(t),r},toWireType:function(t,n){"string"!=typeof n&&Tt("Cannot pass non-string to C++ string type ".concat(r));var i=a(n),c=Xe(4+i+e);return D[c>>2]=i>>s,o(n,c+4,i+e),null!==t&&t.push(Ke,c),c},argPackAdvance:8,readValueFromPointer:se,destructorFunction:function(t){Ke(t)}})},o:function(t,e){Dt(t,{isVoid:!0,name:e=bt(e),argPackAdvance:0,fromWireType:function(){},toWireType:function(t,e){}})},g:function(t,e,r,n){var o,i;(t=Ue[t])(e=Se.toValue(e),r=void 0===(i=Be[o=r])?bt(o):i,null,n)},m:Te,l:function(t,e){var r=function(t,e){for(var r,n,o,i=new Array(t),a=0;a<t;++a)i[a]=(r=D[e+4*a>>2],n="parameter "+a,o=void 0,void 0===(o=kt[r])&&Tt(n+" has unknown type "+ye(r)),o);return i}(t,e),n=r[0],o=n.name+"_$"+r.slice(1).map((function(t){return t.name})).join("_")+"$",i=We[o];if(void 0!==i)return i;for(var a=["retType"],s=[n],c="",u=0;u<t-1;++u)c+=(0!==u?", ":"")+"arg"+u,a.push("argType"+u),s.push(r[1+u]);var f="return function "+Qt("methodCaller_"+o)+"(handle, name, destructors, args) {\n",l=0;for(u=0;u<t-1;++u)f+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(l?"+"+l:"")+");\n",l+=r[u+1].argPackAdvance;for(f+="    var rv = handle[name]("+c+");\n",u=0;u<t-1;++u)r[u+1].deleteObject&&(f+="    argType"+u+".deleteObject(arg"+u+");\n");n.isVoid||(f+="    return retType.toWireType(destructors, rv);\n"),f+="};\n",a.push(f);var d,h,p=Ee(Function,a).apply(null,s);return d=p,h=Ue.length,Ue.push(d),i=h,We[o]=i,i},a:function(){q("")},e:function(){return Date.now()},v:function(){return T.length},A:function(t,e,r){return T.copyWithin(t,e,e+r)},u:function(t){T.length,q("OOM")},y:function(t,e){var r=0;return Ve().forEach((function(n,o){var i=e+r;D[t+4*o>>2]=i,function(t,e){for(var r=0;r<t.length;++r)P[0|e++]=t.charCodeAt(r);P[0|e]=0}(n,i),r+=n.length+1})),0},z:function(t,e){var r=Ve();D[t>>2]=r.length;var n=0;return r.forEach((function(t){n+=t.length+1})),D[e>>2]=n,0},i:function(t){try{var e=yt.getStreamFromFD(t);return vt.close(e),0}catch(t){if(void 0===vt||"ErrnoError"!==t.name)throw t;return t.errno}},x:function(t,e){try{var r=yt.getStreamFromFD(t),n=r.tty?2:vt.isDir(r.mode)?3:vt.isLink(r.mode)?7:4;return P[0|e]=n,C[e+2>>1]=0,U=[0,(B=0,+Math.abs(B)>=1?B>0?+Math.floor(B/4294967296)>>>0:~~+Math.ceil((B-+(~~B>>>0))/4294967296)>>>0:0)],A[e+8>>2]=U[0],A[e+12>>2]=U[1],U=[0,(B=0,+Math.abs(B)>=1?B>0?+Math.floor(B/4294967296)>>>0:~~+Math.ceil((B-+(~~B>>>0))/4294967296)>>>0:0)],A[e+16>>2]=U[0],A[e+20>>2]=U[1],0}catch(t){if(void 0===vt||"ErrnoError"!==t.name)throw t;return t.errno}},B:function(t,e,r,n){try{var o=function(t,e,r,n){for(var o=0,i=0;i<r;i++){var a=D[e>>2],s=D[e+4>>2];e+=8;var c=vt.read(t,P,a,s,n);if(c<0)return-1;if(o+=c,c<s)break}return o}(yt.getStreamFromFD(t),e,r);return D[n>>2]=o,0}catch(t){if(void 0===vt||"ErrnoError"!==t.name)throw t;return t.errno}},s:function(t,e,r,n,o){var i,a,s=(a=r)+2097152>>>0<4194305-!!(i=e)?(i>>>0)+4294967296*a:NaN;try{if(isNaN(s))return 61;var c=yt.getStreamFromFD(t);return vt.llseek(c,s,n),U=[c.position>>>0,(B=c.position,+Math.abs(B)>=1?B>0?+Math.floor(B/4294967296)>>>0:~~+Math.ceil((B-+(~~B>>>0))/4294967296)>>>0:0)],A[o>>2]=U[0],A[o+4>>2]=U[1],c.getdents&&0===s&&0===n&&(c.getdents=null),0}catch(t){if(void 0===vt||"ErrnoError"!==t.name)throw t;return t.errno}},h:function(t,e,r,n){try{var o=function(t,e,r,n){for(var o=0,i=0;i<r;i++){var a=D[e>>2],s=D[e+4>>2];e+=8;var c=vt.write(t,P,a,s,n);if(c<0)return-1;o+=c}return o}(yt.getStreamFromFD(t),e,r);return D[n>>2]=o,0}catch(t){if(void 0===vt||"ErrnoError"!==t.name)throw t;return t.errno}}};!function(){var t,r,n,o,a={a:qe};function s(t,e){var r,n=t.exports;return b=(E=n).E,r=b.buffer,i.HEAP8=P=new Int8Array(r),i.HEAP16=C=new Int16Array(r),i.HEAP32=A=new Int32Array(r),i.HEAPU8=T=new Uint8Array(r),i.HEAPU16=S=new Uint16Array(r),i.HEAPU32=D=new Uint32Array(r),i.HEAPF32=x=new Float32Array(r),i.HEAPF64=F=new Float64Array(r),O=E.H,function(t){$.unshift(t)}(E.F),G(),n}if(V(),i.instantiateWasm)try{return i.instantiateWasm(a,s)}catch(t){_("Module.instantiateWasm callback failed with error: "+t),e(t)}(t=w,r=N,n=a,o=function(t){s(t.instance)},t||"function"!=typeof WebAssembly.instantiateStreaming||X(r)||"function"!=typeof fetch?J(r,n,o):fetch(r,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,n).then(o,(function(t){return _("wasm streaming compile failed: "+t),_("falling back to ArrayBuffer instantiation"),J(r,n,o)}))}))).catch(e)}();var Xe=function(t){return(Xe=E.G)(t)},Ke=function(t){return(Ke=E.I)(t)},Je=function(){return(Je=E.J)()},Ye=function(t){return(Ye=E.K)(t)};i.__embind_initialize_bindings=function(){return(i.__embind_initialize_bindings=E.L)()};var Ze,Qe=function(t){return(Qe=E.M)(t)};function tr(){function e(){Ze||(Ze=!0,i.calledRun=!0,j||(i.noFSInit||vt.init.initialized||vt.init(),vt.ignorePermissions=!1,Y($),t(i),i.onRuntimeInitialized&&i.onRuntimeInitialized(),function(){if(i.postRun)for("function"==typeof i.postRun&&(i.postRun=[i.postRun]);i.postRun.length;)z(i.postRun.shift());Y(R)}()))}W>0||(function(){if(i.preRun)for("function"==typeof i.preRun&&(i.preRun=[i.preRun]);i.preRun.length;)L(i.preRun.shift());Y(M)}(),W>0||(i.setStatus?(i.setStatus("Running..."),setTimeout((function(){setTimeout((function(){i.setStatus("")}),1),e()}),1)):e()))}if(i.dynCall_jiji=function(t,e,r,n,o){return(i.dynCall_jiji=E.N)(t,e,r,n,o)},i._ff_h264_cabac_tables=67061,H=function t(){Ze||tr(),Ze||(H=t)},i.preInit)for("function"==typeof i.preInit&&(i.preInit=[i.preInit]);i.preInit.length>0;)i.preInit.pop()();return tr(),r.ready}),tt=Q,et=function(t){function e(t){return o(this,e),n(this,e,[tt,(null==t?void 0:t.wasmPath)?fetch(null==t?void 0:t.wasmPath).then((function(t){return t.arrayBuffer()})):void 0,null==t?void 0:t.workerMode,null==t?void 0:t.canvas,null==t?void 0:t.yuvMode])}return f(e,t),a(e)}(Y);!function(){var t="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0}();function rt(){return performance&&performance.now?Math.floor(performance.now()):Date.now()}var nt=function(){function t(e){o(this,t),this.core=e,$(this,"contextMap",new Map),$(this,"decodeProcessorMap",new WeakMap),this.addKVReportBeforeExitRoom=this.addKVReportBeforeExitRoom.bind(this),this.createDecoder=this.createDecoder.bind(this),this.core.innerEmitter.on("51",this.addKVReportBeforeExitRoom)}return a(t,[{key:"getAlias",value:function(){return"videoDecoder"}},{key:"getGroup",value:function(t){return(null==t?void 0:t.track)?"".concat(t.track.userId).concat(t.track.streamType):"*"}},{key:"getName",value:function(){return t.Name}},{key:"getValidateRule",value:function(t){return{type:this.core.enums.BASIC_TYPE.Object}}},{key:"createDecoder",value:function(t,e){switch(t){case"wasm":return new et(j(O({},e),{wasmPath:"".concat(this.core.assetsPath||".","/videodec.wasm")}));case"webCodecs":return new K;case"mse":throw new Error("mse decoder not supported yet");default:throw new Error("Unsupported decoder type")}}},{key:"start",value:function(t){var e=this;this.decodeProcessorMap.set(t.track,this.decode(t)),this.core.room.videoManager.addDecodeProcessor({processor:function(t){var r,n=t.frame,o=t.track;return(null==(r=e.decodeProcessorMap.get(o))?void 0:r({frame:n,track:o}))||n},type:3})}},{key:"decode",value:function(t){var e=this,r=this.core.rx,n=r.pipe,o=r.take,i=r.subscribe;return function(r){var a=r.frame,s=r.track;if(s!==t.track||"empty"===a.type)return a;if(e.contextMap.has(s))return e.contextMap.get(s).decode(a);var c=e.core.room.videoManager.createDecodeContext(j(O({},t),{createDecoder:e.createDecoder}));return n(c.trackDoneOB,o(1),i((function(){e.core.clearStarted(e,e.getGroup(t)),e.stop({track:s})}))),e.contextMap.set(s,c),c.decode(a)}}},{key:"stop",value:function(t){var e=this;if(null==t?void 0:t.track){var r=this.contextMap.get(t.track);r&&(r.close("stop"),this.report(r,t.track),this.contextMap.delete(t.track))}else this.contextMap.forEach((function(t,r){t.close("stop"),e.contextMap.delete(r)}));0===this.contextMap.size&&this.core.room.videoManager.removeDecodeProcessor({type:3})}},{key:"report",value:function(t,e){if(!t.isReported){t.isReported=!0;var r=rt()-t.startPerformanceTime,n=this.getRemoteTrackFreezeDuration(e,t),o=n.renderFreezeTotal,i=n.dataFreezeTotal;r&&this.core.kvStatManager.addNumber({key:514850,value:Math.floor(o/r*100),split:1}),r&&this.core.kvStatManager.addNumber({key:514851,value:Math.floor(i/r*100),split:1}),t.inputFrameCount&&this.core.kvStatManager.addNumber({key:514852,value:Math.floor(t.decodedFrameCount/t.inputFrameCount*100),split:1})}}},{key:"addKVReportBeforeExitRoom",value:function(t){var e=this;t.room===this.core.room&&this.contextMap.forEach((function(t,r){e.report(t,r)}))}},{key:"update",value:function(t){var e=this.contextMap.get(t.track);e&&("mock"!==t.type?(e.close("update"),this.contextMap.set(t.track,this.core.room.videoManager.createDecodeContext(j(O({},t),{createDecoder:this.createDecoder})))):e.mock(this.core.enums.DECODE_FAILED_ERROR_CODE.TEST))}},{key:"getRemoteTrackFreezeDuration",value:function(t,e){var r=this.core.room.badCaseDetector.getRenderFreezeMap().get("".concat(t.userId,"_").concat(t.streamType)),n=this.core.room.badCaseDetector.getDataFreezeMap().get("".concat(t.userId,"_").concat(t.streamType)),o=0;r&&r.freezeTimeline.forEach((function(t){var r=t.startTime,n=t.endTime;r>e.startPerformanceTime&&(o+=0===n?rt()-r:n-r)}));var i=0;return n&&n.durationItemList.forEach((function(t){t.startTime>e.startPerformanceTime&&(i+=t.getDuration())})),{renderFreezeTotal:o,dataFreezeTotal:i}}},{key:"destroy",value:function(){this.core.innerEmitter.off("51",this.addKVReportBeforeExitRoom)}}])}();return $(nt,"Name","TRTCVideoDecoder"),nt}));
