package controller

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// SuccessResponse 成功响应结构
type SuccessResponse struct {
	Success   bool        `json:"success" example:"true"`
	Message   string      `json:"message" example:"操作成功"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp time.Time   `json:"timestamp" example:"2023-01-01T00:00:00Z"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Success   bool      `json:"success" example:"false"`
	Error     string    `json:"error" example:"操作失败"`
	Message   string    `json:"message,omitempty" example:"详细错误信息"`
	Code      string    `json:"code,omitempty" example:"ERROR_CODE"`
	Timestamp time.Time `json:"timestamp" example:"2023-01-01T00:00:00Z"`
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total" example:"100"`
	Page       int         `json:"page" example:"1"`
	PageSize   int         `json:"page_size" example:"20"`
	TotalPages int         `json:"total_pages" example:"5"`
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(message string, data interface{}) *SuccessResponse {
	return &SuccessResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(error, message, code string) *ErrorResponse {
	return &ErrorResponse{
		Success:   false,
		Error:     error,
		Message:   message,
		Code:      code,
		Timestamp: time.Now(),
	}
}

// NewPaginationResponse 创建分页响应
func NewPaginationResponse(data interface{}, total int64, page, pageSize int) *PaginationResponse {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	return &PaginationResponse{
		Data:       data,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}
}

// SendSuccessResponse 发送成功响应
func SendSuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, NewSuccessResponse("操作成功", data))
}

// SendErrorResponse 发送错误响应
func SendErrorResponse(c *gin.Context, statusCode int, message string) {
	c.JSON(statusCode, NewErrorResponse(message, "", ""))
}
