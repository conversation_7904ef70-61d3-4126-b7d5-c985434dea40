# 腾讯云直播系统 - 前后端分离示例

## 项目简介

这是一个基于腾讯云直播服务的完整前后端分离直播系统示例，采用现代化的技术栈和整洁架构设计。

### 技术栈

**后端 (Backend)**
- Go 1.21+
- Gin Web框架
- GORM (MySQL ORM)
- Redis (缓存)
- JWT认证
- WebSocket实时通信
- 腾讯云直播SDK

**前端 (Frontend)**
- Vue 3.3+
- TypeScript 5.0+
- Vite 4.0+
- Element Plus UI组件库
- Pinia状态管理
- 腾讯云Web直播SDK

### 项目架构

```
live-streaming-platform/
├── backend/                 # Go后端服务
│   ├── cmd/server/         # 应用入口
│   ├── internal/           # 内部业务逻辑
│   │   ├── controller/     # HTTP控制器层
│   │   ├── service/        # 业务逻辑层
│   │   ├── repository/     # 数据访问层
│   │   ├── model/          # 数据模型
│   │   └── middleware/     # 中间件
│   ├── pkg/               # 可复用包
│   │   └── tencent/       # 腾讯云SDK封装
│   ├── config/            # 配置文件
│   ├── migrations/        # 数据库迁移
│   └── docs/             # API文档
├── frontend/              # Vue3前端应用
│   ├── src/
│   │   ├── components/    # 可复用组件
│   │   ├── views/        # 页面组件
│   │   ├── stores/       # Pinia状态管理
│   │   ├── api/          # API接口封装
│   │   ├── types/        # TypeScript类型定义
│   │   ├── utils/        # 工具函数
│   │   └── router/       # 路由配置
│   └── public/           # 静态资源
├── docker-compose.yml     # 本地开发环境
└── docs/                 # 项目文档
```

## 核心功能

### 用户功能
- [x] 用户注册/登录
- [x] JWT身份认证
- [x] 角色权限管理(RBAC)

### 直播功能
- [x] 创建/管理直播间
- [x] 生成推流地址
- [x] 生成播放地址
- [x] 实时直播状态监控
- [x] 直播录制和截图

### 实时互动
- [x] WebSocket实时聊天
- [x] 在线观众统计
- [x] 弹幕系统
- [x] 礼物打赏

### 管理功能
- [x] 直播间管理
- [x] 用户管理
- [x] 数据统计
- [x] 内容审核

## 腾讯云配置

### 已配置的腾讯云信息

本项目已经配置了以下腾讯云信息：

- **SecretId**: `AKIDYw4vVpsfCSSBdojNkrjMiofFYbOu9CyU`
- **SecretKey**: `HH0TfaGhsnUv5yby4tRihQsjKfkY7QSE`
- **推流域名**: `215131.push.tlivecloud.com`
- **播放域名**: `215131.liveplay.myqcloud.com`
- **地域**: `ap-guangzhou`

### 腾讯云直播服务配置

1. **推流地址格式**: `rtmp://215131.push.tlivecloud.com/live/{StreamName}?{AuthParams}`
2. **播放地址格式**:
   - RTMP: `rtmp://215131.liveplay.myqcloud.com/live/{StreamName}`
   - HLS: `http://215131.liveplay.myqcloud.com/live/{StreamName}.m3u8`
   - FLV: `http://215131.liveplay.myqcloud.com/live/{StreamName}.flv`

### 数据库配置

项目已配置腾讯云数据库：

- **MySQL**: `gz-cdb-hao8jf8p.sql.tencentcdb.com:25118`
- **Redis**: `gz-crs-juno9w6l.sql.tencentcdb.com:27226`

## 快速开始

### 环境要求

- Go 1.21+
- Node.js 18+
- 腾讯云账号和直播服务（已配置）

### 1. 克隆项目

```bash
git clone <repository-url>
cd live-streaming-platform
```

### 2. 后端设置

```bash
cd backend

# 安装依赖
go mod tidy

# 复制配置文件
cp config/.env.example config/.env

# 编辑配置文件，填入腾讯云和数据库信息
# 重要：请配置以下参数
# TENCENT_SECRET_ID=你的腾讯云SecretId
# TENCENT_SECRET_KEY=你的腾讯云SecretKey
# LIVE_PUSH_DOMAIN=你的推流域名
# LIVE_PLAY_DOMAIN=你的播放域名
# DB_HOST=数据库地址
# DB_PASSWORD=数据库密码
# REDIS_HOST=Redis地址
# REDIS_PASSWORD=Redis密码

# 启动后端服务
go run cmd/server/main.go
```

### Windows 快速测试

```bash
# 测试后端
test_backend.bat

# 测试前端
test_frontend.bat
```

### 3. 前端设置

```bash
cd frontend

# 安装依赖
npm install

# 复制配置文件
cp .env.example .env.local

# 启动开发服务器
npm run dev
```

### 4. 使用Docker Compose（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## 腾讯云配置

### 1. 开通直播服务

1. 登录腾讯云控制台
2. 开通云直播服务
3. 获取以下信息：
   - SecretId
   - SecretKey
   - 推流域名
   - 播放域名

### 2. 配置推流域名

1. 在云直播控制台添加推流域名
2. 完成域名备案和CNAME解析
3. 配置推流鉴权Key

### 3. 配置播放域名

1. 添加播放域名
2. 完成CNAME解析
3. 配置播放鉴权（可选）

## API文档

后端API文档在服务启动后可通过以下地址访问：
- Swagger UI: http://localhost:8080/swagger/index.html
- API文档: http://localhost:8080/docs

## 开发指南

### 后端开发

1. **整洁架构原则**
   - 控制器层只处理HTTP请求/响应
   - 服务层包含业务逻辑
   - 仓储层处理数据访问
   - 模型层定义数据结构

2. **错误处理**
   - 使用统一的错误响应格式
   - 实现错误包装和上下文传递
   - 记录详细的错误日志

3. **安全考虑**
   - JWT令牌验证
   - 输入参数验证
   - SQL注入防护
   - XSS攻击防护

### 前端开发

1. **组件设计**
   - 使用组合式API
   - 实现组件复用
   - 遵循单一职责原则

2. **状态管理**
   - 使用Pinia管理全局状态
   - 实现响应式数据流
   - 缓存优化策略

3. **类型安全**
   - 完整的TypeScript类型定义
   - API接口类型约束
   - 组件Props类型检查

## 部署指南

### 生产环境部署

1. **后端部署**
   ```bash
   # 构建二进制文件
   go build -o bin/server cmd/server/main.go
   
   # 使用systemd管理服务
   sudo systemctl start live-streaming-backend
   ```

2. **前端部署**
   ```bash
   # 构建生产版本
   npm run build
   
   # 部署到Nginx
   sudo cp -r dist/* /var/www/html/
   ```

3. **数据库部署**
   - 配置MySQL主从复制
   - 设置Redis集群
   - 定期备份策略

## 故障排除

### 常见问题

1. **推流失败**
   - 检查推流域名配置
   - 验证鉴权Key设置
   - 确认网络连接状态

2. **播放异常**
   - 检查播放域名解析
   - 验证流媒体格式
   - 确认CDN加速配置

3. **WebSocket连接失败**
   - 检查防火墙设置
   - 验证代理配置
   - 确认服务器负载

### 日志分析

- 后端日志: `logs/app.log`
- 前端错误: 浏览器开发者工具
- 数据库日志: MySQL错误日志
- Redis日志: Redis服务日志

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request

## 许可证

MIT License

## 项目特性

### 🚀 技术特性
- **现代化技术栈**: Go 1.21+ + Vue 3.3+ + TypeScript
- **微服务架构**: 清晰的分层架构，易于扩展和维护
- **高性能**: 基于Gin框架的高性能HTTP服务
- **实时通信**: WebSocket支持实时聊天和状态更新
- **响应式设计**: 支持PC和移动端的响应式界面
- **类型安全**: 完整的TypeScript类型定义

### 📺 直播功能
- **多协议支持**: RTMP推流，HLS/FLV播放
- **画质自适应**: 支持多种画质等级
- **实时监控**: 直播状态、观众数量实时监控
- **录制回放**: 自动录制和回放功能
- **截图功能**: 直播截图和封面生成

### 💬 互动功能
- **实时聊天**: WebSocket实时聊天系统
- **弹幕系统**: 支持弹幕显示和管理
- **礼物打赏**: 虚拟礼物系统
- **关注系统**: 用户关注和粉丝管理

### 🔐 安全特性
- **JWT认证**: 安全的用户认证机制
- **RBAC权限**: 基于角色的访问控制
- **输入验证**: 严格的输入参数验证
- **XSS防护**: 前端XSS攻击防护
- **CORS配置**: 跨域请求安全配置

### 📊 管理功能
- **用户管理**: 完整的用户管理系统
- **直播间管理**: 直播间创建、编辑、删除
- **数据统计**: 观看数据、用户行为统计
- **内容审核**: 聊天内容和直播内容审核

## 快速体验

### 方式一：使用启动脚本（推荐）

**Windows用户：**
```bash
# 启动所有服务
start.bat

# 查看服务状态
start.bat status

# 查看日志
start.bat logs

# 停止服务
start.bat stop
```

**Linux/Mac用户：**
```bash
# 给脚本执行权限
chmod +x start.sh

# 启动所有服务
./start.sh

# 查看服务状态
./start.sh status

# 查看日志
./start.sh logs

# 停止服务
./start.sh stop
```

### 方式二：手动启动

1. **启动数据库服务**
```bash
docker-compose up -d mysql redis
```

2. **启动后端服务**
```bash
cd backend
cp config/.env.example config/.env
# 编辑 .env 文件，配置数据库和腾讯云信息
go mod tidy
go run cmd/server/main.go
```

3. **启动前端服务**
```bash
cd frontend
cp .env.example .env.local
# 编辑 .env.local 文件，配置API地址
npm install
npm run dev
```

## 详细配置指南

### 腾讯云直播服务配置

#### 1. 开通云直播服务
1. 登录 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 搜索"云直播"并开通服务
3. 进入云直播控制台

#### 2. 配置推流域名
1. 在控制台选择"域名管理" > "推流域名"
2. 点击"添加域名"，输入您的推流域名
3. 完成域名备案和CNAME解析
4. 配置推流鉴权：
   - 开启推流鉴权
   - 设置主KEY和备KEY
   - 配置鉴权有效时间

#### 3. 配置播放域名
1. 在控制台选择"域名管理" > "播放域名"
2. 点击"添加域名"，输入您的播放域名
3. 完成CNAME解析
4. 可选：配置播放鉴权

#### 4. 获取API密钥
1. 访问 [API密钥管理](https://console.cloud.tencent.com/cam/capi)
2. 创建或查看SecretId和SecretKey
3. 将密钥配置到环境变量中

### 环境变量配置

#### 后端环境变量 (backend/config/.env)
```bash
# 腾讯云配置
TENCENT_SECRET_ID=your-secret-id
TENCENT_SECRET_KEY=your-secret-key
TENCENT_REGION=ap-beijing

# 直播域名配置
LIVE_PUSH_DOMAIN=push.yourdomain.com
LIVE_PLAY_DOMAIN=play.yourdomain.com
LIVE_PUSH_KEY=your-push-auth-key
LIVE_PLAY_KEY=your-play-auth-key

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-password
DB_NAME=live_streaming

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE_HOURS=24
```

#### 前端环境变量 (frontend/.env.local)
```bash
# API配置
VITE_API_BASE_URL=http://localhost:8080/api
VITE_WS_BASE_URL=ws://localhost:8080/ws

# 腾讯云配置
VITE_TENCENT_LIVE_PUSH_DOMAIN=push.yourdomain.com
VITE_TENCENT_LIVE_PLAY_DOMAIN=play.yourdomain.com
```

## 开发指南

### 后端开发

#### 项目结构说明
```
backend/
├── cmd/server/          # 应用入口
├── internal/
│   ├── controller/      # HTTP控制器层
│   ├── service/         # 业务逻辑层
│   ├── repository/      # 数据访问层
│   ├── model/          # 数据模型
│   └── middleware/     # 中间件
├── pkg/tencent/        # 腾讯云SDK封装
└── config/             # 配置管理
```

#### 添加新的API接口
1. 在 `internal/model/` 中定义数据模型
2. 在 `internal/repository/` 中实现数据访问接口
3. 在 `internal/service/` 中实现业务逻辑
4. 在 `internal/controller/` 中实现HTTP处理器
5. 在 `cmd/server/main.go` 中注册路由

#### 数据库迁移
```bash
# 创建新的迁移文件
touch backend/migrations/002_add_new_table.sql

# 重新启动服务以应用迁移
docker-compose restart backend
```

### 前端开发

#### 项目结构说明
```
frontend/src/
├── api/               # API接口封装
├── components/        # 可复用组件
├── stores/           # Pinia状态管理
├── types/            # TypeScript类型定义
├── utils/            # 工具函数
├── views/            # 页面组件
└── router/           # 路由配置
```

#### 添加新页面
1. 在 `src/views/` 中创建页面组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 如需要，在 `src/types/` 中定义类型
4. 在 `src/api/` 中添加API接口

#### 状态管理
使用Pinia进行状态管理：
```typescript
// 定义store
export const useExampleStore = defineStore('example', () => {
  const state = ref(initialState)

  const actions = {
    async fetchData() {
      // 异步操作
    }
  }

  return { state, ...actions }
})
```

## 部署指南

### Docker部署（推荐）

#### 生产环境部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd live-streaming-platform

# 2. 配置环境变量
cp backend/config/.env.example backend/config/.env
cp frontend/.env.example frontend/.env.local
# 编辑配置文件

# 3. 启动服务
docker-compose up -d

# 4. 查看服务状态
docker-compose ps
```

#### 扩展部署
```bash
# 扩展后端服务实例
docker-compose up -d --scale backend=3

# 使用外部负载均衡器
# 配置Nginx或其他负载均衡器指向多个后端实例
```

### 手动部署

#### 后端部署
```bash
# 1. 编译
cd backend
go build -o bin/server cmd/server/main.go

# 2. 配置systemd服务
sudo cp scripts/live-streaming-backend.service /etc/systemd/system/
sudo systemctl enable live-streaming-backend
sudo systemctl start live-streaming-backend
```

#### 前端部署
```bash
# 1. 构建
cd frontend
npm run build

# 2. 部署到Nginx
sudo cp -r dist/* /var/www/html/
sudo systemctl reload nginx
```

## 监控和维护

### 日志管理
- 后端日志: `backend/logs/app.log`
- 前端错误: 浏览器开发者工具
- 数据库日志: Docker容器日志
- Nginx日志: `/var/log/nginx/`

### 性能监控
```bash
# 查看容器资源使用情况
docker stats

# 查看数据库性能
docker-compose exec mysql mysqladmin processlist

# 查看Redis状态
docker-compose exec redis redis-cli info
```

### 备份策略
```bash
# 数据库备份
docker-compose exec mysql mysqldump -u root -p live_streaming > backup.sql

# Redis备份
docker-compose exec redis redis-cli save
```

## 故障排除

### 常见问题

#### 1. 推流失败
**症状**: 无法推流到服务器
**解决方案**:
- 检查推流域名DNS解析
- 验证推流鉴权Key配置
- 确认防火墙端口开放
- 检查腾讯云账户余额

#### 2. 播放异常
**症状**: 无法播放直播流
**解决方案**:
- 检查播放域名配置
- 验证流媒体格式支持
- 确认CDN加速配置
- 检查浏览器兼容性

#### 3. WebSocket连接失败
**症状**: 实时功能不工作
**解决方案**:
- 检查WebSocket端口配置
- 验证代理服务器设置
- 确认防火墙规则
- 检查SSL证书配置

#### 4. 数据库连接失败
**症状**: 后端服务启动失败
**解决方案**:
- 检查数据库服务状态
- 验证连接参数配置
- 确认数据库用户权限
- 检查网络连接

### 调试技巧

#### 后端调试
```bash
# 查看详细日志
docker-compose logs -f backend

# 进入容器调试
docker-compose exec backend sh

# 检查配置
docker-compose exec backend env | grep -E "(DB_|REDIS_|TENCENT_)"
```

#### 前端调试
```bash
# 开发模式启动
cd frontend
npm run dev

# 检查构建
npm run build

# 类型检查
npm run type-check
```

## 性能优化

### 后端优化
- 使用Redis缓存热点数据
- 实现数据库连接池
- 启用Gzip压缩
- 使用CDN加速静态资源

### 前端优化
- 代码分割和懒加载
- 图片压缩和WebP格式
- 启用浏览器缓存
- 使用Service Worker

### 数据库优化
- 创建适当的索引
- 定期清理过期数据
- 配置主从复制
- 实现读写分离

## 安全建议

### 生产环境安全
- 更改默认密码和密钥
- 启用HTTPS和WSS
- 配置防火墙规则
- 定期更新依赖包
- 实现访问日志审计
- 配置入侵检测

### 数据安全
- 定期备份数据
- 加密敏感信息
- 实现数据脱敏
- 配置访问控制

## 联系方式

如有问题，请提交Issue或联系项目维护者。

### 技术支持
- GitHub Issues: [项目Issues页面]
- 技术文档: [在线文档地址]
- 社区讨论: [讨论区地址]

### 贡献指南
欢迎提交Pull Request和Issue，请遵循以下规范：
1. 提交前请先搜索已有Issue
2. 详细描述问题和解决方案
3. 包含必要的测试用例
4. 遵循代码规范和注释要求
