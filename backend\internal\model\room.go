package model

import (
	"time"

	"gorm.io/gorm"
)

// Room 直播间模型
type Room struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 基本信息
	UserID      uint   `json:"user_id" gorm:"not null;index"`
	Title       string `json:"title" gorm:"size:200;not null" validate:"required,max=200"`
	Description string `json:"description" gorm:"size:1000"`
	Cover       string `json:"cover" gorm:"size:255"`

	// 直播配置
	StreamKey     string `json:"stream_key" gorm:"size:100;uniqueIndex;not null"`
	PushURL       string `json:"push_url" gorm:"size:500"`
	WebRTCPushURL string `json:"webrtc_push_url" gorm:"size:500"`
	PlayURL       string `json:"play_url" gorm:"size:500"`
	HLSPlayURL    string `json:"hls_play_url" gorm:"size:500"`
	FLVPlayURL    string `json:"flv_play_url" gorm:"size:500"`
	WebRTCPlayURL string `json:"webrtc_play_url" gorm:"size:500"`

	// 状态信息
	Status    int    `json:"status" gorm:"default:0"` // 0:未开播 1:直播中 2:暂停 3:结束
	IsPrivate bool   `json:"is_private" gorm:"default:false"`
	Password  string `json:"password,omitempty" gorm:"size:50"`

	// 分类标签
	CategoryID uint   `json:"category_id" gorm:"default:1"`
	Tags       string `json:"tags" gorm:"size:500"` // JSON格式存储标签数组

	// 统计信息
	ViewerCount    int `json:"viewer_count" gorm:"default:0"`
	MaxViewerCount int `json:"max_viewer_count" gorm:"default:0"`
	LikeCount      int `json:"like_count" gorm:"default:0"`
	ShareCount     int `json:"share_count" gorm:"default:0"`
	MessageCount   int `json:"message_count" gorm:"default:0"`
	GiftCount      int `json:"gift_count" gorm:"default:0"`

	// 直播时间
	StartedAt *time.Time `json:"started_at"`
	EndedAt   *time.Time `json:"ended_at"`
	Duration  int        `json:"duration" gorm:"default:0"` // 直播时长(秒)

	// 设置选项
	AllowChat    bool `json:"allow_chat" gorm:"default:true"`
	AllowGift    bool `json:"allow_gift" gorm:"default:true"`
	ChatMode     int  `json:"chat_mode" gorm:"default:0"`     // 0:所有人 1:关注者 2:禁言
	QualityLevel int  `json:"quality_level" gorm:"default:1"` // 1:流畅 2:标清 3:高清 4:超清

	// 关联关系
	User     *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Category *Category `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Messages []Message `json:"messages,omitempty" gorm:"foreignKey:RoomID"`
	Viewers  []Viewer  `json:"viewers,omitempty" gorm:"foreignKey:RoomID"`
	Records  []Record  `json:"records,omitempty" gorm:"foreignKey:RoomID"`
}

// Category 直播分类模型
type Category struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	Name        string `json:"name" gorm:"size:50;not null;uniqueIndex"`
	Description string `json:"description" gorm:"size:200"`
	Icon        string `json:"icon" gorm:"size:255"`
	Sort        int    `json:"sort" gorm:"default:0"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// 统计信息
	RoomCount int `json:"room_count" gorm:"default:0"`

	// 关联关系
	Rooms []Room `json:"rooms,omitempty" gorm:"foreignKey:CategoryID"`
}

// Message 聊天消息模型
type Message struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	RoomID  uint   `json:"room_id" gorm:"not null;index"`
	UserID  uint   `json:"user_id" gorm:"not null;index"`
	Content string `json:"content" gorm:"size:1000;not null"`
	Type    int    `json:"type" gorm:"default:1"` // 1:普通消息 2:系统消息 3:礼物消息

	// 消息属性
	IsDeleted bool   `json:"is_deleted" gorm:"default:false"`
	DeletedBy uint   `json:"deleted_by" gorm:"default:0"`
	ClientIP  string `json:"client_ip" gorm:"size:45"`

	// 关联关系
	Room *Room `json:"room,omitempty" gorm:"foreignKey:RoomID"`
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// Viewer 观众记录模型
type Viewer struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	RoomID   uint       `json:"room_id" gorm:"not null;index"`
	UserID   uint       `json:"user_id" gorm:"not null;index"`
	JoinedAt time.Time  `json:"joined_at"`
	LeftAt   *time.Time `json:"left_at"`
	Duration int        `json:"duration" gorm:"default:0"` // 观看时长(秒)
	ClientIP string     `json:"client_ip" gorm:"size:45"`

	// 关联关系
	Room *Room `json:"room,omitempty" gorm:"foreignKey:RoomID"`
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// Record 直播录制记录模型
type Record struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	RoomID   uint   `json:"room_id" gorm:"not null;index"`
	Filename string `json:"filename" gorm:"size:255;not null"`
	FileURL  string `json:"file_url" gorm:"size:500"`
	FileSize int64  `json:"file_size" gorm:"default:0"`
	Duration int    `json:"duration" gorm:"default:0"` // 录制时长(秒)
	Format   string `json:"format" gorm:"size:10;default:'mp4'"`
	Quality  string `json:"quality" gorm:"size:20;default:'720p'"`
	Status   int    `json:"status" gorm:"default:0"` // 0:录制中 1:完成 2:失败

	// 时间信息
	StartedAt  time.Time  `json:"started_at"`
	FinishedAt *time.Time `json:"finished_at"`

	// 关联关系
	Room *Room `json:"room,omitempty" gorm:"foreignKey:RoomID"`
}

// TableName 指定表名
func (Room) TableName() string {
	return "rooms"
}

func (Category) TableName() string {
	return "categories"
}

func (Message) TableName() string {
	return "messages"
}

func (Viewer) TableName() string {
	return "viewers"
}

func (Record) TableName() string {
	return "records"
}

// IsLive 检查是否正在直播
func (r *Room) IsLive() bool {
	return r.Status == 1
}

// CanJoin 检查是否可以加入直播间
func (r *Room) CanJoin(userID uint) bool {
	if r.IsPrivate && r.UserID != userID {
		return false
	}
	return r.Status == 1
}

// Start 开始直播
func (r *Room) Start() {
	now := time.Now()
	r.Status = 1
	r.StartedAt = &now
	r.ViewerCount = 0
}

// Stop 结束直播
func (r *Room) Stop() {
	now := time.Now()
	r.Status = 3
	r.EndedAt = &now
	if r.StartedAt != nil {
		r.Duration = int(now.Sub(*r.StartedAt).Seconds())
	}
	r.ViewerCount = 0
}

// AddViewer 增加观众
func (r *Room) AddViewer() {
	r.ViewerCount++
	if r.ViewerCount > r.MaxViewerCount {
		r.MaxViewerCount = r.ViewerCount
	}
}

// RemoveViewer 减少观众
func (r *Room) RemoveViewer() {
	if r.ViewerCount > 0 {
		r.ViewerCount--
	}
}

// CreateRoomRequest 创建直播间请求
type CreateRoomRequest struct {
	Title        string `json:"title" validate:"required,max=200"`
	Description  string `json:"description" validate:"max=1000"`
	CategoryID   uint   `json:"category_id" validate:"required"`
	Tags         string `json:"tags" validate:"max=500"`
	IsPrivate    bool   `json:"is_private"`
	Password     string `json:"password" validate:"max=50"`
	AllowChat    bool   `json:"allow_chat"`
	AllowGift    bool   `json:"allow_gift"`
	ChatMode     int    `json:"chat_mode" validate:"min=0,max=2"`
	QualityLevel int    `json:"quality_level" validate:"min=1,max=4"`
}

// UpdateRoomRequest 更新直播间请求
type UpdateRoomRequest struct {
	Title       string `json:"title" validate:"max=200"`
	Description string `json:"description" validate:"max=1000"`
	CategoryID  uint   `json:"category_id"`
	Tags        string `json:"tags" validate:"max=500"`
	Cover       string `json:"cover" validate:"max=255"`
}
