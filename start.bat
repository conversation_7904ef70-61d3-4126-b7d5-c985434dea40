@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 腾讯云直播平台启动脚本 (Windows)

set "action=%~1"
if "%action%"=="" set "action=start"

:: 颜色定义（Windows 10+）
for /f %%A in ('"prompt $H &echo on &for %%B in (1) do rem"') do set BS=%%A

:: 打印消息函数
:print_message
echo [INFO] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

:print_step
echo [STEP] %~1
goto :eof

:: 检查依赖
:check_dependencies
call :print_step "检查依赖..."

docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker 未安装，请先安装 Docker Desktop"
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit /b 1
)

call :print_message "依赖检查通过"
goto :eof

:: 检查环境变量文件
:check_env_files
call :print_step "检查环境变量文件..."

if not exist "backend\config\.env" (
    if exist "backend\config\.env.example" (
        call :print_warning "后端 .env 文件不存在，从 .env.example 复制"
        copy "backend\config\.env.example" "backend\config\.env" >nul
    ) else (
        call :print_error "后端 .env.example 文件不存在"
        exit /b 1
    )
)

if not exist "frontend\.env.local" (
    if exist "frontend\.env.example" (
        call :print_warning "前端 .env.local 文件不存在，从 .env.example 复制"
        copy "frontend\.env.example" "frontend\.env.local" >nul
    ) else (
        call :print_error "前端 .env.example 文件不存在"
        exit /b 1
    )
)

call :print_message "环境变量文件检查完成"
goto :eof

:: 创建必要的目录
:create_directories
call :print_step "创建必要的目录..."

if not exist "backend\logs" mkdir "backend\logs"
if not exist "backend\uploads" mkdir "backend\uploads"
if not exist "nginx\ssl" mkdir "nginx\ssl"

call :print_message "目录创建完成"
goto :eof

:: 构建和启动服务
:start_services
call :print_step "构建和启动服务..."

:: 停止现有服务
docker-compose down

:: 构建并启动服务
docker-compose up --build -d

call :print_message "服务启动完成"
goto :eof

:: 等待服务就绪
:wait_for_services
call :print_step "等待服务就绪..."

call :print_message "等待 MySQL 启动..."
:wait_mysql
docker-compose exec mysql mysqladmin ping -h"localhost" --silent >nul 2>&1
if errorlevel 1 (
    timeout /t 2 /nobreak >nul
    goto wait_mysql
)

call :print_message "等待 Redis 启动..."
:wait_redis
docker-compose exec redis redis-cli ping >nul 2>&1
if errorlevel 1 (
    timeout /t 2 /nobreak >nul
    goto wait_redis
)

call :print_message "等待后端服务启动..."
:wait_backend
curl -f http://localhost:8080/health >nul 2>&1
if errorlevel 1 (
    timeout /t 5 /nobreak >nul
    goto wait_backend
)

call :print_message "等待前端服务启动..."
:wait_frontend
curl -f http://localhost:80 >nul 2>&1
if errorlevel 1 (
    timeout /t 5 /nobreak >nul
    goto wait_frontend
)

call :print_message "所有服务已就绪"
goto :eof

:: 显示服务状态
:show_status
call :print_step "服务状态："
docker-compose ps

echo.
call :print_message "服务访问地址："
echo   前端应用: http://localhost
echo   后端API: http://localhost:8080
echo   API文档: http://localhost:8080/swagger/index.html
echo   健康检查: http://localhost:8080/health
echo.
call :print_message "数据库连接信息："
echo   MySQL: localhost:3306
echo   Redis: localhost:6379
echo.
call :print_message "默认管理员账号："
echo   用户名: admin
echo   密码: admin123456
goto :eof

:: 显示日志
:show_logs
call :print_step "显示服务日志..."
docker-compose logs -f
goto :eof

:: 停止服务
:stop_services
call :print_step "停止服务..."
docker-compose down
call :print_message "服务已停止"
goto :eof

:: 清理数据
:clean_data
call :print_step "清理数据..."
docker-compose down -v
docker system prune -f
call :print_message "数据清理完成"
goto :eof

:: 主函数
:main
if "%action%"=="start" (
    call :print_message "启动腾讯云直播平台..."
    call :check_dependencies
    if errorlevel 1 exit /b 1
    call :check_env_files
    if errorlevel 1 exit /b 1
    call :create_directories
    call :start_services
    call :wait_for_services
    call :show_status
) else if "%action%"=="stop" (
    call :stop_services
) else if "%action%"=="restart" (
    call :stop_services
    timeout /t 2 /nobreak >nul
    call :main start
) else if "%action%"=="logs" (
    call :show_logs
) else if "%action%"=="status" (
    docker-compose ps
) else if "%action%"=="clean" (
    call :clean_data
) else (
    echo 用法: %0 {start^|stop^|restart^|logs^|status^|clean}
    echo.
    echo 命令说明：
    echo   start   - 启动所有服务（默认）
    echo   stop    - 停止所有服务
    echo   restart - 重启所有服务
    echo   logs    - 查看服务日志
    echo   status  - 查看服务状态
    echo   clean   - 清理所有数据和容器
    exit /b 1
)

goto :eof

:: 执行主函数
call :main
