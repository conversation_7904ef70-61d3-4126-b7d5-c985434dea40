<template>
  <div class="webrtc-test">
    <el-card>
      <template #header>
        <h2>WebRTC 功能测试</h2>
      </template>

      <!-- 浏览器兼容性检查 -->
      <el-alert
        v-if="!browserSupport.supported"
        title="浏览器兼容性问题"
        type="error"
        :closable="false"
        show-icon
      >
        <template #default>
          <ul>
            <li v-for="issue in browserSupport.issues" :key="issue">{{ issue }}</li>
          </ul>
        </template>
      </el-alert>

      <!-- SDK 版本信息 -->
      <el-descriptions title="SDK 信息" :column="2" border>
        <el-descriptions-item label="TRTC SDK">trtc-sdk-v5</el-descriptions-item>
        <el-descriptions-item label="版本">5.11.0</el-descriptions-item>
        <el-descriptions-item label="SDKAppID">{{ sdkAppId }}</el-descriptions-item>
        <el-descriptions-item label="浏览器支持">{{ browserSupport.supported ? '✅ 支持' : '❌ 不支持' }}</el-descriptions-item>
        <el-descriptions-item label="HTTPS">{{ isHttps ? '✅ 是' : '❌ 否' }}</el-descriptions-item>
        <el-descriptions-item label="UserSig">{{ userSigStatus }}</el-descriptions-item>
      </el-descriptions>

      <!-- 设备测试 -->
      <el-divider>设备测试</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>摄像头测试</span>
            </template>
            <div>
              <el-select v-model="selectedCamera" placeholder="选择摄像头" style="width: 100%; margin-bottom: 16px;">
                <el-option
                  v-for="camera in devices.cameras"
                  :key="camera.deviceId"
                  :label="camera.label"
                  :value="camera.deviceId"
                />
              </el-select>
              <el-button @click="testCamera" :loading="testing.camera" type="primary" style="width: 100%;">
                测试摄像头
              </el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card>
            <template #header>
              <span>麦克风测试</span>
            </template>
            <div>
              <el-select v-model="selectedMicrophone" placeholder="选择麦克风" style="width: 100%; margin-bottom: 16px;">
                <el-option
                  v-for="mic in devices.microphones"
                  :key="mic.deviceId"
                  :label="mic.label"
                  :value="mic.deviceId"
                />
              </el-select>
              <el-button @click="testMicrophone" :loading="testing.microphone" type="primary" style="width: 100%;">
                测试麦克风
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 视频预览 -->
      <el-divider>视频预览</el-divider>
      <div class="video-preview">
        <video
          ref="previewVideo"
          autoplay
          muted
          playsinline
          style="width: 100%; max-width: 640px; height: 360px; background: #000; border-radius: 8px;"
        />
      </div>

      <!-- 测试结果 -->
      <el-divider>测试结果</el-divider>
      <el-timeline>
        <el-timeline-item
          v-for="(log, index) in testLogs"
          :key="index"
          :timestamp="log.timestamp"
          :type="log.type"
        >
          {{ log.message }}
        </el-timeline-item>
      </el-timeline>

      <!-- 错误信息 -->
      <el-alert
        v-if="error"
        :title="error"
        type="error"
        show-icon
        :closable="false"
        style="margin-top: 16px;"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { checkWebRTCSupport, WEBRTC_CONFIG, generateUserSig } from '@/config/webrtc'
import { DeviceManager } from '@/services/device-manager'
import TRTC from 'trtc-sdk-v5'

// 响应式数据
const browserSupport = ref(checkWebRTCSupport())
const isHttps = ref(location.protocol === 'https:')
const sdkAppId = ref(WEBRTC_CONFIG.SDK_APP_ID)
const userSigStatus = ref('未获取')
const selectedCamera = ref('')
const selectedMicrophone = ref('')
const error = ref('')
const previewVideo = ref<HTMLVideoElement>()

const devices = reactive({
  cameras: [] as any[],
  microphones: [] as any[],
  speakers: [] as any[]
})

const testing = reactive({
  camera: false,
  microphone: false
})

const testLogs = ref<Array<{
  timestamp: string
  message: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
}>>([])

// 添加日志
const addLog = (message: string, type: 'primary' | 'success' | 'warning' | 'danger' | 'info' = 'info') => {
  testLogs.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type
  })
}

// 初始化
const init = async () => {
  addLog('开始初始化 WebRTC 测试', 'primary')
  
  try {
    // 检查浏览器支持
    const supported = TRTC.isSupported()
    addLog(`浏览器支持检查: ${supported ? '✅ 支持' : '❌ 不支持'}`, supported ? 'success' : 'danger')
    
    if (!supported) {
      error.value = '当前浏览器不支持 WebRTC 功能'
      return
    }

    // 获取设备列表
    await loadDevices()

    // 测试 UserSig 获取
    await testUserSig()

    addLog('WebRTC 测试初始化完成', 'success')
  } catch (err: any) {
    error.value = err.message || '初始化失败'
    addLog(`初始化失败: ${error.value}`, 'danger')
  }
}

// 加载设备列表
const loadDevices = async () => {
  try {
    addLog('正在获取设备列表...', 'info')
    
    const cameras = await TRTC.getCameraList()
    const microphones = await TRTC.getMicrophoneList()
    const speakers = await TRTC.getSpeakerList()

    devices.cameras = cameras
    devices.microphones = microphones
    devices.speakers = speakers

    // 设置默认设备
    if (cameras.length > 0) {
      selectedCamera.value = cameras[0].deviceId
    }
    if (microphones.length > 0) {
      selectedMicrophone.value = microphones[0].deviceId
    }

    addLog(`设备列表获取成功: ${cameras.length} 个摄像头, ${microphones.length} 个麦克风`, 'success')
  } catch (err: any) {
    addLog(`获取设备列表失败: ${err.message}`, 'danger')
    throw err
  }
}

// 测试摄像头
const testCamera = async () => {
  if (!selectedCamera.value) {
    ElMessage.warning('请先选择摄像头')
    return
  }

  testing.camera = true
  addLog(`开始测试摄像头: ${selectedCamera.value}`, 'info')

  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      video: { deviceId: { exact: selectedCamera.value } },
      audio: false
    })

    if (previewVideo.value) {
      previewVideo.value.srcObject = stream
    }

    addLog('摄像头测试成功', 'success')
    ElMessage.success('摄像头测试成功')

    // 3秒后停止预览
    setTimeout(() => {
      stream.getTracks().forEach(track => track.stop())
      if (previewVideo.value) {
        previewVideo.value.srcObject = null
      }
    }, 3000)

  } catch (err: any) {
    addLog(`摄像头测试失败: ${err.message}`, 'danger')
    ElMessage.error('摄像头测试失败: ' + err.message)
  } finally {
    testing.camera = false
  }
}

// 测试麦克风
const testMicrophone = async () => {
  if (!selectedMicrophone.value) {
    ElMessage.warning('请先选择麦克风')
    return
  }

  testing.microphone = true
  addLog(`开始测试麦克风: ${selectedMicrophone.value}`, 'info')

  try {
    const stream = await navigator.mediaDevices.getUserMedia({
      video: false,
      audio: { deviceId: { exact: selectedMicrophone.value } }
    })

    addLog('麦克风测试成功', 'success')
    ElMessage.success('麦克风测试成功')

    // 立即停止音频流
    stream.getTracks().forEach(track => track.stop())

  } catch (err: any) {
    addLog(`麦克风测试失败: ${err.message}`, 'danger')
    ElMessage.error('麦克风测试失败: ' + err.message)
  } finally {
    testing.microphone = false
  }
}

// 测试 UserSig 获取
const testUserSig = async () => {
  try {
    addLog('正在测试 UserSig 获取...', 'info')
    const testUserId = 'test_user_123'
    const userSig = await generateUserSig(testUserId)

    if (userSig) {
      userSigStatus.value = '✅ 获取成功'
      addLog(`UserSig 获取成功，长度: ${userSig.length}`, 'success')
    } else {
      userSigStatus.value = '❌ 获取失败'
      addLog('UserSig 获取失败：返回空值', 'warning')
    }
  } catch (err: any) {
    userSigStatus.value = '❌ 获取失败'
    addLog(`UserSig 获取失败: ${err.message}`, 'danger')
  }
}

// 生命周期
onMounted(() => {
  init()
})
</script>

<style scoped>
.webrtc-test {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.video-preview {
  text-align: center;
  margin: 16px 0;
}

.el-timeline {
  max-height: 300px;
  overflow-y: auto;
}
</style>
