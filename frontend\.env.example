# 应用配置
VITE_APP_TITLE=腾讯云直播平台
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=基于腾讯云的直播平台

# API配置
VITE_API_BASE_URL=http://localhost:8080/api
VITE_WS_BASE_URL=ws://localhost:8080/ws

# 腾讯云配置
VITE_TENCENT_LIVE_PUSH_DOMAIN=your-push-domain.com
VITE_TENCENT_LIVE_PLAY_DOMAIN=your-play-domain.com

# 腾讯云Web直播SDK配置
VITE_TENCENT_WEB_RTC_APP_ID=your-webrtc-app-id
VITE_TENCENT_WEB_RTC_SECRET_KEY=your-webrtc-secret-key

# 开发配置
VITE_DEV_SERVER_PORT=5173
VITE_DEV_SERVER_HOST=localhost

# 调试配置
VITE_DEBUG=true
VITE_LOG_LEVEL=debug

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,mp4,mov,avi

# 功能开关
VITE_ENABLE_CHAT=true
VITE_ENABLE_GIFT=true
VITE_ENABLE_RECORD=true
VITE_ENABLE_SCREENSHOT=true
