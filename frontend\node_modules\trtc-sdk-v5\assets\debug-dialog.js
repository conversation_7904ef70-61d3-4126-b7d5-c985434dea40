!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).TRTCDebugDialog=t()}(this,(function(){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=function(e){return e&&e.Math===Math&&e},r=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof e&&e)||t("object"==typeof e&&e)||function(){return this}()||Function("return this")(),n={},i=function(e){try{return!!e()}catch(t){return!0}},o=!i((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),a=!i((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),s=a,c=Function.prototype.call,u=s?c.bind(c):function(){return c.apply(c,arguments)},l={},h={}.propertyIsEnumerable,d=Object.getOwnPropertyDescriptor,f=d&&!h.call({1:2},1);l.f=f?function(e){var t=d(this,e);return!!t&&t.enumerable}:h;var p,m,g=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},v=a,y=Function.prototype,b=y.call,_=v&&y.bind.bind(b,b),w=v?_:function(e){return function(){return b.apply(e,arguments)}},k=w,x=k({}.toString),S=k("".slice),E=function(e){return S(x(e),8,-1)},A=i,T=E,R=Object,C=w("".split),I=A((function(){return!R("z").propertyIsEnumerable(0)}))?function(e){return"String"===T(e)?C(e,""):R(e)}:R,O=function(e){return null==e},D=O,B=TypeError,L=function(e){if(D(e))throw new B("Can't call method on "+e);return e},P=I,N=L,z=function(e){return P(N(e))},U="object"==typeof document&&document.all,j=void 0===U&&void 0!==U?function(e){return"function"==typeof e||e===U}:function(e){return"function"==typeof e},F=j,M=function(e){return"object"==typeof e?null!==e:F(e)},q=r,W=j,V=function(e,t){return arguments.length<2?(r=q[e],W(r)?r:void 0):q[e]&&q[e][t];var r},H=w({}.isPrototypeOf),G=r.navigator,Z=G&&G.userAgent,Y=Z?String(Z):"",K=r,X=Y,$=K.process,J=K.Deno,Q=$&&$.versions||J&&J.version,ee=Q&&Q.v8;ee&&(m=(p=ee.split("."))[0]>0&&p[0]<4?1:+(p[0]+p[1])),!m&&X&&(!(p=X.match(/Edge\/(\d+)/))||p[1]>=74)&&(p=X.match(/Chrome\/(\d+)/))&&(m=+p[1]);var te=m,re=te,ne=i,ie=r.String,oe=!!Object.getOwnPropertySymbols&&!ne((function(){var e=Symbol("symbol detection");return!ie(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&re&&re<41})),ae=oe&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,se=V,ce=j,ue=H,le=Object,he=ae?function(e){return"symbol"==typeof e}:function(e){var t=se("Symbol");return ce(t)&&ue(t.prototype,le(e))},de=String,fe=function(e){try{return de(e)}catch(t){return"Object"}},pe=j,me=fe,ge=TypeError,ve=function(e){if(pe(e))return e;throw new ge(me(e)+" is not a function")},ye=ve,be=O,_e=function(e,t){var r=e[t];return be(r)?void 0:ye(r)},we=u,ke=j,xe=M,Se=TypeError,Ee={exports:{}},Ae=r,Te=Object.defineProperty,Re=function(e,t){try{Te(Ae,e,{value:t,configurable:!0,writable:!0})}catch(r){Ae[e]=t}return t},Ce=r,Ie=Re,Oe="__core-js_shared__",De=Ee.exports=Ce[Oe]||Ie(Oe,{});(De.versions||(De.versions=[])).push({version:"3.40.0",mode:"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.40.0/LICENSE",source:"https://github.com/zloirock/core-js"});var Be=Ee.exports,Le=Be,Pe=function(e,t){return Le[e]||(Le[e]=t||{})},Ne=L,ze=Object,Ue=function(e){return ze(Ne(e))},je=Ue,Fe=w({}.hasOwnProperty),Me=Object.hasOwn||function(e,t){return Fe(je(e),t)},qe=w,We=0,Ve=Math.random(),He=qe(1..toString),Ge=function(e){return"Symbol("+(void 0===e?"":e)+")_"+He(++We+Ve,36)},Ze=Pe,Ye=Me,Ke=Ge,Xe=oe,$e=ae,Je=r.Symbol,Qe=Ze("wks"),et=$e?Je.for||Je:Je&&Je.withoutSetter||Ke,tt=function(e){return Ye(Qe,e)||(Qe[e]=Xe&&Ye(Je,e)?Je[e]:et("Symbol."+e)),Qe[e]},rt=u,nt=M,it=he,ot=_e,at=function(e,t){var r,n;if("string"===t&&ke(r=e.toString)&&!xe(n=we(r,e)))return n;if(ke(r=e.valueOf)&&!xe(n=we(r,e)))return n;if("string"!==t&&ke(r=e.toString)&&!xe(n=we(r,e)))return n;throw new Se("Can't convert object to primitive value")},st=TypeError,ct=tt("toPrimitive"),ut=function(e,t){if(!nt(e)||it(e))return e;var r,n=ot(e,ct);if(n){if(void 0===t&&(t="default"),r=rt(n,e,t),!nt(r)||it(r))return r;throw new st("Can't convert object to primitive value")}return void 0===t&&(t="number"),at(e,t)},lt=ut,ht=he,dt=function(e){var t=lt(e,"string");return ht(t)?t:t+""},ft=M,pt=r.document,mt=ft(pt)&&ft(pt.createElement),gt=function(e){return mt?pt.createElement(e):{}},vt=gt,yt=!o&&!i((function(){return 7!==Object.defineProperty(vt("div"),"a",{get:function(){return 7}}).a})),bt=o,_t=u,wt=l,kt=g,xt=z,St=dt,Et=Me,At=yt,Tt=Object.getOwnPropertyDescriptor;n.f=bt?Tt:function(e,t){if(e=xt(e),t=St(t),At)try{return Tt(e,t)}catch(r){}if(Et(e,t))return kt(!_t(wt.f,e,t),e[t])};var Rt={},Ct=o&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),It=M,Ot=String,Dt=TypeError,Bt=function(e){if(It(e))return e;throw new Dt(Ot(e)+" is not an object")},Lt=o,Pt=yt,Nt=Ct,zt=Bt,Ut=dt,jt=TypeError,Ft=Object.defineProperty,Mt=Object.getOwnPropertyDescriptor,qt="enumerable",Wt="configurable",Vt="writable";Rt.f=Lt?Nt?function(e,t,r){if(zt(e),t=Ut(t),zt(r),"function"==typeof e&&"prototype"===t&&"value"in r&&Vt in r&&!r[Vt]){var n=Mt(e,t);n&&n[Vt]&&(e[t]=r.value,r={configurable:Wt in r?r[Wt]:n[Wt],enumerable:qt in r?r[qt]:n[qt],writable:!1})}return Ft(e,t,r)}:Ft:function(e,t,r){if(zt(e),t=Ut(t),zt(r),Pt)try{return Ft(e,t,r)}catch(n){}if("get"in r||"set"in r)throw new jt("Accessors not supported");return"value"in r&&(e[t]=r.value),e};var Ht=Rt,Gt=g,Zt=o?function(e,t,r){return Ht.f(e,t,Gt(1,r))}:function(e,t,r){return e[t]=r,e},Yt={exports:{}},Kt=o,Xt=Me,$t=Function.prototype,Jt=Kt&&Object.getOwnPropertyDescriptor,Qt=Xt($t,"name"),er={EXISTS:Qt,PROPER:Qt&&"something"===function(){}.name,CONFIGURABLE:Qt&&(!Kt||Kt&&Jt($t,"name").configurable)},tr=j,rr=Be,nr=w(Function.toString);tr(rr.inspectSource)||(rr.inspectSource=function(e){return nr(e)});var ir,or,ar,sr=rr.inspectSource,cr=j,ur=r.WeakMap,lr=cr(ur)&&/native code/.test(String(ur)),hr=Ge,dr=Pe("keys"),fr=function(e){return dr[e]||(dr[e]=hr(e))},pr={},mr=lr,gr=r,vr=M,yr=Zt,br=Me,_r=Be,wr=fr,kr=pr,xr="Object already initialized",Sr=gr.TypeError,Er=gr.WeakMap;if(mr||_r.state){var Ar=_r.state||(_r.state=new Er);Ar.get=Ar.get,Ar.has=Ar.has,Ar.set=Ar.set,ir=function(e,t){if(Ar.has(e))throw new Sr(xr);return t.facade=e,Ar.set(e,t),t},or=function(e){return Ar.get(e)||{}},ar=function(e){return Ar.has(e)}}else{var Tr=wr("state");kr[Tr]=!0,ir=function(e,t){if(br(e,Tr))throw new Sr(xr);return t.facade=e,yr(e,Tr,t),t},or=function(e){return br(e,Tr)?e[Tr]:{}},ar=function(e){return br(e,Tr)}}var Rr={set:ir,get:or,has:ar,enforce:function(e){return ar(e)?or(e):ir(e,{})},getterFor:function(e){return function(t){var r;if(!vr(t)||(r=or(t)).type!==e)throw new Sr("Incompatible receiver, "+e+" required");return r}}},Cr=w,Ir=i,Or=j,Dr=Me,Br=o,Lr=er.CONFIGURABLE,Pr=sr,Nr=Rr.enforce,zr=Rr.get,Ur=String,jr=Object.defineProperty,Fr=Cr("".slice),Mr=Cr("".replace),qr=Cr([].join),Wr=Br&&!Ir((function(){return 8!==jr((function(){}),"length",{value:8}).length})),Vr=String(String).split("String"),Hr=Yt.exports=function(e,t,r){"Symbol("===Fr(Ur(t),0,7)&&(t="["+Mr(Ur(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!Dr(e,"name")||Lr&&e.name!==t)&&(Br?jr(e,"name",{value:t,configurable:!0}):e.name=t),Wr&&r&&Dr(r,"arity")&&e.length!==r.arity&&jr(e,"length",{value:r.arity});try{r&&Dr(r,"constructor")&&r.constructor?Br&&jr(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(i){}var n=Nr(e);return Dr(n,"source")||(n.source=qr(Vr,"string"==typeof t?t:"")),e};Function.prototype.toString=Hr((function(){return Or(this)&&zr(this).source||Pr(this)}),"toString");var Gr=Yt.exports,Zr=j,Yr=Rt,Kr=Gr,Xr=Re,$r=function(e,t,r,n){n||(n={});var i=n.enumerable,o=void 0!==n.name?n.name:t;if(Zr(r)&&Kr(r,o,n),n.global)i?e[t]=r:Xr(t,r);else{try{n.unsafe?e[t]&&(i=!0):delete e[t]}catch(a){}i?e[t]=r:Yr.f(e,t,{value:r,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return e},Jr={},Qr=Math.ceil,en=Math.floor,tn=Math.trunc||function(e){var t=+e;return(t>0?en:Qr)(t)},rn=function(e){var t=+e;return t!=t||0===t?0:tn(t)},nn=rn,on=Math.max,an=Math.min,sn=function(e,t){var r=nn(e);return r<0?on(r+t,0):an(r,t)},cn=rn,un=Math.min,ln=function(e){var t=cn(e);return t>0?un(t,9007199254740991):0},hn=ln,dn=function(e){return hn(e.length)},fn=z,pn=sn,mn=dn,gn=function(e){return function(t,r,n){var i=fn(t),o=mn(i);if(0===o)return!e&&-1;var a,s=pn(n,o);if(e&&r!=r){for(;o>s;)if((a=i[s++])!=a)return!0}else for(;o>s;s++)if((e||s in i)&&i[s]===r)return e||s||0;return!e&&-1}},vn={includes:gn(!0),indexOf:gn(!1)},yn=Me,bn=z,_n=vn.indexOf,wn=pr,kn=w([].push),xn=function(e,t){var r,n=bn(e),i=0,o=[];for(r in n)!yn(wn,r)&&yn(n,r)&&kn(o,r);for(;t.length>i;)yn(n,r=t[i++])&&(~_n(o,r)||kn(o,r));return o},Sn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],En=xn,An=Sn.concat("length","prototype");Jr.f=Object.getOwnPropertyNames||function(e){return En(e,An)};var Tn={};Tn.f=Object.getOwnPropertySymbols;var Rn=V,Cn=Jr,In=Tn,On=Bt,Dn=w([].concat),Bn=Rn("Reflect","ownKeys")||function(e){var t=Cn.f(On(e)),r=In.f;return r?Dn(t,r(e)):t},Ln=Me,Pn=Bn,Nn=n,zn=Rt,Un=function(e,t,r){for(var n=Pn(t),i=zn.f,o=Nn.f,a=0;a<n.length;a++){var s=n[a];Ln(e,s)||r&&Ln(r,s)||i(e,s,o(t,s))}},jn=i,Fn=j,Mn=/#|\.prototype\./,qn=function(e,t){var r=Vn[Wn(e)];return r===Gn||r!==Hn&&(Fn(t)?jn(t):!!t)},Wn=qn.normalize=function(e){return String(e).replace(Mn,".").toLowerCase()},Vn=qn.data={},Hn=qn.NATIVE="N",Gn=qn.POLYFILL="P",Zn=qn,Yn=r,Kn=n.f,Xn=Zt,$n=$r,Jn=Re,Qn=Un,ei=Zn,ti=function(e,t){var r,n,i,o,a,s=e.target,c=e.global,u=e.stat;if(r=c?Yn:u?Yn[s]||Jn(s,{}):Yn[s]&&Yn[s].prototype)for(n in t){if(o=t[n],i=e.dontCallGetSet?(a=Kn(r,n))&&a.value:r[n],!ei(c?n:s+(u?".":"#")+n,e.forced)&&void 0!==i){if(typeof o==typeof i)continue;Qn(o,i)}(e.sham||i&&i.sham)&&Xn(o,"sham",!0),$n(r,n,o,e)}},ri={},ni=xn,ii=Sn,oi=Object.keys||function(e){return ni(e,ii)},ai=o,si=Ct,ci=Rt,ui=Bt,li=z,hi=oi;ri.f=ai&&!si?Object.defineProperties:function(e,t){ui(e);for(var r,n=li(t),i=hi(t),o=i.length,a=0;o>a;)ci.f(e,r=i[a++],n[r]);return e};var di,fi=V("document","documentElement"),pi=Bt,mi=ri,gi=Sn,vi=pr,yi=fi,bi=gt,_i="prototype",wi="script",ki=fr("IE_PROTO"),xi=function(){},Si=function(e){return"<"+wi+">"+e+"</"+wi+">"},Ei=function(e){e.write(Si("")),e.close();var t=e.parentWindow.Object;return e=null,t},Ai=function(){try{di=new ActiveXObject("htmlfile")}catch(i){}var e,t,r;Ai="undefined"!=typeof document?document.domain&&di?Ei(di):(t=bi("iframe"),r="java"+wi+":",t.style.display="none",yi.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(Si("document.F=Object")),e.close(),e.F):Ei(di);for(var n=gi.length;n--;)delete Ai[_i][gi[n]];return Ai()};vi[ki]=!0;var Ti=Object.create||function(e,t){var r;return null!==e?(xi[_i]=pi(e),r=new xi,xi[_i]=null,r[ki]=e):r=Ai(),void 0===t?r:mi.f(r,t)},Ri=tt,Ci=Ti,Ii=Rt.f,Oi=Ri("unscopables"),Di=Array.prototype;void 0===Di[Oi]&&Ii(Di,Oi,{configurable:!0,value:Ci(null)});var Bi=function(e){Di[Oi][e]=!0},Li=vn.includes,Pi=Bi;ti({target:"Array",proto:!0,forced:i((function(){return!Array(1).includes()}))},{includes:function(e){return Li(this,e,arguments.length>1?arguments[1]:void 0)}}),Pi("includes");var Ni,zi,Ui,ji={},Fi=!i((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),Mi=Me,qi=j,Wi=Ue,Vi=Fi,Hi=fr("IE_PROTO"),Gi=Object,Zi=Gi.prototype,Yi=Vi?Gi.getPrototypeOf:function(e){var t=Wi(e);if(Mi(t,Hi))return t[Hi];var r=t.constructor;return qi(r)&&t instanceof r?r.prototype:t instanceof Gi?Zi:null},Ki=i,Xi=j,$i=M,Ji=Yi,Qi=$r,eo=tt("iterator"),to=!1;[].keys&&("next"in(Ui=[].keys())?(zi=Ji(Ji(Ui)))!==Object.prototype&&(Ni=zi):to=!0);var ro=!$i(Ni)||Ki((function(){var e={};return Ni[eo].call(e)!==e}));ro&&(Ni={}),Xi(Ni[eo])||Qi(Ni,eo,(function(){return this}));var no={IteratorPrototype:Ni,BUGGY_SAFARI_ITERATORS:to},io=Rt.f,oo=Me,ao=tt("toStringTag"),so=function(e,t,r){e&&!r&&(e=e.prototype),e&&!oo(e,ao)&&io(e,ao,{configurable:!0,value:t})},co=no.IteratorPrototype,uo=Ti,lo=g,ho=so,fo=ji,po=function(){return this},mo=function(e,t,r,n){var i=t+" Iterator";return e.prototype=uo(co,{next:lo(+!n,r)}),ho(e,i,!1),fo[i]=po,e},go=w,vo=ve,yo=M,bo=function(e){return yo(e)||null===e},_o=String,wo=TypeError,ko=function(e,t,r){try{return go(vo(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(n){}},xo=M,So=L,Eo=function(e){if(bo(e))return e;throw new wo("Can't set "+_o(e)+" as a prototype")},Ao=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=ko(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(n){}return function(r,n){return So(r),Eo(n),xo(r)?(t?e(r,n):r.__proto__=n,r):r}}():void 0),To=ti,Ro=u,Co=j,Io=mo,Oo=Yi,Do=Ao,Bo=so,Lo=Zt,Po=$r,No=ji,zo=er.PROPER,Uo=er.CONFIGURABLE,jo=no.IteratorPrototype,Fo=no.BUGGY_SAFARI_ITERATORS,Mo=tt("iterator"),qo="keys",Wo="values",Vo="entries",Ho=function(){return this},Go=function(e,t,r,n,i,o,a){Io(r,t,n);var s,c,u,l=function(e){if(e===i&&m)return m;if(!Fo&&e&&e in f)return f[e];switch(e){case qo:case Wo:case Vo:return function(){return new r(this,e)}}return function(){return new r(this)}},h=t+" Iterator",d=!1,f=e.prototype,p=f[Mo]||f["@@iterator"]||i&&f[i],m=!Fo&&p||l(i),g="Array"===t&&f.entries||p;if(g&&(s=Oo(g.call(new e)))!==Object.prototype&&s.next&&(Oo(s)!==jo&&(Do?Do(s,jo):Co(s[Mo])||Po(s,Mo,Ho)),Bo(s,h,!0)),zo&&i===Wo&&p&&p.name!==Wo&&(Uo?Lo(f,"name",Wo):(d=!0,m=function(){return Ro(p,this)})),i)if(c={values:l(Wo),keys:o?m:l(qo),entries:l(Vo)},a)for(u in c)(Fo||d||!(u in f))&&Po(f,u,c[u]);else To({target:t,proto:!0,forced:Fo||d},c);return f[Mo]!==m&&Po(f,Mo,m,{name:i}),No[t]=m,c},Zo=function(e,t){return{value:e,done:t}},Yo=z,Ko=Bi,Xo=ji,$o=Rr,Jo=Rt.f,Qo=Go,ea=Zo,ta=o,ra="Array Iterator",na=$o.set,ia=$o.getterFor(ra),oa=Qo(Array,"Array",(function(e,t){na(this,{type:ra,target:Yo(e),index:0,kind:t})}),(function(){var e=ia(this),t=e.target,r=e.index++;if(!t||r>=t.length)return e.target=null,ea(void 0,!0);switch(e.kind){case"keys":return ea(r,!1);case"values":return ea(t[r],!1)}return ea([r,t[r]],!1)}),"values"),aa=Xo.Arguments=Xo.Array;if(Ko("keys"),Ko("values"),Ko("entries"),ta&&"values"!==aa.name)try{Jo(aa,"name",{value:"values"})}catch(iA){}var sa=ve,ca=Ue,ua=I,la=dn,ha=TypeError,da="Reduce of empty array with no initial value",fa=function(e){return function(t,r,n,i){var o=ca(t),a=ua(o),s=la(o);if(sa(r),0===s&&n<2)throw new ha(da);var c=e?s-1:0,u=e?-1:1;if(n<2)for(;;){if(c in a){i=a[c],c+=u;break}if(c+=u,e?c<0:s<=c)throw new ha(da)}for(;e?c>=0:s>c;c+=u)c in a&&(i=r(i,a[c],c,o));return i}},pa={left:fa(!1),right:fa(!0)},ma=i,ga=function(e,t){var r=[][e];return!!r&&ma((function(){r.call(null,t||function(){return 1},1)}))},va=r,ya=Y,ba=E,_a=function(e){return ya.slice(0,e.length)===e},wa=_a("Bun/")?"BUN":_a("Cloudflare-Workers")?"CLOUDFLARE":_a("Deno/")?"DENO":_a("Node.js/")?"NODE":va.Bun&&"string"==typeof Bun.version?"BUN":va.Deno&&"object"==typeof Deno.version?"DENO":"process"===ba(va.process)?"NODE":va.window&&va.document?"BROWSER":"REST",ka="NODE"===wa,xa=pa.left;ti({target:"Array",proto:!0,forced:!ka&&te>79&&te<83||!ga("reduce")},{reduce:function(e){var t=arguments.length;return xa(this,e,t,t>1?arguments[1]:void 0)}});var Sa="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,Ea=Gr,Aa=Rt,Ta=function(e,t,r){return r.get&&Ea(r.get,t,{getter:!0}),r.set&&Ea(r.set,t,{setter:!0}),Aa.f(e,t,r)},Ra=$r,Ca=function(e,t,r){for(var n in t)Ra(e,n,t[n],r);return e},Ia=H,Oa=TypeError,Da=function(e,t){if(Ia(t,e))return e;throw new Oa("Incorrect invocation")},Ba=rn,La=ln,Pa=RangeError,Na=function(e){if(void 0===e)return 0;var t=Ba(e),r=La(t);if(t!==r)throw new Pa("Wrong length or index");return r},za=Math.sign||function(e){var t=+e;return 0===t||t!=t?t:t<0?-1:1},Ua=4503599627370496,ja=za,Fa=function(e){return e+Ua-Ua},Ma=Math.abs,qa=function(e,t,r,n){var i=+e,o=Ma(i),a=ja(i);if(o<n)return a*Fa(o/n/t)*n*t;var s=(1+t/2220446049250313e-31)*o,c=s-(s-o);return c>r||c!=c?Infinity*a:a*c},Wa=Math.fround||function(e){return qa(e,1.1920928955078125e-7,34028234663852886e22,11754943508222875e-54)},Va=Array,Ha=Math.abs,Ga=Math.pow,Za=Math.floor,Ya=Math.log,Ka=Math.LN2,Xa={pack:function(e,t,r){var n,i,o,a=Va(r),s=8*r-t-1,c=(1<<s)-1,u=c>>1,l=23===t?Ga(2,-24)-Ga(2,-77):0,h=e<0||0===e&&1/e<0?1:0,d=0;for((e=Ha(e))!=e||Infinity===e?(i=e!=e?1:0,n=c):(n=Za(Ya(e)/Ka),e*(o=Ga(2,-n))<1&&(n--,o*=2),(e+=n+u>=1?l/o:l*Ga(2,1-u))*o>=2&&(n++,o/=2),n+u>=c?(i=0,n=c):n+u>=1?(i=(e*o-1)*Ga(2,t),n+=u):(i=e*Ga(2,u-1)*Ga(2,t),n=0));t>=8;)a[d++]=255&i,i/=256,t-=8;for(n=n<<t|i,s+=t;s>0;)a[d++]=255&n,n/=256,s-=8;return a[d-1]|=128*h,a},unpack:function(e,t){var r,n=e.length,i=8*n-t-1,o=(1<<i)-1,a=o>>1,s=i-7,c=n-1,u=e[c--],l=127&u;for(u>>=7;s>0;)l=256*l+e[c--],s-=8;for(r=l&(1<<-s)-1,l>>=-s,s+=t;s>0;)r=256*r+e[c--],s-=8;if(0===l)l=1-a;else{if(l===o)return r?NaN:u?-Infinity:Infinity;r+=Ga(2,t),l-=a}return(u?-1:1)*r*Ga(2,l-t)}},$a=Ue,Ja=sn,Qa=dn,es=function(e){for(var t=$a(this),r=Qa(t),n=arguments.length,i=Ja(n>1?arguments[1]:void 0,r),o=n>2?arguments[2]:void 0,a=void 0===o?r:Ja(o,r);a>i;)t[i++]=e;return t},ts=w([].slice),rs=j,ns=M,is=Ao,os=function(e,t,r){var n,i;return is&&rs(n=t.constructor)&&n!==r&&ns(i=n.prototype)&&i!==r.prototype&&is(e,i),e},as=r,ss=w,cs=o,us=Sa,ls=Zt,hs=Ta,ds=Ca,fs=i,ps=Da,ms=rn,gs=ln,vs=Na,ys=Wa,bs=Xa,_s=Yi,ws=Ao,ks=es,xs=ts,Ss=os,Es=Un,As=so,Ts=Rr,Rs=er.PROPER,Cs=er.CONFIGURABLE,Is="ArrayBuffer",Os="DataView",Ds="prototype",Bs="Wrong index",Ls=Ts.getterFor(Is),Ps=Ts.getterFor(Os),Ns=Ts.set,zs=as[Is],Us=zs,js=Us&&Us[Ds],Fs=as[Os],Ms=Fs&&Fs[Ds],qs=Object.prototype,Ws=as.Array,Vs=as.RangeError,Hs=ss(ks),Gs=ss([].reverse),Zs=bs.pack,Ys=bs.unpack,Ks=function(e){return[255&e]},Xs=function(e){return[255&e,e>>8&255]},$s=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},Js=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},Qs=function(e){return Zs(ys(e),23,4)},ec=function(e){return Zs(e,52,8)},tc=function(e,t,r){hs(e[Ds],t,{configurable:!0,get:function(){return r(this)[t]}})},rc=function(e,t,r,n){var i=Ps(e),o=vs(r),a=!!n;if(o+t>i.byteLength)throw new Vs(Bs);var s=i.bytes,c=o+i.byteOffset,u=xs(s,c,c+t);return a?u:Gs(u)},nc=function(e,t,r,n,i,o){var a=Ps(e),s=vs(r),c=n(+i),u=!!o;if(s+t>a.byteLength)throw new Vs(Bs);for(var l=a.bytes,h=s+a.byteOffset,d=0;d<t;d++)l[h+d]=c[u?d:t-d-1]};if(us){var ic=Rs&&zs.name!==Is;fs((function(){zs(1)}))&&fs((function(){new zs(-1)}))&&!fs((function(){return new zs,new zs(1.5),new zs(NaN),1!==zs.length||ic&&!Cs}))?ic&&Cs&&ls(zs,"name",Is):((Us=function(e){return ps(this,js),Ss(new zs(vs(e)),this,Us)})[Ds]=js,js.constructor=Us,Es(Us,zs)),ws&&_s(Ms)!==qs&&ws(Ms,qs);var oc=new Fs(new Us(2)),ac=ss(Ms.setInt8);oc.setInt8(0,2147483648),oc.setInt8(1,2147483649),!oc.getInt8(0)&&oc.getInt8(1)||ds(Ms,{setInt8:function(e,t){ac(this,e,t<<24>>24)},setUint8:function(e,t){ac(this,e,t<<24>>24)}},{unsafe:!0})}else js=(Us=function(e){ps(this,js);var t=vs(e);Ns(this,{type:Is,bytes:Hs(Ws(t),0),byteLength:t}),cs||(this.byteLength=t,this.detached=!1)})[Ds],Ms=(Fs=function(e,t,r){ps(this,Ms),ps(e,js);var n=Ls(e),i=n.byteLength,o=ms(t);if(o<0||o>i)throw new Vs("Wrong offset");if(o+(r=void 0===r?i-o:gs(r))>i)throw new Vs("Wrong length");Ns(this,{type:Os,buffer:e,byteLength:r,byteOffset:o,bytes:n.bytes}),cs||(this.buffer=e,this.byteLength=r,this.byteOffset=o)})[Ds],cs&&(tc(Us,"byteLength",Ls),tc(Fs,"buffer",Ps),tc(Fs,"byteLength",Ps),tc(Fs,"byteOffset",Ps)),ds(Ms,{getInt8:function(e){return rc(this,1,e)[0]<<24>>24},getUint8:function(e){return rc(this,1,e)[0]},getInt16:function(e){var t=rc(this,2,e,arguments.length>1&&arguments[1]);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=rc(this,2,e,arguments.length>1&&arguments[1]);return t[1]<<8|t[0]},getInt32:function(e){return Js(rc(this,4,e,arguments.length>1&&arguments[1]))},getUint32:function(e){return Js(rc(this,4,e,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(e){return Ys(rc(this,4,e,arguments.length>1&&arguments[1]),23)},getFloat64:function(e){return Ys(rc(this,8,e,arguments.length>1&&arguments[1]),52)},setInt8:function(e,t){nc(this,1,e,Ks,t)},setUint8:function(e,t){nc(this,1,e,Ks,t)},setInt16:function(e,t){nc(this,2,e,Xs,t,arguments.length>2&&arguments[2])},setUint16:function(e,t){nc(this,2,e,Xs,t,arguments.length>2&&arguments[2])},setInt32:function(e,t){nc(this,4,e,$s,t,arguments.length>2&&arguments[2])},setUint32:function(e,t){nc(this,4,e,$s,t,arguments.length>2&&arguments[2])},setFloat32:function(e,t){nc(this,4,e,Qs,t,arguments.length>2&&arguments[2])},setFloat64:function(e,t){nc(this,8,e,ec,t,arguments.length>2&&arguments[2])}});As(Us,Is),As(Fs,Os);var sc={ArrayBuffer:Us,DataView:Fs},cc=V,uc=Ta,lc=o,hc=tt("species"),dc=function(e){var t=cc(e);lc&&t&&!t[hc]&&uc(t,hc,{configurable:!0,get:function(){return this}})},fc=dc,pc="ArrayBuffer",mc=sc[pc];ti({global:!0,constructor:!0,forced:r[pc]!==mc},{ArrayBuffer:mc}),fc(pc);var gc=E,vc=w,yc=function(e){if("Function"===gc(e))return vc(e)},bc=ti,_c=yc,wc=i,kc=Bt,xc=sn,Sc=ln,Ec=sc.ArrayBuffer,Ac=sc.DataView,Tc=Ac.prototype,Rc=_c(Ec.prototype.slice),Cc=_c(Tc.getUint8),Ic=_c(Tc.setUint8);bc({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:wc((function(){return!new Ec(2).slice(1,void 0).byteLength}))},{slice:function(e,t){if(Rc&&void 0===t)return Rc(kc(this),e);for(var r=kc(this).byteLength,n=xc(e,r),i=xc(void 0===t?r:t,r),o=new Ec(Sc(i-n)),a=new Ac(this),s=new Ac(o),c=0;n<i;)Ic(s,c++,Cc(a,n++));return o}});var Oc={};Oc[tt("toStringTag")]="z";var Dc="[object z]"===String(Oc),Bc=j,Lc=E,Pc=tt("toStringTag"),Nc=Object,zc="Arguments"===Lc(function(){return arguments}()),Uc=Dc?Lc:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(iA){}}(t=Nc(e),Pc))?r:zc?Lc(t):"Object"===(n=Lc(t))&&Bc(t.callee)?"Arguments":n},jc=w,Fc=i,Mc=j,qc=Uc,Wc=sr,Vc=function(){},Hc=V("Reflect","construct"),Gc=/^\s*(?:class|function)\b/,Zc=jc(Gc.exec),Yc=!Gc.test(Vc),Kc=function(e){if(!Mc(e))return!1;try{return Hc(Vc,[],e),!0}catch(iA){return!1}},Xc=function(e){if(!Mc(e))return!1;switch(qc(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Yc||!!Zc(Gc,Wc(e))}catch(iA){return!0}};Xc.sham=!0;var $c,Jc,Qc,eu,tu=!Hc||Fc((function(){var e;return Kc(Kc.call)||!Kc(Object)||!Kc((function(){e=!0}))||e}))?Xc:Kc,ru=tu,nu=fe,iu=TypeError,ou=function(e){if(ru(e))return e;throw new iu(nu(e)+" is not a constructor")},au=Bt,su=ou,cu=O,uu=tt("species"),lu=function(e,t){var r,n=au(e).constructor;return void 0===n||cu(r=au(n)[uu])?t:su(r)},hu=a,du=Function.prototype,fu=du.apply,pu=du.call,mu="object"==typeof Reflect&&Reflect.apply||(hu?pu.bind(fu):function(){return pu.apply(fu,arguments)}),gu=ve,vu=a,yu=yc(yc.bind),bu=function(e,t){return gu(e),void 0===t?e:vu?yu(e,t):function(){return e.apply(t,arguments)}},_u=TypeError,wu=function(e,t){if(e<t)throw new _u("Not enough arguments");return e},ku=/(?:ipad|iphone|ipod).*applewebkit/i.test(Y),xu=r,Su=mu,Eu=bu,Au=j,Tu=Me,Ru=i,Cu=fi,Iu=ts,Ou=gt,Du=wu,Bu=ku,Lu=ka,Pu=xu.setImmediate,Nu=xu.clearImmediate,zu=xu.process,Uu=xu.Dispatch,ju=xu.Function,Fu=xu.MessageChannel,Mu=xu.String,qu=0,Wu={},Vu="onreadystatechange";Ru((function(){$c=xu.location}));var Hu=function(e){if(Tu(Wu,e)){var t=Wu[e];delete Wu[e],t()}},Gu=function(e){return function(){Hu(e)}},Zu=function(e){Hu(e.data)},Yu=function(e){xu.postMessage(Mu(e),$c.protocol+"//"+$c.host)};Pu&&Nu||(Pu=function(e){Du(arguments.length,1);var t=Au(e)?e:ju(e),r=Iu(arguments,1);return Wu[++qu]=function(){Su(t,void 0,r)},Jc(qu),qu},Nu=function(e){delete Wu[e]},Lu?Jc=function(e){zu.nextTick(Gu(e))}:Uu&&Uu.now?Jc=function(e){Uu.now(Gu(e))}:Fu&&!Bu?(eu=(Qc=new Fu).port2,Qc.port1.onmessage=Zu,Jc=Eu(eu.postMessage,eu)):xu.addEventListener&&Au(xu.postMessage)&&!xu.importScripts&&$c&&"file:"!==$c.protocol&&!Ru(Yu)?(Jc=Yu,xu.addEventListener("message",Zu,!1)):Jc=Vu in Ou("script")?function(e){Cu.appendChild(Ou("script"))[Vu]=function(){Cu.removeChild(this),Hu(e)}}:function(e){setTimeout(Gu(e),0)});var Ku={set:Pu,clear:Nu},Xu=r,$u=o,Ju=Object.getOwnPropertyDescriptor,Qu=function(e){if(!$u)return Xu[e];var t=Ju(Xu,e);return t&&t.value},el=function(){this.head=null,this.tail=null};el.prototype={add:function(e){var t={item:e,next:null},r=this.tail;r?r.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}};var tl,rl,nl,il,ol,al=el,sl=/ipad|iphone|ipod/i.test(Y)&&"undefined"!=typeof Pebble,cl=/web0s(?!.*chrome)/i.test(Y),ul=r,ll=Qu,hl=bu,dl=Ku.set,fl=al,pl=ku,ml=sl,gl=cl,vl=ka,yl=ul.MutationObserver||ul.WebKitMutationObserver,bl=ul.document,_l=ul.process,wl=ul.Promise,kl=ll("queueMicrotask");if(!kl){var xl=new fl,Sl=function(){var e,t;for(vl&&(e=_l.domain)&&e.exit();t=xl.get();)try{t()}catch(iA){throw xl.head&&tl(),iA}e&&e.enter()};pl||vl||gl||!yl||!bl?!ml&&wl&&wl.resolve?((il=wl.resolve(void 0)).constructor=wl,ol=hl(il.then,il),tl=function(){ol(Sl)}):vl?tl=function(){_l.nextTick(Sl)}:(dl=hl(dl,ul),tl=function(){dl(Sl)}):(rl=!0,nl=bl.createTextNode(""),new yl(Sl).observe(nl,{characterData:!0}),tl=function(){nl.data=rl=!rl}),kl=function(e){xl.head||tl(),xl.add(e)}}var El=kl,Al=function(e){try{return{error:!1,value:e()}}catch(iA){return{error:!0,value:iA}}},Tl=r.Promise,Rl=r,Cl=Tl,Il=j,Ol=Zn,Dl=sr,Bl=tt,Ll=wa,Pl=te;Cl&&Cl.prototype;var Nl=Bl("species"),zl=!1,Ul=Il(Rl.PromiseRejectionEvent),jl=Ol("Promise",(function(){var e=Dl(Cl),t=e!==String(Cl);if(!t&&66===Pl)return!0;if(!Pl||Pl<51||!/native code/.test(e)){var r=new Cl((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};if((r.constructor={})[Nl]=n,!(zl=r.then((function(){}))instanceof n))return!0}return!(t||"BROWSER"!==Ll&&"DENO"!==Ll||Ul)})),Fl={CONSTRUCTOR:jl,REJECTION_EVENT:Ul,SUBCLASSING:zl},Ml={},ql=ve,Wl=TypeError,Vl=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw new Wl("Bad Promise constructor");t=e,r=n})),this.resolve=ql(t),this.reject=ql(r)};Ml.f=function(e){return new Vl(e)};var Hl,Gl,Zl,Yl=ti,Kl=ka,Xl=r,$l=u,Jl=$r,Ql=Ao,eh=so,th=dc,rh=ve,nh=j,ih=M,oh=Da,ah=lu,sh=Ku.set,ch=El,uh=function(e,t){try{1===arguments.length?console.error(e):console.error(e,t)}catch(iA){}},lh=Al,hh=al,dh=Rr,fh=Tl,ph=Ml,mh="Promise",gh=Fl.CONSTRUCTOR,vh=Fl.REJECTION_EVENT,yh=Fl.SUBCLASSING,bh=dh.getterFor(mh),_h=dh.set,wh=fh&&fh.prototype,kh=fh,xh=wh,Sh=Xl.TypeError,Eh=Xl.document,Ah=Xl.process,Th=ph.f,Rh=Th,Ch=!!(Eh&&Eh.createEvent&&Xl.dispatchEvent),Ih="unhandledrejection",Oh=function(e){var t;return!(!ih(e)||!nh(t=e.then))&&t},Dh=function(e,t){var r,n,i,o=t.value,a=1===t.state,s=a?e.ok:e.fail,c=e.resolve,u=e.reject,l=e.domain;try{s?(a||(2===t.rejection&&zh(t),t.rejection=1),!0===s?r=o:(l&&l.enter(),r=s(o),l&&(l.exit(),i=!0)),r===e.promise?u(new Sh("Promise-chain cycle")):(n=Oh(r))?$l(n,r,c,u):c(r)):u(o)}catch(iA){l&&!i&&l.exit(),u(iA)}},Bh=function(e,t){e.notified||(e.notified=!0,ch((function(){for(var r,n=e.reactions;r=n.get();)Dh(r,e);e.notified=!1,t&&!e.rejection&&Ph(e)})))},Lh=function(e,t,r){var n,i;Ch?((n=Eh.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),Xl.dispatchEvent(n)):n={promise:t,reason:r},!vh&&(i=Xl["on"+e])?i(n):e===Ih&&uh("Unhandled promise rejection",r)},Ph=function(e){$l(sh,Xl,(function(){var t,r=e.facade,n=e.value;if(Nh(e)&&(t=lh((function(){Kl?Ah.emit("unhandledRejection",n,r):Lh(Ih,r,n)})),e.rejection=Kl||Nh(e)?2:1,t.error))throw t.value}))},Nh=function(e){return 1!==e.rejection&&!e.parent},zh=function(e){$l(sh,Xl,(function(){var t=e.facade;Kl?Ah.emit("rejectionHandled",t):Lh("rejectionhandled",t,e.value)}))},Uh=function(e,t,r){return function(n){e(t,n,r)}},jh=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,Bh(e,!0))},Fh=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw new Sh("Promise can't be resolved itself");var n=Oh(t);n?ch((function(){var r={done:!1};try{$l(n,t,Uh(Fh,r,e),Uh(jh,r,e))}catch(iA){jh(r,iA,e)}})):(e.value=t,e.state=1,Bh(e,!1))}catch(iA){jh({done:!1},iA,e)}}};if(gh&&(xh=(kh=function(e){oh(this,xh),rh(e),$l(Hl,this);var t=bh(this);try{e(Uh(Fh,t),Uh(jh,t))}catch(iA){jh(t,iA)}}).prototype,(Hl=function(e){_h(this,{type:mh,done:!1,notified:!1,parent:!1,reactions:new hh,rejection:!1,state:0,value:null})}).prototype=Jl(xh,"then",(function(e,t){var r=bh(this),n=Th(ah(this,kh));return r.parent=!0,n.ok=!nh(e)||e,n.fail=nh(t)&&t,n.domain=Kl?Ah.domain:void 0,0===r.state?r.reactions.add(n):ch((function(){Dh(n,r)})),n.promise})),Gl=function(){var e=new Hl,t=bh(e);this.promise=e,this.resolve=Uh(Fh,t),this.reject=Uh(jh,t)},ph.f=Th=function(e){return e===kh||undefined===e?new Gl(e):Rh(e)},nh(fh)&&wh!==Object.prototype)){Zl=wh.then,yh||Jl(wh,"then",(function(e,t){var r=this;return new kh((function(e,t){$l(Zl,r,e,t)})).then(e,t)}),{unsafe:!0});try{delete wh.constructor}catch(iA){}Ql&&Ql(wh,xh)}Yl({global:!0,constructor:!0,wrap:!0,forced:gh},{Promise:kh}),eh(kh,mh,!1),th(mh);var Mh=ji,qh=tt("iterator"),Wh=Array.prototype,Vh=function(e){return void 0!==e&&(Mh.Array===e||Wh[qh]===e)},Hh=Uc,Gh=_e,Zh=O,Yh=ji,Kh=tt("iterator"),Xh=function(e){if(!Zh(e))return Gh(e,Kh)||Gh(e,"@@iterator")||Yh[Hh(e)]},$h=u,Jh=ve,Qh=Bt,ed=fe,td=Xh,rd=TypeError,nd=function(e,t){var r=arguments.length<2?td(e):t;if(Jh(r))return Qh($h(r,e));throw new rd(ed(e)+" is not iterable")},id=u,od=Bt,ad=_e,sd=function(e,t,r){var n,i;od(e);try{if(!(n=ad(e,"return"))){if("throw"===t)throw r;return r}n=id(n,e)}catch(iA){i=!0,n=iA}if("throw"===t)throw r;if(i)throw n;return od(n),r},cd=bu,ud=u,ld=Bt,hd=fe,dd=Vh,fd=dn,pd=H,md=nd,gd=Xh,vd=sd,yd=TypeError,bd=function(e,t){this.stopped=e,this.result=t},_d=bd.prototype,wd=function(e,t,r){var n,i,o,a,s,c,u,l=r&&r.that,h=!(!r||!r.AS_ENTRIES),d=!(!r||!r.IS_RECORD),f=!(!r||!r.IS_ITERATOR),p=!(!r||!r.INTERRUPTED),m=cd(t,l),g=function(e){return n&&vd(n,"normal",e),new bd(!0,e)},v=function(e){return h?(ld(e),p?m(e[0],e[1],g):m(e[0],e[1])):p?m(e,g):m(e)};if(d)n=e.iterator;else if(f)n=e;else{if(!(i=gd(e)))throw new yd(hd(e)+" is not iterable");if(dd(i)){for(o=0,a=fd(e);a>o;o++)if((s=v(e[o]))&&pd(_d,s))return s;return new bd(!1)}n=md(e,i)}for(c=d?e.next:n.next;!(u=ud(c,n)).done;){try{s=v(u.value)}catch(iA){vd(n,"throw",iA)}if("object"==typeof s&&s&&pd(_d,s))return s}return new bd(!1)},kd=tt("iterator"),xd=!1;try{var Sd=0,Ed={next:function(){return{done:!!Sd++}},return:function(){xd=!0}};Ed[kd]=function(){return this},Array.from(Ed,(function(){throw 2}))}catch(iA){}var Ad=function(e,t){try{if(!t&&!xd)return!1}catch(iA){return!1}var r=!1;try{var n={};n[kd]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(iA){}return r},Td=Tl,Rd=Fl.CONSTRUCTOR||!Ad((function(e){Td.all(e).then(void 0,(function(){}))})),Cd=u,Id=ve,Od=Ml,Dd=Al,Bd=wd;ti({target:"Promise",stat:!0,forced:Rd},{all:function(e){var t=this,r=Od.f(t),n=r.resolve,i=r.reject,o=Dd((function(){var r=Id(t.resolve),o=[],a=0,s=1;Bd(e,(function(e){var c=a++,u=!1;s++,Cd(r,t,e).then((function(e){u||(u=!0,o[c]=e,--s||n(o))}),i)})),--s||n(o)}));return o.error&&i(o.value),r.promise}});var Ld=ti,Pd=Fl.CONSTRUCTOR,Nd=Tl,zd=V,Ud=j,jd=$r,Fd=Nd&&Nd.prototype;if(Ld({target:"Promise",proto:!0,forced:Pd,real:!0},{catch:function(e){return this.then(void 0,e)}}),Ud(Nd)){var Md=zd("Promise").prototype.catch;Fd.catch!==Md&&jd(Fd,"catch",Md,{unsafe:!0})}var qd=u,Wd=ve,Vd=Ml,Hd=Al,Gd=wd;ti({target:"Promise",stat:!0,forced:Rd},{race:function(e){var t=this,r=Vd.f(t),n=r.reject,i=Hd((function(){var i=Wd(t.resolve);Gd(e,(function(e){qd(i,t,e).then(r.resolve,n)}))}));return i.error&&n(i.value),r.promise}});var Zd=Ml;ti({target:"Promise",stat:!0,forced:Fl.CONSTRUCTOR},{reject:function(e){var t=Zd.f(this);return(0,t.reject)(e),t.promise}});var Yd=Bt,Kd=M,Xd=Ml,$d=function(e,t){if(Yd(e),Kd(t)&&t.constructor===e)return t;var r=Xd.f(e);return(0,r.resolve)(t),r.promise},Jd=ti,Qd=Fl.CONSTRUCTOR,ef=$d;V("Promise"),Jd({target:"Promise",stat:!0,forced:Qd},{resolve:function(e){return ef(this,e)}});var tf=ti,rf=Tl,nf=i,of=V,af=j,sf=lu,cf=$d,uf=$r,lf=rf&&rf.prototype;if(tf({target:"Promise",proto:!0,real:!0,forced:!!rf&&nf((function(){lf.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=sf(this,of("Promise")),r=af(e);return this.then(r?function(r){return cf(t,e()).then((function(){return r}))}:e,r?function(r){return cf(t,e()).then((function(){throw r}))}:e)}}),af(rf)){var hf=of("Promise").prototype.finally;lf.finally!==hf&&uf(lf,"finally",hf,{unsafe:!0})}var df=M,ff=E,pf=tt("match"),mf=Uc,gf=String,vf=function(e){if("Symbol"===mf(e))throw new TypeError("Cannot convert a Symbol value to a string");return gf(e)},yf=Bt,bf=function(){var e=yf(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t},_f=u,wf=Me,kf=H,xf=bf,Sf=RegExp.prototype,Ef=i,Af=r.RegExp,Tf=Ef((function(){var e=Af("a","y");return e.lastIndex=2,null!==e.exec("abcd")})),Rf=Tf||Ef((function(){return!Af("a","y").sticky})),Cf=Tf||Ef((function(){var e=Af("^r","gy");return e.lastIndex=2,null!==e.exec("str")})),If={BROKEN_CARET:Cf,MISSED_STICKY:Rf,UNSUPPORTED_Y:Tf},Of=Rt.f,Df=i,Bf=r.RegExp,Lf=Df((function(){var e=Bf(".","s");return!(e.dotAll&&e.test("\n")&&"s"===e.flags)})),Pf=i,Nf=r.RegExp,zf=Pf((function(){var e=Nf("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})),Uf=o,jf=r,Ff=w,Mf=Zn,qf=os,Wf=Zt,Vf=Ti,Hf=Jr.f,Gf=H,Zf=function(e){var t;return df(e)&&(void 0!==(t=e[pf])?!!t:"RegExp"===ff(e))},Yf=vf,Kf=function(e){var t=e.flags;return void 0!==t||"flags"in Sf||wf(e,"flags")||!kf(Sf,e)?t:_f(xf,e)},Xf=If,$f=function(e,t,r){r in e||Of(e,r,{configurable:!0,get:function(){return t[r]},set:function(e){t[r]=e}})},Jf=$r,Qf=i,ep=Me,tp=Rr.enforce,rp=dc,np=Lf,ip=zf,op=tt("match"),ap=jf.RegExp,sp=ap.prototype,cp=jf.SyntaxError,up=Ff(sp.exec),lp=Ff("".charAt),hp=Ff("".replace),dp=Ff("".indexOf),fp=Ff("".slice),pp=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,mp=/a/g,gp=/a/g,vp=new ap(mp)!==mp,yp=Xf.MISSED_STICKY,bp=Xf.UNSUPPORTED_Y,_p=Uf&&(!vp||yp||np||ip||Qf((function(){return gp[op]=!1,ap(mp)!==mp||ap(gp)===gp||"/a/i"!==String(ap(mp,"i"))})));if(Mf("RegExp",_p)){for(var wp=function(e,t){var r,n,i,o,a,s,c=Gf(sp,this),u=Zf(e),l=void 0===t,h=[],d=e;if(!c&&u&&l&&e.constructor===wp)return e;if((u||Gf(sp,e))&&(e=e.source,l&&(t=Kf(d))),e=void 0===e?"":Yf(e),t=void 0===t?"":Yf(t),d=e,np&&"dotAll"in mp&&(n=!!t&&dp(t,"s")>-1)&&(t=hp(t,/s/g,"")),r=t,yp&&"sticky"in mp&&(i=!!t&&dp(t,"y")>-1)&&bp&&(t=hp(t,/y/g,"")),ip&&(o=function(e){for(var t,r=e.length,n=0,i="",o=[],a=Vf(null),s=!1,c=!1,u=0,l="";n<=r;n++){if("\\"===(t=lp(e,n)))t+=lp(e,++n);else if("]"===t)s=!1;else if(!s)switch(!0){case"["===t:s=!0;break;case"("===t:if(i+=t,"?:"===fp(e,n+1,n+3))continue;up(pp,fp(e,n+1))&&(n+=2,c=!0),u++;continue;case">"===t&&c:if(""===l||ep(a,l))throw new cp("Invalid capture group name");a[l]=!0,o[o.length]=[l,u],c=!1,l="";continue}c?l+=t:i+=t}return[i,o]}(e),e=o[0],h=o[1]),a=qf(ap(e,t),c?this:sp,wp),(n||i||h.length)&&(s=tp(a),n&&(s.dotAll=!0,s.raw=wp(function(e){for(var t,r=e.length,n=0,i="",o=!1;n<=r;n++)"\\"!==(t=lp(e,n))?o||"."!==t?("["===t?o=!0:"]"===t&&(o=!1),i+=t):i+="[\\s\\S]":i+=t+lp(e,++n);return i}(e),r)),i&&(s.sticky=!0),h.length&&(s.groups=h)),e!==d)try{Wf(a,"source",""===d?"(?:)":d)}catch(iA){}return a},kp=Hf(ap),xp=0;kp.length>xp;)$f(wp,ap,kp[xp++]);sp.constructor=wp,wp.prototype=sp,Jf(jf,"RegExp",wp,{constructor:!0})}rp("RegExp");var Sp=u,Ep=w,Ap=vf,Tp=bf,Rp=If,Cp=Ti,Ip=Rr.get,Op=Lf,Dp=zf,Bp=Pe("native-string-replace",String.prototype.replace),Lp=RegExp.prototype.exec,Pp=Lp,Np=Ep("".charAt),zp=Ep("".indexOf),Up=Ep("".replace),jp=Ep("".slice),Fp=function(){var e=/a/,t=/b*/g;return Sp(Lp,e,"a"),Sp(Lp,t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),Mp=Rp.BROKEN_CARET,qp=void 0!==/()??/.exec("")[1];(Fp||qp||Mp||Op||Dp)&&(Pp=function(e){var t,r,n,i,o,a,s,c=this,u=Ip(c),l=Ap(e),h=u.raw;if(h)return h.lastIndex=c.lastIndex,t=Sp(Pp,h,l),c.lastIndex=h.lastIndex,t;var d=u.groups,f=Mp&&c.sticky,p=Sp(Tp,c),m=c.source,g=0,v=l;if(f&&(p=Up(p,"y",""),-1===zp(p,"g")&&(p+="g"),v=jp(l,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==Np(l,c.lastIndex-1))&&(m="(?: "+m+")",v=" "+v,g++),r=new RegExp("^(?:"+m+")",p)),qp&&(r=new RegExp("^"+m+"$(?!\\s)",p)),Fp&&(n=c.lastIndex),i=Sp(Lp,f?r:c,v),f?i?(i.input=jp(i.input,g),i[0]=jp(i[0],g),i.index=c.lastIndex,c.lastIndex+=i[0].length):c.lastIndex=0:Fp&&i&&(c.lastIndex=c.global?i.index+i[0].length:n),qp&&i&&i.length>1&&Sp(Bp,i[0],r,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(i[o]=void 0)})),i&&d)for(i.groups=a=Cp(null),o=0;o<d.length;o++)a[(s=d[o])[0]]=i[s[1]];return i});var Wp=Pp;ti({target:"RegExp",proto:!0,forced:/./.exec!==Wp},{exec:Wp});var Vp=o,Hp=Ta,Gp=bf,Zp=i,Yp=r.RegExp,Kp=Yp.prototype,Xp=Vp&&Zp((function(){var e=!0;try{Yp(".","d")}catch(iA){e=!1}var t={},r="",n=e?"dgimsy":"gimsy",i=function(e,n){Object.defineProperty(t,e,{get:function(){return r+=n,!0}})},o={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var a in e&&(o.hasIndices="d"),o)i(a,o[a]);return Object.getOwnPropertyDescriptor(Kp,"flags").get.call(t)!==n||r!==n}));Xp&&Hp(Kp,"flags",{configurable:!0,get:Gp});var $p=u,Jp=$r,Qp=Wp,em=i,tm=tt,rm=Zt,nm=tm("species"),im=RegExp.prototype,om=w,am=rn,sm=vf,cm=L,um=om("".charAt),lm=om("".charCodeAt),hm=om("".slice),dm=function(e){return function(t,r){var n,i,o=sm(cm(t)),a=am(r),s=o.length;return a<0||a>=s?e?"":void 0:(n=lm(o,a))<55296||n>56319||a+1===s||(i=lm(o,a+1))<56320||i>57343?e?um(o,a):n:e?hm(o,a,a+2):i-56320+(n-55296<<10)+65536}},fm={codeAt:dm(!1),charAt:dm(!0)},pm=fm.charAt,mm=w,gm=Ue,vm=Math.floor,ym=mm("".charAt),bm=mm("".replace),_m=mm("".slice),wm=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,km=/\$([$&'`]|\d{1,2})/g,xm=u,Sm=Bt,Em=j,Am=E,Tm=Wp,Rm=TypeError,Cm=mu,Im=u,Om=w,Dm=function(e,t,r,n){var i=tm(e),o=!em((function(){var t={};return t[i]=function(){return 7},7!==""[e](t)})),a=o&&!em((function(){var t=!1,r=/a/;return"split"===e&&((r={}).constructor={},r.constructor[nm]=function(){return r},r.flags="",r[i]=/./[i]),r.exec=function(){return t=!0,null},r[i](""),!t}));if(!o||!a||r){var s=/./[i],c=t(i,""[e],(function(e,t,r,n,i){var a=t.exec;return a===Qp||a===im.exec?o&&!i?{done:!0,value:$p(s,t,r,n)}:{done:!0,value:$p(e,r,t,n)}:{done:!1}}));Jp(String.prototype,e,c[0]),Jp(im,i,c[1])}n&&rm(im[i],"sham",!0)},Bm=i,Lm=Bt,Pm=j,Nm=O,zm=rn,Um=ln,jm=vf,Fm=L,Mm=function(e,t,r){return t+(r?pm(e,t).length:1)},qm=_e,Wm=function(e,t,r,n,i,o){var a=r+e.length,s=n.length,c=km;return void 0!==i&&(i=gm(i),c=wm),bm(o,c,(function(o,c){var u;switch(ym(c,0)){case"$":return"$";case"&":return e;case"`":return _m(t,0,r);case"'":return _m(t,a);case"<":u=i[_m(c,1,-1)];break;default:var l=+c;if(0===l)return o;if(l>s){var h=vm(l/10);return 0===h?o:h<=s?void 0===n[h-1]?ym(c,1):n[h-1]+ym(c,1):o}u=n[l-1]}return void 0===u?"":u}))},Vm=function(e,t){var r=e.exec;if(Em(r)){var n=xm(r,e,t);return null!==n&&Sm(n),n}if("RegExp"===Am(e))return xm(Tm,e,t);throw new Rm("RegExp#exec called on incompatible receiver")},Hm=tt("replace"),Gm=Math.max,Zm=Math.min,Ym=Om([].concat),Km=Om([].push),Xm=Om("".indexOf),$m=Om("".slice),Jm="$0"==="a".replace(/./,"$0"),Qm=!!/./[Hm]&&""===/./[Hm]("a","$0"),eg=!Bm((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}));Dm("replace",(function(e,t,r){var n=Qm?"$":"$0";return[function(e,r){var n=Fm(this),i=Nm(e)?void 0:qm(e,Hm);return i?Im(i,e,n,r):Im(t,jm(n),e,r)},function(e,i){var o=Lm(this),a=jm(e);if("string"==typeof i&&-1===Xm(i,n)&&-1===Xm(i,"$<")){var s=r(t,o,a,i);if(s.done)return s.value}var c=Pm(i);c||(i=jm(i));var u,l=o.global;l&&(u=o.unicode,o.lastIndex=0);for(var h,d=[];null!==(h=Vm(o,a))&&(Km(d,h),l);){""===jm(h[0])&&(o.lastIndex=Mm(a,Um(o.lastIndex),u))}for(var f,p="",m=0,g=0;g<d.length;g++){for(var v,y=jm((h=d[g])[0]),b=Gm(Zm(zm(h.index),a.length),0),_=[],w=1;w<h.length;w++)Km(_,void 0===(f=h[w])?f:String(f));var k=h.groups;if(c){var x=Ym([y],_,b,a);void 0!==k&&Km(x,k),v=jm(Cm(i,void 0,x))}else v=Wm(y,a,b,_,k,i);b>=m&&(p+=$m(a,m,b)+v,m=b+y.length)}return p+$m(a,m)}]}),!eg||!Jm||Qm);var tg,rg,ng,ig={exports:{}},og=Sa,ag=o,sg=r,cg=j,ug=M,lg=Me,hg=Uc,dg=fe,fg=Zt,pg=$r,mg=Ta,gg=H,vg=Yi,yg=Ao,bg=tt,_g=Ge,wg=Rr.enforce,kg=Rr.get,xg=sg.Int8Array,Sg=xg&&xg.prototype,Eg=sg.Uint8ClampedArray,Ag=Eg&&Eg.prototype,Tg=xg&&vg(xg),Rg=Sg&&vg(Sg),Cg=Object.prototype,Ig=sg.TypeError,Og=bg("toStringTag"),Dg=_g("TYPED_ARRAY_TAG"),Bg="TypedArrayConstructor",Lg=og&&!!yg&&"Opera"!==hg(sg.opera),Pg=!1,Ng={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},zg={BigInt64Array:8,BigUint64Array:8},Ug=function(e){var t=vg(e);if(ug(t)){var r=kg(t);return r&&lg(r,Bg)?r[Bg]:Ug(t)}},jg=function(e){if(!ug(e))return!1;var t=hg(e);return lg(Ng,t)||lg(zg,t)};for(tg in Ng)(ng=(rg=sg[tg])&&rg.prototype)?wg(ng)[Bg]=rg:Lg=!1;for(tg in zg)(ng=(rg=sg[tg])&&rg.prototype)&&(wg(ng)[Bg]=rg);if((!Lg||!cg(Tg)||Tg===Function.prototype)&&(Tg=function(){throw new Ig("Incorrect invocation")},Lg))for(tg in Ng)sg[tg]&&yg(sg[tg],Tg);if((!Lg||!Rg||Rg===Cg)&&(Rg=Tg.prototype,Lg))for(tg in Ng)sg[tg]&&yg(sg[tg].prototype,Rg);if(Lg&&vg(Ag)!==Rg&&yg(Ag,Rg),ag&&!lg(Rg,Og))for(tg in Pg=!0,mg(Rg,Og,{configurable:!0,get:function(){return ug(this)?this[Dg]:void 0}}),Ng)sg[tg]&&fg(sg[tg],Dg,tg);var Fg={NATIVE_ARRAY_BUFFER_VIEWS:Lg,TYPED_ARRAY_TAG:Pg&&Dg,aTypedArray:function(e){if(jg(e))return e;throw new Ig("Target is not a typed array")},aTypedArrayConstructor:function(e){if(cg(e)&&(!yg||gg(Tg,e)))return e;throw new Ig(dg(e)+" is not a typed array constructor")},exportTypedArrayMethod:function(e,t,r,n){if(ag){if(r)for(var i in Ng){var o=sg[i];if(o&&lg(o.prototype,e))try{delete o.prototype[e]}catch(iA){try{o.prototype[e]=t}catch(a){}}}Rg[e]&&!r||pg(Rg,e,r?t:Lg&&Sg[e]||t,n)}},exportTypedArrayStaticMethod:function(e,t,r){var n,i;if(ag){if(yg){if(r)for(n in Ng)if((i=sg[n])&&lg(i,e))try{delete i[e]}catch(iA){}if(Tg[e]&&!r)return;try{return pg(Tg,e,r?t:Lg&&Tg[e]||t)}catch(iA){}}for(n in Ng)!(i=sg[n])||i[e]&&!r||pg(i,e,t)}},getTypedArrayConstructor:Ug,isView:function(e){if(!ug(e))return!1;var t=hg(e);return"DataView"===t||lg(Ng,t)||lg(zg,t)},isTypedArray:jg,TypedArray:Tg,TypedArrayPrototype:Rg},Mg=r,qg=i,Wg=Ad,Vg=Fg.NATIVE_ARRAY_BUFFER_VIEWS,Hg=Mg.ArrayBuffer,Gg=Mg.Int8Array,Zg=!Vg||!qg((function(){Gg(1)}))||!qg((function(){new Gg(-1)}))||!Wg((function(e){new Gg,new Gg(null),new Gg(1.5),new Gg(e)}),!0)||qg((function(){return 1!==new Gg(new Hg(2),1,void 0).length})),Yg=M,Kg=Math.floor,Xg=Number.isInteger||function(e){return!Yg(e)&&isFinite(e)&&Kg(e)===e},$g=rn,Jg=RangeError,Qg=function(e){var t=$g(e);if(t<0)throw new Jg("The argument can't be less than 0");return t},ev=RangeError,tv=function(e,t){var r=Qg(e);if(r%t)throw new ev("Wrong offset");return r},rv=Math.round,nv=Uc,iv=ut,ov=TypeError,av=function(e){var t=iv(e,"number");if("number"==typeof t)throw new ov("Can't convert number to bigint");return BigInt(t)},sv=bu,cv=u,uv=ou,lv=Ue,hv=dn,dv=nd,fv=Xh,pv=Vh,mv=function(e){var t=nv(e);return"BigInt64Array"===t||"BigUint64Array"===t},gv=Fg.aTypedArrayConstructor,vv=av,yv=function(e){var t,r,n,i,o,a,s,c,u=uv(this),l=lv(e),h=arguments.length,d=h>1?arguments[1]:void 0,f=void 0!==d,p=fv(l);if(p&&!pv(p))for(c=(s=dv(l,p)).next,l=[];!(a=cv(c,s)).done;)l.push(a.value);for(f&&h>2&&(d=sv(d,arguments[2])),r=hv(l),n=new(gv(u))(r),i=mv(n),t=0;r>t;t++)o=f?d(l[t],t):l[t],n[t]=i?vv(o):+o;return n},bv=E,_v=Array.isArray||function(e){return"Array"===bv(e)},wv=tu,kv=M,xv=tt("species"),Sv=Array,Ev=function(e){var t;return _v(e)&&(t=e.constructor,(wv(t)&&(t===Sv||_v(t.prototype))||kv(t)&&null===(t=t[xv]))&&(t=void 0)),void 0===t?Sv:t},Av=bu,Tv=I,Rv=Ue,Cv=dn,Iv=function(e,t){return new(Ev(e))(0===t?0:t)},Ov=w([].push),Dv=function(e){var t=1===e,r=2===e,n=3===e,i=4===e,o=6===e,a=7===e,s=5===e||o;return function(c,u,l,h){for(var d,f,p=Rv(c),m=Tv(p),g=Cv(m),v=Av(u,l),y=0,b=h||Iv,_=t?b(c,g):r||a?b(c,0):void 0;g>y;y++)if((s||y in m)&&(f=v(d=m[y],y,p),e))if(t)_[y]=f;else if(f)switch(e){case 3:return!0;case 5:return d;case 6:return y;case 2:Ov(_,d)}else switch(e){case 4:return!1;case 7:Ov(_,d)}return o?-1:n||i?i:_}},Bv={forEach:Dv(0),map:Dv(1),filter:Dv(2),some:Dv(3),every:Dv(4),find:Dv(5),findIndex:Dv(6),filterReject:Dv(7)},Lv=dn,Pv=ti,Nv=r,zv=u,Uv=o,jv=Zg,Fv=Fg,Mv=sc,qv=Da,Wv=g,Vv=Zt,Hv=Xg,Gv=ln,Zv=Na,Yv=tv,Kv=function(e){var t=rv(e);return t<0?0:t>255?255:255&t},Xv=dt,$v=Me,Jv=Uc,Qv=M,ey=he,ty=Ti,ry=H,ny=Ao,iy=Jr.f,oy=yv,ay=Bv.forEach,sy=dc,cy=Ta,uy=Rt,ly=n,hy=function(e,t,r){for(var n=0,i=arguments.length>2?r:Lv(t),o=new e(i);i>n;)o[n]=t[n++];return o},dy=os,fy=Rr.get,py=Rr.set,my=Rr.enforce,gy=uy.f,vy=ly.f,yy=Nv.RangeError,by=Mv.ArrayBuffer,_y=by.prototype,wy=Mv.DataView,ky=Fv.NATIVE_ARRAY_BUFFER_VIEWS,xy=Fv.TYPED_ARRAY_TAG,Sy=Fv.TypedArray,Ey=Fv.TypedArrayPrototype,Ay=Fv.isTypedArray,Ty="BYTES_PER_ELEMENT",Ry="Wrong length",Cy=function(e,t){cy(e,t,{configurable:!0,get:function(){return fy(this)[t]}})},Iy=function(e){var t;return ry(_y,e)||"ArrayBuffer"===(t=Jv(e))||"SharedArrayBuffer"===t},Oy=function(e,t){return Ay(e)&&!ey(t)&&t in e&&Hv(+t)&&t>=0},Dy=function(e,t){return t=Xv(t),Oy(e,t)?Wv(2,e[t]):vy(e,t)},By=function(e,t,r){return t=Xv(t),!(Oy(e,t)&&Qv(r)&&$v(r,"value"))||$v(r,"get")||$v(r,"set")||r.configurable||$v(r,"writable")&&!r.writable||$v(r,"enumerable")&&!r.enumerable?gy(e,t,r):(e[t]=r.value,e)};Uv?(ky||(ly.f=Dy,uy.f=By,Cy(Ey,"buffer"),Cy(Ey,"byteOffset"),Cy(Ey,"byteLength"),Cy(Ey,"length")),Pv({target:"Object",stat:!0,forced:!ky},{getOwnPropertyDescriptor:Dy,defineProperty:By}),ig.exports=function(e,t,r){var n=e.match(/\d+/)[0]/8,i=e+(r?"Clamped":"")+"Array",o="get"+e,a="set"+e,s=Nv[i],c=s,u=c&&c.prototype,l={},h=function(e,t){gy(e,t,{get:function(){return function(e,t){var r=fy(e);return r.view[o](t*n+r.byteOffset,!0)}(this,t)},set:function(e){return function(e,t,i){var o=fy(e);o.view[a](t*n+o.byteOffset,r?Kv(i):i,!0)}(this,t,e)},enumerable:!0})};ky?jv&&(c=t((function(e,t,r,i){return qv(e,u),dy(Qv(t)?Iy(t)?void 0!==i?new s(t,Yv(r,n),i):void 0!==r?new s(t,Yv(r,n)):new s(t):Ay(t)?hy(c,t):zv(oy,c,t):new s(Zv(t)),e,c)})),ny&&ny(c,Sy),ay(iy(s),(function(e){e in c||Vv(c,e,s[e])})),c.prototype=u):(c=t((function(e,t,r,i){qv(e,u);var o,a,s,l=0,d=0;if(Qv(t)){if(!Iy(t))return Ay(t)?hy(c,t):zv(oy,c,t);o=t,d=Yv(r,n);var f=t.byteLength;if(void 0===i){if(f%n)throw new yy(Ry);if((a=f-d)<0)throw new yy(Ry)}else if((a=Gv(i)*n)+d>f)throw new yy(Ry);s=a/n}else s=Zv(t),o=new by(a=s*n);for(py(e,{buffer:o,byteOffset:d,byteLength:a,length:s,view:new wy(o)});l<s;)h(e,l++)})),ny&&ny(c,Sy),u=c.prototype=ty(Ey)),u.constructor!==c&&Vv(u,"constructor",c),my(u).TypedArrayConstructor=c,xy&&Vv(u,xy,i);var d=c!==s;l[i]=c,Pv({global:!0,constructor:!0,forced:d,sham:!ky},l),Ty in c||Vv(c,Ty,n),Ty in u||Vv(u,Ty,n),sy(i)}):ig.exports=function(){};var Ly=ig.exports;Ly("Int32",(function(e){return function(t,r,n){return e(this,t,r,n)}})),Ly("Uint8",(function(e){return function(t,r,n){return e(this,t,r,n)}})),Ly("Uint16",(function(e){return function(t,r,n){return e(this,t,r,n)}})),Ly("Uint32",(function(e){return function(t,r,n){return e(this,t,r,n)}}));var Py=es,Ny=av,zy=Uc,Uy=u,jy=i,Fy=Fg.aTypedArray,My=Fg.exportTypedArrayMethod,qy=w("".slice);My("fill",(function(e){var t=arguments.length;Fy(this);var r="Big"===qy(zy(this),0,3)?Ny(e):+e;return Uy(Py,this,r,t>1?arguments[1]:void 0,t>2?arguments[2]:void 0)}),jy((function(){var e=0;return new Int8Array(2).fill({valueOf:function(){return e++}}),1!==e}))),(0,Fg.exportTypedArrayStaticMethod)("from",yv,Zg);var Wy=r,Vy=u,Hy=Fg,Gy=dn,Zy=tv,Yy=Ue,Ky=i,Xy=Wy.RangeError,$y=Wy.Int8Array,Jy=$y&&$y.prototype,Qy=Jy&&Jy.set,eb=Hy.aTypedArray,tb=Hy.exportTypedArrayMethod,rb=!Ky((function(){var e=new Uint8ClampedArray(2);return Vy(Qy,e,{length:1,0:3},1),3!==e[1]})),nb=rb&&Hy.NATIVE_ARRAY_BUFFER_VIEWS&&Ky((function(){var e=new $y(2);return e.set(1),e.set("2",1),0!==e[0]||2!==e[1]}));tb("set",(function(e){eb(this);var t=Zy(arguments.length>1?arguments[1]:void 0,1),r=Yy(e);if(rb)return Vy(Qy,this,r,t);var n=this.length,i=Gy(r),o=0;if(i+t>n)throw new Xy("Wrong length");for(;o<i;)this[t+o]=r[o++]}),!rb||nb);var ib=ts,ob=Math.floor,ab=function(e,t){var r=e.length;if(r<8)for(var n,i,o=1;o<r;){for(i=o,n=e[o];i&&t(e[i-1],n)>0;)e[i]=e[--i];i!==o++&&(e[i]=n)}else for(var a=ob(r/2),s=ab(ib(e,0,a),t),c=ab(ib(e,a),t),u=s.length,l=c.length,h=0,d=0;h<u||d<l;)e[h+d]=h<u&&d<l?t(s[h],c[d])<=0?s[h++]:c[d++]:h<u?s[h++]:c[d++];return e},sb=ab,cb=Y.match(/firefox\/(\d+)/i),ub=!!cb&&+cb[1],lb=/MSIE|Trident/.test(Y),hb=Y.match(/AppleWebKit\/(\d+)\./),db=!!hb&&+hb[1],fb=yc,pb=i,mb=ve,gb=sb,vb=ub,yb=lb,bb=te,_b=db,wb=Fg.aTypedArray,kb=Fg.exportTypedArrayMethod,xb=r.Uint16Array,Sb=xb&&fb(xb.prototype.sort),Eb=!(!Sb||pb((function(){Sb(new xb(2),null)}))&&pb((function(){Sb(new xb(2),{})}))),Ab=!!Sb&&!pb((function(){if(bb)return bb<74;if(vb)return vb<67;if(yb)return!0;if(_b)return _b<602;var e,t,r=new xb(516),n=Array(516);for(e=0;e<516;e++)t=e%4,r[e]=515-e,n[e]=e-2*t+3;for(Sb(r,(function(e,t){return(e/4|0)-(t/4|0)})),e=0;e<516;e++)if(r[e]!==n[e])return!0}));kb("sort",(function(e){return void 0!==e&&mb(e),Ab?Sb(this,e):gb(wb(this),function(e){return function(t,r){return void 0!==e?+e(t,r)||0:r!=r?-1:t!=t?1:0===t&&0===r?1/t>0&&1/r<0?1:-1:t>r}}(e))}),!Ab||Eb);var Tb={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Rb=gt("span").classList,Cb=Rb&&Rb.constructor&&Rb.constructor.prototype,Ib=Cb===Object.prototype?void 0:Cb,Ob=Bv.forEach,Db=ga("forEach")?[].forEach:function(e){return Ob(this,e,arguments.length>1?arguments[1]:void 0)},Bb=r,Lb=Tb,Pb=Ib,Nb=Db,zb=Zt,Ub=function(e){if(e&&e.forEach!==Nb)try{zb(e,"forEach",Nb)}catch(iA){e.forEach=Nb}};for(var jb in Lb)Lb[jb]&&Ub(Bb[jb]&&Bb[jb].prototype);Ub(Pb);var Fb=r,Mb=Tb,qb=Ib,Wb=oa,Vb=Zt,Hb=so,Gb=tt("iterator"),Zb=Wb.values,Yb=function(e,t){if(e){if(e[Gb]!==Zb)try{Vb(e,Gb,Zb)}catch(iA){e[Gb]=Zb}if(Hb(e,t,!0),Mb[t])for(var r in Wb)if(e[r]!==Wb[r])try{Vb(e,r,Wb[r])}catch(iA){e[r]=Wb[r]}}};for(var Kb in Mb)Yb(Fb[Kb]&&Fb[Kb].prototype,Kb);Yb(qb,"DOMTokenList");var Xb=Ku.clear;ti({global:!0,bind:!0,enumerable:!0,forced:r.clearImmediate!==Xb},{clearImmediate:Xb});var $b=r,Jb=mu,Qb=j,e_=wa,t_=Y,r_=ts,n_=wu,i_=$b.Function,o_=/MSIE .\./.test(t_)||"BUN"===e_&&function(){var e=$b.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}(),a_=ti,s_=r,c_=Ku.set,u_=function(e,t){var r=t?2:1;return o_?function(n,i){var o=n_(arguments.length,1)>r,a=Qb(n)?n:i_(n),s=o?r_(arguments,r):[],c=o?function(){Jb(a,this,s)}:a;return t?e(c,i):e(c)}:e},l_=s_.setImmediate?u_(c_,!1):c_;a_({global:!0,bind:!0,enumerable:!0,forced:s_.setImmediate!==l_},{setImmediate:l_});var h_=fm.charAt,d_=vf,f_=Rr,p_=Go,m_=Zo,g_="String Iterator",v_=f_.set,y_=f_.getterFor(g_);p_(String,"String",(function(e){v_(this,{type:g_,string:d_(e),index:0})}),(function(){var e,t=y_(this),r=t.string,n=t.index;return n>=r.length?m_(void 0,!0):(e=h_(r,n),t.index+=e.length,m_(e,!1))}));var b_=i,__=o,w_=tt("iterator"),k_=!b_((function(){var e=new URL("b?a=1&b=2&c=3","https://a"),t=e.searchParams,r=new URLSearchParams("a=1&a=2&b=3"),n="";return e.pathname="c%20d",t.forEach((function(e,r){t.delete("b"),n+=r+e})),r.delete("a",2),r.delete("b",void 0),!t.size&&!__||!t.sort||"https://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[w_]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==n||"x"!==new URL("https://x",void 0).host})),x_=o,S_=w,E_=u,A_=i,T_=oi,R_=Tn,C_=l,I_=Ue,O_=I,D_=Object.assign,B_=Object.defineProperty,L_=S_([].concat),P_=!D_||A_((function(){if(x_&&1!==D_({b:1},D_(B_({},"a",{enumerable:!0,get:function(){B_(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},r=Symbol("assign detection"),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach((function(e){t[e]=e})),7!==D_({},e)[r]||T_(D_({},t)).join("")!==n}))?function(e,t){for(var r=I_(e),n=arguments.length,i=1,o=R_.f,a=C_.f;n>i;)for(var s,c=O_(arguments[i++]),u=o?L_(T_(c),o(c)):T_(c),l=u.length,h=0;l>h;)s=u[h++],x_&&!E_(a,c,s)||(r[s]=c[s]);return r}:D_,N_=Bt,z_=sd,U_=o,j_=Rt,F_=g,M_=bu,q_=u,W_=Ue,V_=function(e,t,r,n){try{return n?t(N_(r)[0],r[1]):t(r)}catch(iA){z_(e,"throw",iA)}},H_=Vh,G_=tu,Z_=dn,Y_=function(e,t,r){U_?j_.f(e,t,F_(0,r)):e[t]=r},K_=nd,X_=Xh,$_=Array,J_=w,Q_=2147483647,ew=/[^\0-\u007E]/,tw=/[.\u3002\uFF0E\uFF61]/g,rw="Overflow: input needs wider integers to process",nw=RangeError,iw=J_(tw.exec),ow=Math.floor,aw=String.fromCharCode,sw=J_("".charCodeAt),cw=J_([].join),uw=J_([].push),lw=J_("".replace),hw=J_("".split),dw=J_("".toLowerCase),fw=function(e){return e+22+75*(e<26)},pw=function(e,t,r){var n=0;for(e=r?ow(e/700):e>>1,e+=ow(e/t);e>455;)e=ow(e/35),n+=36;return ow(n+36*e/(e+38))},mw=function(e){var t=[];e=function(e){for(var t=[],r=0,n=e.length;r<n;){var i=sw(e,r++);if(i>=55296&&i<=56319&&r<n){var o=sw(e,r++);56320==(64512&o)?uw(t,((1023&i)<<10)+(1023&o)+65536):(uw(t,i),r--)}else uw(t,i)}return t}(e);var r,n,i=e.length,o=128,a=0,s=72;for(r=0;r<e.length;r++)(n=e[r])<128&&uw(t,aw(n));var c=t.length,u=c;for(c&&uw(t,"-");u<i;){var l=Q_;for(r=0;r<e.length;r++)(n=e[r])>=o&&n<l&&(l=n);var h=u+1;if(l-o>ow((Q_-a)/h))throw new nw(rw);for(a+=(l-o)*h,o=l,r=0;r<e.length;r++){if((n=e[r])<o&&++a>Q_)throw new nw(rw);if(n===o){for(var d=a,f=36;;){var p=f<=s?1:f>=s+26?26:f-s;if(d<p)break;var m=d-p,g=36-p;uw(t,aw(fw(p+m%g))),d=ow(m/g),f+=36}uw(t,aw(fw(d))),s=pw(a,h,u===c),a=0,u++}}a++,o++}return cw(t,"")},gw=ti,vw=w,yw=sn,bw=RangeError,_w=String.fromCharCode,ww=String.fromCodePoint,kw=vw([].join);gw({target:"String",stat:!0,arity:1,forced:!!ww&&1!==ww.length},{fromCodePoint:function(e){for(var t,r=[],n=arguments.length,i=0;n>i;){if(t=+arguments[i++],yw(t,1114111)!==t)throw new bw(t+" is not a valid code point");r[i]=t<65536?_w(t):_w(55296+((t-=65536)>>10),t%1024+56320)}return kw(r,"")}});var xw=ti,Sw=r,Ew=Qu,Aw=V,Tw=u,Rw=w,Cw=o,Iw=k_,Ow=$r,Dw=Ta,Bw=Ca,Lw=so,Pw=mo,Nw=Rr,zw=Da,Uw=j,jw=Me,Fw=bu,Mw=Uc,qw=Bt,Ww=M,Vw=vf,Hw=Ti,Gw=g,Zw=nd,Yw=Xh,Kw=Zo,Xw=wu,$w=sb,Jw=tt("iterator"),Qw="URLSearchParams",ek=Qw+"Iterator",tk=Nw.set,rk=Nw.getterFor(Qw),nk=Nw.getterFor(ek),ik=Ew("fetch"),ok=Ew("Request"),ak=Ew("Headers"),sk=ok&&ok.prototype,ck=ak&&ak.prototype,uk=Sw.TypeError,lk=Sw.encodeURIComponent,hk=String.fromCharCode,dk=Aw("String","fromCodePoint"),fk=parseInt,pk=Rw("".charAt),mk=Rw([].join),gk=Rw([].push),vk=Rw("".replace),yk=Rw([].shift),bk=Rw([].splice),_k=Rw("".split),wk=Rw("".slice),kk=Rw(/./.exec),xk=/\+/g,Sk=/^[0-9a-f]+$/i,Ek=function(e,t){var r=wk(e,t,t+2);return kk(Sk,r)?fk(r,16):NaN},Ak=function(e){for(var t=0,r=128;r>0&&e&r;r>>=1)t++;return t},Tk=function(e){var t=null;switch(e.length){case 1:t=e[0];break;case 2:t=(31&e[0])<<6|63&e[1];break;case 3:t=(15&e[0])<<12|(63&e[1])<<6|63&e[2];break;case 4:t=(7&e[0])<<18|(63&e[1])<<12|(63&e[2])<<6|63&e[3]}return t>1114111?null:t},Rk=function(e){for(var t=(e=vk(e,xk," ")).length,r="",n=0;n<t;){var i=pk(e,n);if("%"===i){if("%"===pk(e,n+1)||n+3>t){r+="%",n++;continue}var o=Ek(e,n+1);if(o!=o){r+=i,n++;continue}n+=2;var a=Ak(o);if(0===a)i=hk(o);else{if(1===a||a>4){r+="�",n++;continue}for(var s=[o],c=1;c<a&&!(++n+3>t||"%"!==pk(e,n));){var u=Ek(e,n+1);if(u!=u){n+=3;break}if(u>191||u<128)break;gk(s,u),n+=2,c++}if(s.length!==a){r+="�";continue}var l=Tk(s);null===l?r+="�":i=dk(l)}}r+=i,n++}return r},Ck=/[!'()~]|%20/g,Ik={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},Ok=function(e){return Ik[e]},Dk=function(e){return vk(lk(e),Ck,Ok)},Bk=Pw((function(e,t){tk(this,{type:ek,target:rk(e).entries,index:0,kind:t})}),Qw,(function(){var e=nk(this),t=e.target,r=e.index++;if(!t||r>=t.length)return e.target=null,Kw(void 0,!0);var n=t[r];switch(e.kind){case"keys":return Kw(n.key,!1);case"values":return Kw(n.value,!1)}return Kw([n.key,n.value],!1)}),!0),Lk=function(e){this.entries=[],this.url=null,void 0!==e&&(Ww(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===pk(e,0)?wk(e,1):e:Vw(e)))};Lk.prototype={type:Qw,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,r,n,i,o,a,s,c=this.entries,u=Yw(e);if(u)for(r=(t=Zw(e,u)).next;!(n=Tw(r,t)).done;){if(o=(i=Zw(qw(n.value))).next,(a=Tw(o,i)).done||(s=Tw(o,i)).done||!Tw(o,i).done)throw new uk("Expected sequence with length 2");gk(c,{key:Vw(a.value),value:Vw(s.value)})}else for(var l in e)jw(e,l)&&gk(c,{key:l,value:Vw(e[l])})},parseQuery:function(e){if(e)for(var t,r,n=this.entries,i=_k(e,"&"),o=0;o<i.length;)(t=i[o++]).length&&(r=_k(t,"="),gk(n,{key:Rk(yk(r)),value:Rk(mk(r,"="))}))},serialize:function(){for(var e,t=this.entries,r=[],n=0;n<t.length;)e=t[n++],gk(r,Dk(e.key)+"="+Dk(e.value));return mk(r,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var Pk=function(){zw(this,Nk);var e=tk(this,new Lk(arguments.length>0?arguments[0]:void 0));Cw||(this.size=e.entries.length)},Nk=Pk.prototype;if(Bw(Nk,{append:function(e,t){var r=rk(this);Xw(arguments.length,2),gk(r.entries,{key:Vw(e),value:Vw(t)}),Cw||this.length++,r.updateURL()},delete:function(e){for(var t=rk(this),r=Xw(arguments.length,1),n=t.entries,i=Vw(e),o=r<2?void 0:arguments[1],a=void 0===o?o:Vw(o),s=0;s<n.length;){var c=n[s];if(c.key!==i||void 0!==a&&c.value!==a)s++;else if(bk(n,s,1),void 0!==a)break}Cw||(this.size=n.length),t.updateURL()},get:function(e){var t=rk(this).entries;Xw(arguments.length,1);for(var r=Vw(e),n=0;n<t.length;n++)if(t[n].key===r)return t[n].value;return null},getAll:function(e){var t=rk(this).entries;Xw(arguments.length,1);for(var r=Vw(e),n=[],i=0;i<t.length;i++)t[i].key===r&&gk(n,t[i].value);return n},has:function(e){for(var t=rk(this).entries,r=Xw(arguments.length,1),n=Vw(e),i=r<2?void 0:arguments[1],o=void 0===i?i:Vw(i),a=0;a<t.length;){var s=t[a++];if(s.key===n&&(void 0===o||s.value===o))return!0}return!1},set:function(e,t){var r=rk(this);Xw(arguments.length,1);for(var n,i=r.entries,o=!1,a=Vw(e),s=Vw(t),c=0;c<i.length;c++)(n=i[c]).key===a&&(o?bk(i,c--,1):(o=!0,n.value=s));o||gk(i,{key:a,value:s}),Cw||(this.size=i.length),r.updateURL()},sort:function(){var e=rk(this);$w(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){for(var t,r=rk(this).entries,n=Fw(e,arguments.length>1?arguments[1]:void 0),i=0;i<r.length;)n((t=r[i++]).value,t.key,this)},keys:function(){return new Bk(this,"keys")},values:function(){return new Bk(this,"values")},entries:function(){return new Bk(this,"entries")}},{enumerable:!0}),Ow(Nk,Jw,Nk.entries,{name:"entries"}),Ow(Nk,"toString",(function(){return rk(this).serialize()}),{enumerable:!0}),Cw&&Dw(Nk,"size",{get:function(){return rk(this).entries.length},configurable:!0,enumerable:!0}),Lw(Pk,Qw),xw({global:!0,constructor:!0,forced:!Iw},{URLSearchParams:Pk}),!Iw&&Uw(ak)){var zk=Rw(ck.has),Uk=Rw(ck.set),jk=function(e){if(Ww(e)){var t,r=e.body;if(Mw(r)===Qw)return t=e.headers?new ak(e.headers):new ak,zk(t,"content-type")||Uk(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),Hw(e,{body:Gw(0,Vw(r)),headers:Gw(0,t)})}return e};if(Uw(ik)&&xw({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return ik(e,arguments.length>1?jk(arguments[1]):{})}}),Uw(ok)){var Fk=function(e){return zw(this,sk),new ok(e,arguments.length>1?jk(arguments[1]):{})};sk.constructor=Fk,Fk.prototype=sk,xw({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:Fk})}}var Mk,qk=ti,Wk=o,Vk=k_,Hk=r,Gk=bu,Zk=w,Yk=$r,Kk=Ta,Xk=Da,$k=Me,Jk=P_,Qk=function(e){var t=W_(e),r=G_(this),n=arguments.length,i=n>1?arguments[1]:void 0,o=void 0!==i;o&&(i=M_(i,n>2?arguments[2]:void 0));var a,s,c,u,l,h,d=X_(t),f=0;if(!d||this===$_&&H_(d))for(a=Z_(t),s=r?new this(a):$_(a);a>f;f++)h=o?i(t[f],f):t[f],Y_(s,f,h);else for(s=r?new this:[],l=(u=K_(t,d)).next;!(c=q_(l,u)).done;f++)h=o?V_(u,i,[c.value,f],!0):c.value,Y_(s,f,h);return s.length=f,s},ex=ts,tx=fm.codeAt,rx=function(e){var t,r,n=[],i=hw(lw(dw(e),tw,"."),".");for(t=0;t<i.length;t++)r=i[t],uw(n,iw(ew,r)?"xn--"+mw(r):r);return cw(n,".")},nx=vf,ix=so,ox=wu,ax={URLSearchParams:Pk,getState:rk},sx=Rr,cx=sx.set,ux=sx.getterFor("URL"),lx=ax.URLSearchParams,hx=ax.getState,dx=Hk.URL,fx=Hk.TypeError,px=Hk.parseInt,mx=Math.floor,gx=Math.pow,vx=Zk("".charAt),yx=Zk(/./.exec),bx=Zk([].join),_x=Zk(1..toString),wx=Zk([].pop),kx=Zk([].push),xx=Zk("".replace),Sx=Zk([].shift),Ex=Zk("".split),Ax=Zk("".slice),Tx=Zk("".toLowerCase),Rx=Zk([].unshift),Cx="Invalid scheme",Ix="Invalid host",Ox="Invalid port",Dx=/[a-z]/i,Bx=/[\d+-.a-z]/i,Lx=/\d/,Px=/^0x/i,Nx=/^[0-7]+$/,zx=/^\d+$/,Ux=/^[\da-f]+$/i,jx=/[\0\t\n\r #%/:<>?@[\\\]^|]/,Fx=/[\0\t\n\r #/:<>?@[\\\]^|]/,Mx=/^[\u0000-\u0020]+/,qx=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,Wx=/[\t\n\r]/g,Vx=function(e){var t,r,n,i;if("number"==typeof e){for(t=[],r=0;r<4;r++)Rx(t,e%256),e=mx(e/256);return bx(t,".")}if("object"==typeof e){for(t="",n=function(e){for(var t=null,r=1,n=null,i=0,o=0;o<8;o++)0!==e[o]?(i>r&&(t=n,r=i),n=null,i=0):(null===n&&(n=o),++i);return i>r?n:t}(e),r=0;r<8;r++)i&&0===e[r]||(i&&(i=!1),n===r?(t+=r?":":"::",i=!0):(t+=_x(e[r],16),r<7&&(t+=":")));return"["+t+"]"}return e},Hx={},Gx=Jk({},Hx,{" ":1,'"':1,"<":1,">":1,"`":1}),Zx=Jk({},Gx,{"#":1,"?":1,"{":1,"}":1}),Yx=Jk({},Zx,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),Kx=function(e,t){var r=tx(e,0);return r>32&&r<127&&!$k(t,e)?e:encodeURIComponent(e)},Xx={ftp:21,file:null,http:80,https:443,ws:80,wss:443},$x=function(e,t){var r;return 2===e.length&&yx(Dx,vx(e,0))&&(":"===(r=vx(e,1))||!t&&"|"===r)},Jx=function(e){var t;return e.length>1&&$x(Ax(e,0,2))&&(2===e.length||"/"===(t=vx(e,2))||"\\"===t||"?"===t||"#"===t)},Qx=function(e){return"."===e||"%2e"===Tx(e)},eS={},tS={},rS={},nS={},iS={},oS={},aS={},sS={},cS={},uS={},lS={},hS={},dS={},fS={},pS={},mS={},gS={},vS={},yS={},bS={},_S={},wS=function(e,t,r){var n,i,o,a=nx(e);if(t){if(i=this.parse(a))throw new fx(i);this.searchParams=null}else{if(void 0!==r&&(n=new wS(r,!0)),i=this.parse(a,null,n))throw new fx(i);(o=hx(new lx)).bindURL(this),this.searchParams=o}};wS.prototype={type:"URL",parse:function(e,t,r){var n,i,o,a,s,c=this,u=t||eS,l=0,h="",d=!1,f=!1,p=!1;for(e=nx(e),t||(c.scheme="",c.username="",c.password="",c.host=null,c.port=null,c.path=[],c.query=null,c.fragment=null,c.cannotBeABaseURL=!1,e=xx(e,Mx,""),e=xx(e,qx,"$1")),e=xx(e,Wx,""),n=Qk(e);l<=n.length;){switch(i=n[l],u){case eS:if(!i||!yx(Dx,i)){if(t)return Cx;u=rS;continue}h+=Tx(i),u=tS;break;case tS:if(i&&(yx(Bx,i)||"+"===i||"-"===i||"."===i))h+=Tx(i);else{if(":"!==i){if(t)return Cx;h="",u=rS,l=0;continue}if(t&&(c.isSpecial()!==$k(Xx,h)||"file"===h&&(c.includesCredentials()||null!==c.port)||"file"===c.scheme&&!c.host))return;if(c.scheme=h,t)return void(c.isSpecial()&&Xx[c.scheme]===c.port&&(c.port=null));h="","file"===c.scheme?u=fS:c.isSpecial()&&r&&r.scheme===c.scheme?u=nS:c.isSpecial()?u=sS:"/"===n[l+1]?(u=iS,l++):(c.cannotBeABaseURL=!0,kx(c.path,""),u=yS)}break;case rS:if(!r||r.cannotBeABaseURL&&"#"!==i)return Cx;if(r.cannotBeABaseURL&&"#"===i){c.scheme=r.scheme,c.path=ex(r.path),c.query=r.query,c.fragment="",c.cannotBeABaseURL=!0,u=_S;break}u="file"===r.scheme?fS:oS;continue;case nS:if("/"!==i||"/"!==n[l+1]){u=oS;continue}u=cS,l++;break;case iS:if("/"===i){u=uS;break}u=vS;continue;case oS:if(c.scheme=r.scheme,i===Mk)c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,c.path=ex(r.path),c.query=r.query;else if("/"===i||"\\"===i&&c.isSpecial())u=aS;else if("?"===i)c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,c.path=ex(r.path),c.query="",u=bS;else{if("#"!==i){c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,c.path=ex(r.path),c.path.length--,u=vS;continue}c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,c.path=ex(r.path),c.query=r.query,c.fragment="",u=_S}break;case aS:if(!c.isSpecial()||"/"!==i&&"\\"!==i){if("/"!==i){c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,u=vS;continue}u=uS}else u=cS;break;case sS:if(u=cS,"/"!==i||"/"!==vx(h,l+1))continue;l++;break;case cS:if("/"!==i&&"\\"!==i){u=uS;continue}break;case uS:if("@"===i){d&&(h="%40"+h),d=!0,o=Qk(h);for(var m=0;m<o.length;m++){var g=o[m];if(":"!==g||p){var v=Kx(g,Yx);p?c.password+=v:c.username+=v}else p=!0}h=""}else if(i===Mk||"/"===i||"?"===i||"#"===i||"\\"===i&&c.isSpecial()){if(d&&""===h)return"Invalid authority";l-=Qk(h).length+1,h="",u=lS}else h+=i;break;case lS:case hS:if(t&&"file"===c.scheme){u=mS;continue}if(":"!==i||f){if(i===Mk||"/"===i||"?"===i||"#"===i||"\\"===i&&c.isSpecial()){if(c.isSpecial()&&""===h)return Ix;if(t&&""===h&&(c.includesCredentials()||null!==c.port))return;if(a=c.parseHost(h))return a;if(h="",u=gS,t)return;continue}"["===i?f=!0:"]"===i&&(f=!1),h+=i}else{if(""===h)return Ix;if(a=c.parseHost(h))return a;if(h="",u=dS,t===hS)return}break;case dS:if(!yx(Lx,i)){if(i===Mk||"/"===i||"?"===i||"#"===i||"\\"===i&&c.isSpecial()||t){if(""!==h){var y=px(h,10);if(y>65535)return Ox;c.port=c.isSpecial()&&y===Xx[c.scheme]?null:y,h=""}if(t)return;u=gS;continue}return Ox}h+=i;break;case fS:if(c.scheme="file","/"===i||"\\"===i)u=pS;else{if(!r||"file"!==r.scheme){u=vS;continue}switch(i){case Mk:c.host=r.host,c.path=ex(r.path),c.query=r.query;break;case"?":c.host=r.host,c.path=ex(r.path),c.query="",u=bS;break;case"#":c.host=r.host,c.path=ex(r.path),c.query=r.query,c.fragment="",u=_S;break;default:Jx(bx(ex(n,l),""))||(c.host=r.host,c.path=ex(r.path),c.shortenPath()),u=vS;continue}}break;case pS:if("/"===i||"\\"===i){u=mS;break}r&&"file"===r.scheme&&!Jx(bx(ex(n,l),""))&&($x(r.path[0],!0)?kx(c.path,r.path[0]):c.host=r.host),u=vS;continue;case mS:if(i===Mk||"/"===i||"\\"===i||"?"===i||"#"===i){if(!t&&$x(h))u=vS;else if(""===h){if(c.host="",t)return;u=gS}else{if(a=c.parseHost(h))return a;if("localhost"===c.host&&(c.host=""),t)return;h="",u=gS}continue}h+=i;break;case gS:if(c.isSpecial()){if(u=vS,"/"!==i&&"\\"!==i)continue}else if(t||"?"!==i)if(t||"#"!==i){if(i!==Mk&&(u=vS,"/"!==i))continue}else c.fragment="",u=_S;else c.query="",u=bS;break;case vS:if(i===Mk||"/"===i||"\\"===i&&c.isSpecial()||!t&&("?"===i||"#"===i)){if(".."===(s=Tx(s=h))||"%2e."===s||".%2e"===s||"%2e%2e"===s?(c.shortenPath(),"/"===i||"\\"===i&&c.isSpecial()||kx(c.path,"")):Qx(h)?"/"===i||"\\"===i&&c.isSpecial()||kx(c.path,""):("file"===c.scheme&&!c.path.length&&$x(h)&&(c.host&&(c.host=""),h=vx(h,0)+":"),kx(c.path,h)),h="","file"===c.scheme&&(i===Mk||"?"===i||"#"===i))for(;c.path.length>1&&""===c.path[0];)Sx(c.path);"?"===i?(c.query="",u=bS):"#"===i&&(c.fragment="",u=_S)}else h+=Kx(i,Zx);break;case yS:"?"===i?(c.query="",u=bS):"#"===i?(c.fragment="",u=_S):i!==Mk&&(c.path[0]+=Kx(i,Hx));break;case bS:t||"#"!==i?i!==Mk&&("'"===i&&c.isSpecial()?c.query+="%27":c.query+="#"===i?"%23":Kx(i,Hx)):(c.fragment="",u=_S);break;case _S:i!==Mk&&(c.fragment+=Kx(i,Gx))}l++}},parseHost:function(e){var t,r,n;if("["===vx(e,0)){if("]"!==vx(e,e.length-1))return Ix;if(t=function(e){var t,r,n,i,o,a,s,c=[0,0,0,0,0,0,0,0],u=0,l=null,h=0,d=function(){return vx(e,h)};if(":"===d()){if(":"!==vx(e,1))return;h+=2,l=++u}for(;d();){if(8===u)return;if(":"!==d()){for(t=r=0;r<4&&yx(Ux,d());)t=16*t+px(d(),16),h++,r++;if("."===d()){if(0===r)return;if(h-=r,u>6)return;for(n=0;d();){if(i=null,n>0){if(!("."===d()&&n<4))return;h++}if(!yx(Lx,d()))return;for(;yx(Lx,d());){if(o=px(d(),10),null===i)i=o;else{if(0===i)return;i=10*i+o}if(i>255)return;h++}c[u]=256*c[u]+i,2!=++n&&4!==n||u++}if(4!==n)return;break}if(":"===d()){if(h++,!d())return}else if(d())return;c[u++]=t}else{if(null!==l)return;h++,l=++u}}if(null!==l)for(a=u-l,u=7;0!==u&&a>0;)s=c[u],c[u--]=c[l+a-1],c[l+--a]=s;else if(8!==u)return;return c}(Ax(e,1,-1)),!t)return Ix;this.host=t}else if(this.isSpecial()){if(e=rx(e),yx(jx,e))return Ix;if(t=function(e){var t,r,n,i,o,a,s,c=Ex(e,".");if(c.length&&""===c[c.length-1]&&c.length--,(t=c.length)>4)return e;for(r=[],n=0;n<t;n++){if(""===(i=c[n]))return e;if(o=10,i.length>1&&"0"===vx(i,0)&&(o=yx(Px,i)?16:8,i=Ax(i,8===o?1:2)),""===i)a=0;else{if(!yx(10===o?zx:8===o?Nx:Ux,i))return e;a=px(i,o)}kx(r,a)}for(n=0;n<t;n++)if(a=r[n],n===t-1){if(a>=gx(256,5-t))return null}else if(a>255)return null;for(s=wx(r),n=0;n<r.length;n++)s+=r[n]*gx(256,3-n);return s}(e),null===t)return Ix;this.host=t}else{if(yx(Fx,e))return Ix;for(t="",r=Qk(e),n=0;n<r.length;n++)t+=Kx(r[n],Hx);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"===this.scheme},includesCredentials:function(){return""!==this.username||""!==this.password},isSpecial:function(){return $k(Xx,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"===this.scheme&&1===t&&$x(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,r=e.username,n=e.password,i=e.host,o=e.port,a=e.path,s=e.query,c=e.fragment,u=t+":";return null!==i?(u+="//",e.includesCredentials()&&(u+=r+(n?":"+n:"")+"@"),u+=Vx(i),null!==o&&(u+=":"+o)):"file"===t&&(u+="//"),u+=e.cannotBeABaseURL?a[0]:a.length?"/"+bx(a,"/"):"",null!==s&&(u+="?"+s),null!==c&&(u+="#"+c),u},setHref:function(e){var t=this.parse(e);if(t)throw new fx(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"===e)try{return new kS(e.path[0]).origin}catch(iA){return"null"}return"file"!==e&&this.isSpecial()?e+"://"+Vx(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(nx(e)+":",eS)},getUsername:function(){return this.username},setUsername:function(e){var t=Qk(nx(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var r=0;r<t.length;r++)this.username+=Kx(t[r],Yx)}},getPassword:function(){return this.password},setPassword:function(e){var t=Qk(nx(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var r=0;r<t.length;r++)this.password+=Kx(t[r],Yx)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?Vx(e):Vx(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,lS)},getHostname:function(){var e=this.host;return null===e?"":Vx(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,hS)},getPort:function(){var e=this.port;return null===e?"":nx(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(""===(e=nx(e))?this.port=null:this.parse(e,dS))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+bx(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,gS))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){""===(e=nx(e))?this.query=null:("?"===vx(e,0)&&(e=Ax(e,1)),this.query="",this.parse(e,bS)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){""!==(e=nx(e))?("#"===vx(e,0)&&(e=Ax(e,1)),this.fragment="",this.parse(e,_S)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var kS=function(e){var t=Xk(this,xS),r=ox(arguments.length,1)>1?arguments[1]:void 0,n=cx(t,new wS(e,!1,r));Wk||(t.href=n.serialize(),t.origin=n.getOrigin(),t.protocol=n.getProtocol(),t.username=n.getUsername(),t.password=n.getPassword(),t.host=n.getHost(),t.hostname=n.getHostname(),t.port=n.getPort(),t.pathname=n.getPathname(),t.search=n.getSearch(),t.searchParams=n.getSearchParams(),t.hash=n.getHash())},xS=kS.prototype,SS=function(e,t){return{get:function(){return ux(this)[e]()},set:t&&function(e){return ux(this)[t](e)},configurable:!0,enumerable:!0}};if(Wk&&(Kk(xS,"href",SS("serialize","setHref")),Kk(xS,"origin",SS("getOrigin")),Kk(xS,"protocol",SS("getProtocol","setProtocol")),Kk(xS,"username",SS("getUsername","setUsername")),Kk(xS,"password",SS("getPassword","setPassword")),Kk(xS,"host",SS("getHost","setHost")),Kk(xS,"hostname",SS("getHostname","setHostname")),Kk(xS,"port",SS("getPort","setPort")),Kk(xS,"pathname",SS("getPathname","setPathname")),Kk(xS,"search",SS("getSearch","setSearch")),Kk(xS,"searchParams",SS("getSearchParams")),Kk(xS,"hash",SS("getHash","setHash"))),Yk(xS,"toJSON",(function(){return ux(this).serialize()}),{enumerable:!0}),Yk(xS,"toString",(function(){return ux(this).serialize()}),{enumerable:!0}),dx){var ES=dx.createObjectURL,AS=dx.revokeObjectURL;ES&&Yk(kS,"createObjectURL",Gk(ES,dx)),AS&&Yk(kS,"revokeObjectURL",Gk(AS,dx))}ix(kS,"URL"),qk({global:!0,constructor:!0,forced:!Vk,sham:!Wk},{URL:kS});var TS=u;ti({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return TS(URL.prototype.toString,this)}});var RS,CS=Object.create,IS=Object.defineProperty,OS=Object.getOwnPropertyDescriptor,DS=Object.getOwnPropertyNames,BS=Object.getPrototypeOf,LS=Object.prototype.hasOwnProperty,PS=(RS=function(e){if("undefined"!=typeof require)return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')},"undefined"!=typeof require?require:"undefined"!=typeof Proxy?new Proxy(RS,{get:(e,t)=>("undefined"!=typeof require?require:e)[t]}):RS),NS=(e,t,r,n)=>{for(var i,o=n>1?void 0:n?OS(t,r):t,a=e.length-1;a>=0;a--)(i=e[a])&&(o=(n?i(t,r,o):i(o))||o);return n&&o&&IS(t,r,o),o},zS=(e,t,r)=>((e,t,r)=>t in e?IS(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),US=((e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports))(((e,t)=>{!function(r){"object"==typeof e&&void 0!==t?t.exports=r():"function"==typeof define&&define.amd?define([],r):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).JSZip=r()}((function(){return function e(t,r,n){function i(a,s){if(!r[a]){if(!t[a]){var c="function"==typeof PS&&PS;if(!s&&c)return c(a,!0);if(o)return o(a,!0);var u=new Error("Cannot find module '"+a+"'");throw u.code="MODULE_NOT_FOUND",u}var l=r[a]={exports:{}};t[a][0].call(l.exports,(function(e){return i(t[a][1][e]||e)}),l,l.exports,e,t,r,n)}return r[a].exports}for(var o="function"==typeof PS&&PS,a=0;a<n.length;a++)i(n[a]);return i}({1:[function(e,t,r){var n=e("./utils"),i=e("./support"),o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(e){for(var t,r,i,a,s,c,u,l=[],h=0,d=e.length,f=d,p="string"!==n.getTypeOf(e);h<e.length;)f=d-h,i=p?(t=e[h++],r=h<d?e[h++]:0,h<d?e[h++]:0):(t=e.charCodeAt(h++),r=h<d?e.charCodeAt(h++):0,h<d?e.charCodeAt(h++):0),a=t>>2,s=(3&t)<<4|r>>4,c=1<f?(15&r)<<2|i>>6:64,u=2<f?63&i:64,l.push(o.charAt(a)+o.charAt(s)+o.charAt(c)+o.charAt(u));return l.join("")},r.decode=function(e){var t,r,n,a,s,c,u=0,l=0,h="data:";if(e.substr(0,5)===h)throw new Error("Invalid base64 input, it looks like a data url.");var d,f=3*(e=e.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(e.charAt(e.length-1)===o.charAt(64)&&f--,e.charAt(e.length-2)===o.charAt(64)&&f--,f%1!=0)throw new Error("Invalid base64 input, bad content length.");for(d=i.uint8array?new Uint8Array(0|f):new Array(0|f);u<e.length;)t=o.indexOf(e.charAt(u++))<<2|(a=o.indexOf(e.charAt(u++)))>>4,r=(15&a)<<4|(s=o.indexOf(e.charAt(u++)))>>2,n=(3&s)<<6|(c=o.indexOf(e.charAt(u++))),d[l++]=t,64!==s&&(d[l++]=r),64!==c&&(d[l++]=n);return d}},{"./support":30,"./utils":32}],2:[function(e,t,r){var n=e("./external"),i=e("./stream/DataWorker"),o=e("./stream/Crc32Probe"),a=e("./stream/DataLengthProbe");function s(e,t,r,n,i){this.compressedSize=e,this.uncompressedSize=t,this.crc32=r,this.compression=n,this.compressedContent=i}s.prototype={getContentWorker:function(){var e=new i(n.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new a("data_length")),t=this;return e.on("end",(function(){if(this.streamInfo.data_length!==t.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")})),e},getCompressedWorker:function(){return new i(n.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},s.createWorkerFrom=function(e,t,r){return e.pipe(new o).pipe(new a("uncompressedSize")).pipe(t.compressWorker(r)).pipe(new a("compressedSize")).withStreamInfo("compression",t)},t.exports=s},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(e,t,r){var n=e("./stream/GenericWorker");r.STORE={magic:"\0\0",compressWorker:function(){return new n("STORE compression")},uncompressWorker:function(){return new n("STORE decompression")}},r.DEFLATE=e("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(e,t,r){var n=e("./utils"),i=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t){return void 0!==e&&e.length?"string"!==n.getTypeOf(e)?function(e,t,r){var n=i,o=0+r;e^=-1;for(var a=0;a<o;a++)e=e>>>8^n[255&(e^t[a])];return~e}(0|t,e,e.length):function(e,t,r){var n=i,o=0+r;e^=-1;for(var a=0;a<o;a++)e=e>>>8^n[255&(e^t.charCodeAt(a))];return~e}(0|t,e,e.length):0}},{"./utils":32}],5:[function(e,t,r){r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!0,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],6:[function(e,t,r){var n;n="undefined"!=typeof Promise?Promise:e("lie"),t.exports={Promise:n}},{lie:37}],7:[function(e,t,r){var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,i=e("pako"),o=e("./utils"),a=e("./stream/GenericWorker"),s=n?"uint8array":"array";function c(e,t){a.call(this,"FlateWorker/"+e),this._pako=null,this._pakoAction=e,this._pakoOptions=t,this.meta={}}r.magic="\b\0",o.inherits(c,a),c.prototype.processChunk=function(e){this.meta=e.meta,null===this._pako&&this._createPako(),this._pako.push(o.transformTo(s,e.data),!1)},c.prototype.flush=function(){a.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},c.prototype.cleanUp=function(){a.prototype.cleanUp.call(this),this._pako=null},c.prototype._createPako=function(){this._pako=new i[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(t){e.push({data:t,meta:e.meta})}},r.compressWorker=function(e){return new c("Deflate",e)},r.uncompressWorker=function(){return new c("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(e,t,r){function n(e,t){var r,n="";for(r=0;r<t;r++)n+=String.fromCharCode(255&e),e>>>=8;return n}function i(e,t,r,i,a,l){var h,d,f=e.file,p=e.compression,m=l!==s.utf8encode,g=o.transformTo("string",l(f.name)),v=o.transformTo("string",s.utf8encode(f.name)),y=f.comment,b=o.transformTo("string",l(y)),_=o.transformTo("string",s.utf8encode(y)),w=v.length!==f.name.length,k=_.length!==y.length,x="",S="",E="",A=f.dir,T=f.date,R={crc32:0,compressedSize:0,uncompressedSize:0};t&&!r||(R.crc32=e.crc32,R.compressedSize=e.compressedSize,R.uncompressedSize=e.uncompressedSize);var C=0;t&&(C|=8),m||!w&&!k||(C|=2048);var I,O,D,B=0,L=0;A&&(B|=16),"UNIX"===a?(L=798,B|=(I=f.unixPermissions,O=A,D=I,I||(D=O?16893:33204),(65535&D)<<16)):(L=20,B|=function(e){return 63&(e||0)}(f.dosPermissions)),h=T.getUTCHours(),h<<=6,h|=T.getUTCMinutes(),h<<=5,h|=T.getUTCSeconds()/2,d=T.getUTCFullYear()-1980,d<<=4,d|=T.getUTCMonth()+1,d<<=5,d|=T.getUTCDate(),w&&(S=n(1,1)+n(c(g),4)+v,x+="up"+n(S.length,2)+S),k&&(E=n(1,1)+n(c(b),4)+_,x+="uc"+n(E.length,2)+E);var P="";return P+="\n\0",P+=n(C,2),P+=p.magic,P+=n(h,2),P+=n(d,2),P+=n(R.crc32,4),P+=n(R.compressedSize,4),P+=n(R.uncompressedSize,4),P+=n(g.length,2),P+=n(x.length,2),{fileRecord:u.LOCAL_FILE_HEADER+P+g+x,dirRecord:u.CENTRAL_FILE_HEADER+n(L,2)+P+n(b.length,2)+"\0\0\0\0"+n(B,4)+n(i,4)+g+x+b}}var o=e("../utils"),a=e("../stream/GenericWorker"),s=e("../utf8"),c=e("../crc32"),u=e("../signature");function l(e,t,r,n){a.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=t,this.zipPlatform=r,this.encodeFileName=n,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}o.inherits(l,a),l.prototype.push=function(e){var t=e.meta.percent||0,r=this.entriesCount,n=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,a.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:r?(t+100*(r-n-1))/r:100}}))},l.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var t=this.streamFiles&&!e.file.dir;if(t){var r=i(e,t,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},l.prototype.closedSource=function(e){this.accumulate=!1;var t,r=this.streamFiles&&!e.file.dir,o=i(e,r,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(o.dirRecord),r)this.push({data:(t=e,u.DATA_DESCRIPTOR+n(t.crc32,4)+n(t.compressedSize,4)+n(t.uncompressedSize,4)),meta:{percent:100}});else for(this.push({data:o.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},l.prototype.flush=function(){for(var e=this.bytesWritten,t=0;t<this.dirRecords.length;t++)this.push({data:this.dirRecords[t],meta:{percent:100}});var r,i,a,s,c,l,h=this.bytesWritten-e,d=(r=this.dirRecords.length,i=h,a=e,s=this.zipComment,c=this.encodeFileName,l=o.transformTo("string",c(s)),u.CENTRAL_DIRECTORY_END+"\0\0\0\0"+n(r,2)+n(r,2)+n(i,4)+n(a,4)+n(l.length,2)+l);this.push({data:d,meta:{percent:100}})},l.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},l.prototype.registerPrevious=function(e){this._sources.push(e);var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.closedSource(t.previous.streamInfo),t._sources.length?t.prepareNextSource():t.end()})),e.on("error",(function(e){t.error(e)})),this},l.prototype.resume=function(){return!!a.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},l.prototype.error=function(e){var t=this._sources;if(!a.prototype.error.call(this,e))return!1;for(var r=0;r<t.length;r++)try{t[r].error(e)}catch(n){}return!0},l.prototype.lock=function(){a.prototype.lock.call(this);for(var e=this._sources,t=0;t<e.length;t++)e[t].lock()},t.exports=l},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(e,t,r){var n=e("../compressions"),i=e("./ZipFileWorker");r.generateWorker=function(e,t,r){var o=new i(t.streamFiles,r,t.platform,t.encodeFileName),a=0;try{e.forEach((function(e,r){a++;var i=function(e,t){var r=e||t,i=n[r];if(!i)throw new Error(r+" is not a valid compression method !");return i}(r.options.compression,t.compression),s=r.options.compressionOptions||t.compressionOptions||{},c=r.dir,u=r.date;r._compressWorker(i,s).withStreamInfo("file",{name:e,dir:c,date:u,comment:r.comment||"",unixPermissions:r.unixPermissions,dosPermissions:r.dosPermissions}).pipe(o)})),o.entriesCount=a}catch(s){o.error(s)}return o}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(e,t,r){function n(){if(!(this instanceof n))return new n;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var e=new n;for(var t in this)"function"!=typeof this[t]&&(e[t]=this[t]);return e}}(n.prototype=e("./object")).loadAsync=e("./load"),n.support=e("./support"),n.defaults=e("./defaults"),n.version="3.10.1",n.loadAsync=function(e,t){return(new n).loadAsync(e,t)},n.external=e("./external"),t.exports=n},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(e,t,r){var n=e("./utils"),i=e("./external"),o=e("./utf8"),a=e("./zipEntries"),s=e("./stream/Crc32Probe"),c=e("./nodejsUtils");function u(e){return new i.Promise((function(t,r){var n=e.decompressed.getContentWorker().pipe(new s);n.on("error",(function(e){r(e)})).on("end",(function(){n.streamInfo.crc32!==e.decompressed.crc32?r(new Error("Corrupted zip : CRC32 mismatch")):t()})).resume()}))}t.exports=function(e,t){var r=this;return t=n.extend(t||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:o.utf8decode}),c.isNode&&c.isStream(e)?i.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):n.prepareContent("the loaded zip file",e,!0,t.optimizedBinaryString,t.base64).then((function(e){var r=new a(t);return r.load(e),r})).then((function(e){var r=[i.Promise.resolve(e)],n=e.files;if(t.checkCRC32)for(var o=0;o<n.length;o++)r.push(u(n[o]));return i.Promise.all(r)})).then((function(e){for(var i=e.shift(),o=i.files,a=0;a<o.length;a++){var s=o[a],c=s.fileNameStr,u=n.resolve(s.fileNameStr);r.file(u,s.decompressed,{binary:!0,optimizedBinaryString:!0,date:s.date,dir:s.dir,comment:s.fileCommentStr.length?s.fileCommentStr:null,unixPermissions:s.unixPermissions,dosPermissions:s.dosPermissions,createFolders:t.createFolders}),s.dir||(r.file(u).unsafeOriginalName=c)}return i.zipComment.length&&(r.comment=i.zipComment),r}))}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(e,t,r){var n=e("../utils"),i=e("../stream/GenericWorker");function o(e,t){i.call(this,"Nodejs stream input adapter for "+e),this._upstreamEnded=!1,this._bindStream(t)}n.inherits(o,i),o.prototype._bindStream=function(e){var t=this;(this._stream=e).pause(),e.on("data",(function(e){t.push({data:e,meta:{percent:0}})})).on("error",(function(e){t.isPaused?this.generatedError=e:t.error(e)})).on("end",(function(){t.isPaused?t._upstreamEnded=!0:t.end()}))},o.prototype.pause=function(){return!!i.prototype.pause.call(this)&&(this._stream.pause(),!0)},o.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},t.exports=o},{"../stream/GenericWorker":28,"../utils":32}],13:[function(e,t,r){var n=e("readable-stream").Readable;function i(e,t,r){n.call(this,t),this._helper=e;var i=this;e.on("data",(function(e,t){i.push(e)||i._helper.pause(),r&&r(t)})).on("error",(function(e){i.emit("error",e)})).on("end",(function(){i.push(null)}))}e("../utils").inherits(i,n),i.prototype._read=function(){this._helper.resume()},t.exports=i},{"../utils":32,"readable-stream":16}],14:[function(e,t,r){t.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(e,t){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(e,t);if("number"==typeof e)throw new Error('The "data" argument must not be a number');return new Buffer(e,t)},allocBuffer:function(e){if(Buffer.alloc)return Buffer.alloc(e);var t=new Buffer(e);return t.fill(0),t},isBuffer:function(e){return Buffer.isBuffer(e)},isStream:function(e){return e&&"function"==typeof e.on&&"function"==typeof e.pause&&"function"==typeof e.resume}}},{}],15:[function(e,t,r){function n(e,t,r){var n,i=o.getTypeOf(t),s=o.extend(r||{},c);s.date=s.date||new Date,null!==s.compression&&(s.compression=s.compression.toUpperCase()),"string"==typeof s.unixPermissions&&(s.unixPermissions=parseInt(s.unixPermissions,8)),s.unixPermissions&&16384&s.unixPermissions&&(s.dir=!0),s.dosPermissions&&16&s.dosPermissions&&(s.dir=!0),s.dir&&(e=m(e)),s.createFolders&&(n=p(e))&&g.call(this,n,!0);var h="string"===i&&!1===s.binary&&!1===s.base64;r&&void 0!==r.binary||(s.binary=!h),(t instanceof u&&0===t.uncompressedSize||s.dir||!t||0===t.length)&&(s.base64=!1,s.binary=!0,t="",s.compression="STORE",i="string");var v;v=t instanceof u||t instanceof a?t:d.isNode&&d.isStream(t)?new f(e,t):o.prepareContent(e,t,s.binary,s.optimizedBinaryString,s.base64);var y=new l(e,v,s);this.files[e]=y}var i=e("./utf8"),o=e("./utils"),a=e("./stream/GenericWorker"),s=e("./stream/StreamHelper"),c=e("./defaults"),u=e("./compressedObject"),l=e("./zipObject"),h=e("./generate"),d=e("./nodejsUtils"),f=e("./nodejs/NodejsStreamInputAdapter"),p=function(e){"/"===e.slice(-1)&&(e=e.substring(0,e.length-1));var t=e.lastIndexOf("/");return 0<t?e.substring(0,t):""},m=function(e){return"/"!==e.slice(-1)&&(e+="/"),e},g=function(e,t){return t=void 0!==t?t:c.createFolders,e=m(e),this.files[e]||n.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]};function v(e){return"[object RegExp]"===Object.prototype.toString.call(e)}var y={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(e){var t,r,n;for(t in this.files)n=this.files[t],(r=t.slice(this.root.length,t.length))&&t.slice(0,this.root.length)===this.root&&e(r,n)},filter:function(e){var t=[];return this.forEach((function(r,n){e(r,n)&&t.push(n)})),t},file:function(e,t,r){if(1!==arguments.length)return e=this.root+e,n.call(this,e,t,r),this;if(v(e)){var i=e;return this.filter((function(e,t){return!t.dir&&i.test(e)}))}var o=this.files[this.root+e];return o&&!o.dir?o:null},folder:function(e){if(!e)return this;if(v(e))return this.filter((function(t,r){return r.dir&&e.test(t)}));var t=this.root+e,r=g.call(this,t),n=this.clone();return n.root=r.name,n},remove:function(e){e=this.root+e;var t=this.files[e];if(t||("/"!==e.slice(-1)&&(e+="/"),t=this.files[e]),t&&!t.dir)delete this.files[e];else for(var r=this.filter((function(t,r){return r.name.slice(0,e.length)===e})),n=0;n<r.length;n++)delete this.files[r[n].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(e){var t,r={};try{if((r=o.extend(e||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:i.utf8encode})).type=r.type.toLowerCase(),r.compression=r.compression.toUpperCase(),"binarystring"===r.type&&(r.type="string"),!r.type)throw new Error("No output type specified.");o.checkSupport(r.type),"darwin"!==r.platform&&"freebsd"!==r.platform&&"linux"!==r.platform&&"sunos"!==r.platform||(r.platform="UNIX"),"win32"===r.platform&&(r.platform="DOS");var n=r.comment||this.comment||"";t=h.generateWorker(this,r,n)}catch(c){(t=new a("error")).error(c)}return new s(t,r.type||"string",r.mimeType)},generateAsync:function(e,t){return this.generateInternalStream(e).accumulate(t)},generateNodeStream:function(e,t){return(e=e||{}).type||(e.type="nodebuffer"),this.generateInternalStream(e).toNodejsStream(t)}};t.exports=y},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(e,t,r){t.exports=e("stream")},{stream:void 0}],17:[function(e,t,r){var n=e("./DataReader");function i(e){n.call(this,e);for(var t=0;t<this.data.length;t++)e[t]=255&e[t]}e("../utils").inherits(i,n),i.prototype.byteAt=function(e){return this.data[this.zero+e]},i.prototype.lastIndexOfSignature=function(e){for(var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),i=e.charCodeAt(3),o=this.length-4;0<=o;--o)if(this.data[o]===t&&this.data[o+1]===r&&this.data[o+2]===n&&this.data[o+3]===i)return o-this.zero;return-1},i.prototype.readAndCheckSignature=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),i=e.charCodeAt(3),o=this.readData(4);return t===o[0]&&r===o[1]&&n===o[2]&&i===o[3]},i.prototype.readData=function(e){if(this.checkOffset(e),0===e)return[];var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{"../utils":32,"./DataReader":18}],18:[function(e,t,r){var n=e("../utils");function i(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}i.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(){},readInt:function(e){var t,r=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(e){return n.transformTo("string",this.readData(e))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var e=this.readInt(4);return new Date(Date.UTC(1980+(e>>25&127),(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1))}},t.exports=i},{"../utils":32}],19:[function(e,t,r){var n=e("./Uint8ArrayReader");function i(e){n.call(this,e)}e("../utils").inherits(i,n),i.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(e,t,r){var n=e("./DataReader");function i(e){n.call(this,e)}e("../utils").inherits(i,n),i.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)},i.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero},i.prototype.readAndCheckSignature=function(e){return e===this.readData(4)},i.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{"../utils":32,"./DataReader":18}],21:[function(e,t,r){var n=e("./ArrayReader");function i(e){n.call(this,e)}e("../utils").inherits(i,n),i.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},t.exports=i},{"../utils":32,"./ArrayReader":17}],22:[function(e,t,r){var n=e("../utils"),i=e("../support"),o=e("./ArrayReader"),a=e("./StringReader"),s=e("./NodeBufferReader"),c=e("./Uint8ArrayReader");t.exports=function(e){var t=n.getTypeOf(e);return n.checkSupport(t),"string"!==t||i.uint8array?"nodebuffer"===t?new s(e):i.uint8array?new c(n.transformTo("uint8array",e)):new o(n.transformTo("array",e)):new a(e)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(e,t,r){r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\b"},{}],24:[function(e,t,r){var n=e("./GenericWorker"),i=e("../utils");function o(e){n.call(this,"ConvertWorker to "+e),this.destType=e}i.inherits(o,n),o.prototype.processChunk=function(e){this.push({data:i.transformTo(this.destType,e.data),meta:e.meta})},t.exports=o},{"../utils":32,"./GenericWorker":28}],25:[function(e,t,r){var n=e("./GenericWorker"),i=e("../crc32");function o(){n.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}e("../utils").inherits(o,n),o.prototype.processChunk=function(e){this.streamInfo.crc32=i(e.data,this.streamInfo.crc32||0),this.push(e)},t.exports=o},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(e,t,r){var n=e("../utils"),i=e("./GenericWorker");function o(e){i.call(this,"DataLengthProbe for "+e),this.propName=e,this.withStreamInfo(e,0)}n.inherits(o,i),o.prototype.processChunk=function(e){if(e){var t=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=t+e.data.length}i.prototype.processChunk.call(this,e)},t.exports=o},{"../utils":32,"./GenericWorker":28}],27:[function(e,t,r){var n=e("../utils"),i=e("./GenericWorker");function o(e){i.call(this,"DataWorker");var t=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,e.then((function(e){t.dataIsReady=!0,t.data=e,t.max=e&&e.length||0,t.type=n.getTypeOf(e),t.isPaused||t._tickAndRepeat()}),(function(e){t.error(e)}))}n.inherits(o,i),o.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this.data=null},o.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,n.delay(this._tickAndRepeat,[],this)),!0)},o.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(n.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},o.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=null,t=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":e=this.data.substring(this.index,t);break;case"uint8array":e=this.data.subarray(this.index,t);break;case"array":case"nodebuffer":e=this.data.slice(this.index,t)}return this.index=t,this.push({data:e,meta:{percent:this.max?this.index/this.max*100:0}})},t.exports=o},{"../utils":32,"./GenericWorker":28}],28:[function(e,t,r){function n(e){this.name=e||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}n.prototype={push:function(e){this.emit("data",e)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit("error",e)}return!0},error:function(e){return!this.isFinished&&(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit("error",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,t){return this._listeners[e].push(t),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,t){if(this._listeners[e])for(var r=0;r<this._listeners[e].length;r++)this._listeners[e][r].call(this,t)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.end()})),e.on("error",(function(e){t.error(e)})),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var e=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,t){return this.extraStreamInfo[e]=t,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var e in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e="Worker "+this.name;return this.previous?this.previous+" -> "+e:e}},t.exports=n},{}],29:[function(e,t,r){var n=e("../utils"),i=e("./ConvertWorker"),o=e("./GenericWorker"),a=e("../base64"),s=e("../support"),c=e("../external"),u=null;if(s.nodestream)try{u=e("../nodejs/NodejsStreamOutputAdapter")}catch(h){}function l(e,t,r){var a=t;switch(t){case"blob":case"arraybuffer":a="uint8array";break;case"base64":a="string"}try{this._internalType=a,this._outputType=t,this._mimeType=r,n.checkSupport(a),this._worker=e.pipe(new i(a)),e.lock()}catch(s){this._worker=new o("error"),this._worker.error(s)}}l.prototype={accumulate:function(e){return function(e,t){return new c.Promise((function(r,i){var o=[],s=e._internalType,c=e._outputType,u=e._mimeType;e.on("data",(function(e,r){o.push(e),t&&t(r)})).on("error",(function(e){o=[],i(e)})).on("end",(function(){try{var e=function(e,t,r){switch(e){case"blob":return n.newBlob(n.transformTo("arraybuffer",t),r);case"base64":return a.encode(t);default:return n.transformTo(e,t)}}(c,function(e,t){var r,n=0,i=null,o=0;for(r=0;r<t.length;r++)o+=t[r].length;switch(e){case"string":return t.join("");case"array":return Array.prototype.concat.apply([],t);case"uint8array":for(i=new Uint8Array(o),r=0;r<t.length;r++)i.set(t[r],n),n+=t[r].length;return i;case"nodebuffer":return Buffer.concat(t);default:throw new Error("concat : unsupported type '"+e+"'")}}(s,o),u);r(e)}catch(t){i(t)}o=[]})).resume()}))}(this,e)},on:function(e,t){var r=this;return"data"===e?this._worker.on(e,(function(e){t.call(r,e.data,e.meta)})):this._worker.on(e,(function(){n.delay(t,arguments,r)})),this},resume:function(){return n.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(e){if(n.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new u(this,{objectMode:"nodebuffer"!==this._outputType},e)}},t.exports=l},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(e,t,r){if(r.base64=!0,r.array=!0,r.string=!0,r.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,r.nodebuffer="undefined"!=typeof Buffer,r.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)r.blob=!1;else{var n=new ArrayBuffer(0);try{r.blob=0===new Blob([n],{type:"application/zip"}).size}catch(o){try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);i.append(n),r.blob=0===i.getBlob("application/zip").size}catch(MS){r.blob=!1}}}try{r.nodestream=!!e("readable-stream").Readable}catch(o){r.nodestream=!1}},{"readable-stream":16}],31:[function(e,t,r){for(var n=e("./utils"),i=e("./support"),o=e("./nodejsUtils"),a=e("./stream/GenericWorker"),s=new Array(256),c=0;c<256;c++)s[c]=252<=c?6:248<=c?5:240<=c?4:224<=c?3:192<=c?2:1;function u(){a.call(this,"utf-8 decode"),this.leftOver=null}function l(){a.call(this,"utf-8 encode")}s[254]=s[254]=1,r.utf8encode=function(e){return i.nodebuffer?o.newBufferFrom(e,"utf-8"):function(e){var t,r,n,o,a,s=e.length,c=0;for(o=0;o<s;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(n=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(n-56320),o++),c+=r<128?1:r<2048?2:r<65536?3:4;for(t=i.uint8array?new Uint8Array(c):new Array(c),o=a=0;a<c;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(n=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(n-56320),o++),r<128?t[a++]=r:(r<2048?t[a++]=192|r>>>6:(r<65536?t[a++]=224|r>>>12:(t[a++]=240|r>>>18,t[a++]=128|r>>>12&63),t[a++]=128|r>>>6&63),t[a++]=128|63&r);return t}(e)},r.utf8decode=function(e){return i.nodebuffer?n.transformTo("nodebuffer",e).toString("utf-8"):function(e){var t,r,i,o,a=e.length,c=new Array(2*a);for(t=r=0;t<a;)if((i=e[t++])<128)c[r++]=i;else if(4<(o=s[i]))c[r++]=65533,t+=o-1;else{for(i&=2===o?31:3===o?15:7;1<o&&t<a;)i=i<<6|63&e[t++],o--;1<o?c[r++]=65533:i<65536?c[r++]=i:(i-=65536,c[r++]=55296|i>>10&1023,c[r++]=56320|1023&i)}return c.length!==r&&(c.subarray?c=c.subarray(0,r):c.length=r),n.applyFromCharCode(c)}(e=n.transformTo(i.uint8array?"uint8array":"array",e))},n.inherits(u,a),u.prototype.processChunk=function(e){var t=n.transformTo(i.uint8array?"uint8array":"array",e.data);if(this.leftOver&&this.leftOver.length){if(i.uint8array){var o=t;(t=new Uint8Array(o.length+this.leftOver.length)).set(this.leftOver,0),t.set(o,this.leftOver.length)}else t=this.leftOver.concat(t);this.leftOver=null}var a=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;0<=r&&128==(192&e[r]);)r--;return r<0||0===r?t:r+s[e[r]]>t?r:t}(t),c=t;a!==t.length&&(i.uint8array?(c=t.subarray(0,a),this.leftOver=t.subarray(a,t.length)):(c=t.slice(0,a),this.leftOver=t.slice(a,t.length))),this.push({data:r.utf8decode(c),meta:e.meta})},u.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:r.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},r.Utf8DecodeWorker=u,n.inherits(l,a),l.prototype.processChunk=function(e){this.push({data:r.utf8encode(e.data),meta:e.meta})},r.Utf8EncodeWorker=l},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(e,t,r){var n=e("./support"),i=e("./base64"),o=e("./nodejsUtils"),a=e("./external");function s(e){return e}function c(e,t){for(var r=0;r<e.length;++r)t[r]=255&e.charCodeAt(r);return t}e("setimmediate"),r.newBlob=function(e,t){r.checkSupport("blob");try{return new Blob([e],{type:t})}catch(i){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return n.append(e),n.getBlob(t)}catch(o){throw new Error("Bug : can't construct the Blob.")}}};var u={stringifyByChunk:function(e,t,r){var n=[],i=0,o=e.length;if(o<=r)return String.fromCharCode.apply(null,e);for(;i<o;)"array"===t||"nodebuffer"===t?n.push(String.fromCharCode.apply(null,e.slice(i,Math.min(i+r,o)))):n.push(String.fromCharCode.apply(null,e.subarray(i,Math.min(i+r,o)))),i+=r;return n.join("")},stringifyByChar:function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},applyCanBeUsed:{uint8array:function(){try{return n.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(e){return!1}}(),nodebuffer:function(){try{return n.nodebuffer&&1===String.fromCharCode.apply(null,o.allocBuffer(1)).length}catch(e){return!1}}()}};function l(e){var t=65536,n=r.getTypeOf(e),i=!0;if("uint8array"===n?i=u.applyCanBeUsed.uint8array:"nodebuffer"===n&&(i=u.applyCanBeUsed.nodebuffer),i)for(;1<t;)try{return u.stringifyByChunk(e,n,t)}catch(o){t=Math.floor(t/2)}return u.stringifyByChar(e)}function h(e,t){for(var r=0;r<e.length;r++)t[r]=e[r];return t}r.applyFromCharCode=l;var d={};d.string={string:s,array:function(e){return c(e,new Array(e.length))},arraybuffer:function(e){return d.string.uint8array(e).buffer},uint8array:function(e){return c(e,new Uint8Array(e.length))},nodebuffer:function(e){return c(e,o.allocBuffer(e.length))}},d.array={string:l,array:s,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return o.newBufferFrom(e)}},d.arraybuffer={string:function(e){return l(new Uint8Array(e))},array:function(e){return h(new Uint8Array(e),new Array(e.byteLength))},arraybuffer:s,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return o.newBufferFrom(new Uint8Array(e))}},d.uint8array={string:l,array:function(e){return h(e,new Array(e.length))},arraybuffer:function(e){return e.buffer},uint8array:s,nodebuffer:function(e){return o.newBufferFrom(e)}},d.nodebuffer={string:l,array:function(e){return h(e,new Array(e.length))},arraybuffer:function(e){return d.nodebuffer.uint8array(e).buffer},uint8array:function(e){return h(e,new Uint8Array(e.length))},nodebuffer:s},r.transformTo=function(e,t){if(t=t||"",!e)return t;r.checkSupport(e);var n=r.getTypeOf(t);return d[n][e](t)},r.resolve=function(e){for(var t=e.split("/"),r=[],n=0;n<t.length;n++){var i=t[n];"."===i||""===i&&0!==n&&n!==t.length-1||(".."===i?r.pop():r.push(i))}return r.join("/")},r.getTypeOf=function(e){return"string"==typeof e?"string":"[object Array]"===Object.prototype.toString.call(e)?"array":n.nodebuffer&&o.isBuffer(e)?"nodebuffer":n.uint8array&&e instanceof Uint8Array?"uint8array":n.arraybuffer&&e instanceof ArrayBuffer?"arraybuffer":void 0},r.checkSupport=function(e){if(!n[e.toLowerCase()])throw new Error(e+" is not supported by this platform")},r.MAX_VALUE_16BITS=65535,r.MAX_VALUE_32BITS=-1,r.pretty=function(e){var t,r,n="";for(r=0;r<(e||"").length;r++)n+="\\x"+((t=e.charCodeAt(r))<16?"0":"")+t.toString(16).toUpperCase();return n},r.delay=function(e,t,r){setImmediate((function(){e.apply(r||null,t||[])}))},r.inherits=function(e,t){function r(){}r.prototype=t.prototype,e.prototype=new r},r.extend=function(){var e,t,r={};for(e=0;e<arguments.length;e++)for(t in arguments[e])Object.prototype.hasOwnProperty.call(arguments[e],t)&&void 0===r[t]&&(r[t]=arguments[e][t]);return r},r.prepareContent=function(e,t,o,s,u){return a.Promise.resolve(t).then((function(e){return n.blob&&(e instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(e)))&&"undefined"!=typeof FileReader?new a.Promise((function(t,r){var n=new FileReader;n.onload=function(e){t(e.target.result)},n.onerror=function(e){r(e.target.error)},n.readAsArrayBuffer(e)})):e})).then((function(t){var l,h=r.getTypeOf(t);return h?("arraybuffer"===h?t=r.transformTo("uint8array",t):"string"===h&&(u?t=i.decode(t):o&&!0!==s&&(t=c(l=t,n.uint8array?new Uint8Array(l.length):new Array(l.length)))),t):a.Promise.reject(new Error("Can't read the data of '"+e+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))}))}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,setimmediate:54}],33:[function(e,t,r){var n=e("./reader/readerFor"),i=e("./utils"),o=e("./signature"),a=e("./zipEntry"),s=e("./support");function c(e){this.files=[],this.loadOptions=e}c.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var t=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+i.pretty(t)+", expected "+i.pretty(e)+")")}},isSignature:function(e,t){var r=this.reader.index;this.reader.setIndex(e);var n=this.reader.readString(4)===t;return this.reader.setIndex(r),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var e=this.reader.readData(this.zipCommentLength),t=s.uint8array?"uint8array":"array",r=i.transformTo(t,e);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,r,n=this.zip64EndOfCentralSize-44;0<n;)e=this.reader.readInt(2),t=this.reader.readInt(4),r=this.reader.readData(t),this.zip64ExtensibleData[e]={id:e,length:t,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var e,t;for(e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(o.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(o.CENTRAL_FILE_HEADER);)(e=new a({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(o.CENTRAL_DIRECTORY_END);if(e<0)throw this.isSignature(0,o.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(e);var t=e;if(this.checkSignature(o.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,(e=this.reader.lastIndexOfSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(e),this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,o.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(o.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var r=this.centralDirOffset+this.centralDirSize;this.zip64&&(r+=20,r+=12+this.zip64EndOfCentralSize);var n=t-r;if(0<n)this.isSignature(t,o.CENTRAL_FILE_HEADER)||(this.reader.zero=n);else if(n<0)throw new Error("Corrupted zip: missing "+Math.abs(n)+" bytes.")},prepareReader:function(e){this.reader=n(e)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},t.exports=c},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utils":32,"./zipEntry":34}],34:[function(e,t,r){var n=e("./reader/readerFor"),i=e("./utils"),o=e("./compressedObject"),a=e("./crc32"),s=e("./utf8"),c=e("./compressions"),u=e("./support");function l(e,t){this.options=e,this.loadOptions=t}l.prototype={isEncrypted:function(){return!(1&~this.bitFlag)},useUTF8:function(){return!(2048&~this.bitFlag)},readLocalPart:function(e){var t,r;if(e.skip(22),this.fileNameLength=e.readInt(2),r=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(r),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(t=function(e){for(var t in c)if(Object.prototype.hasOwnProperty.call(c,t)&&c[t].magic===e)return c[t];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+i.pretty(this.compressionMethod)+" unknown (inner file : "+i.transformTo("string",this.fileName)+")");this.decompressed=new o(this.compressedSize,this.uncompressedSize,this.crc32,t,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var t=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");e.skip(t),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==e&&(this.dosPermissions=63&this.externalFileAttributes),3==e&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var e=n(this.extraFields[1].value);this.uncompressedSize===i.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===i.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===i.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===i.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(e){var t,r,n,i=e.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});e.index+4<i;)t=e.readInt(2),r=e.readInt(2),n=e.readData(r),this.extraFields[t]={id:t,length:r,value:n};e.setIndex(i)},handleUTF8:function(){var e=u.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=s.utf8decode(this.fileName),this.fileCommentStr=s.utf8decode(this.fileComment);else{var t=this.findExtraFieldUnicodePath();if(null!==t)this.fileNameStr=t;else{var r=i.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var n=this.findExtraFieldUnicodeComment();if(null!==n)this.fileCommentStr=n;else{var o=i.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(o)}}},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var t=n(e.value);return 1!==t.readInt(1)||a(this.fileName)!==t.readInt(4)?null:s.utf8decode(t.readData(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var t=n(e.value);return 1!==t.readInt(1)||a(this.fileComment)!==t.readInt(4)?null:s.utf8decode(t.readData(e.length-5))}return null}},t.exports=l},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(e,t,r){function n(e,t,r){this.name=e,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=t,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}}var i=e("./stream/StreamHelper"),o=e("./stream/DataWorker"),a=e("./utf8"),s=e("./compressedObject"),c=e("./stream/GenericWorker");n.prototype={internalStream:function(e){var t=null,r="string";try{if(!e)throw new Error("No output type specified.");var n="string"===(r=e.toLowerCase())||"text"===r;"binarystring"!==r&&"text"!==r||(r="string"),t=this._decompressWorker();var o=!this._dataBinary;o&&!n&&(t=t.pipe(new a.Utf8EncodeWorker)),!o&&n&&(t=t.pipe(new a.Utf8DecodeWorker))}catch(s){(t=new c("error")).error(s)}return new i(t,r,"")},async:function(e,t){return this.internalStream(e).accumulate(t)},nodeStream:function(e,t){return this.internalStream(e||"nodebuffer").toNodejsStream(t)},_compressWorker:function(e,t){if(this._data instanceof s&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var r=this._decompressWorker();return this._dataBinary||(r=r.pipe(new a.Utf8EncodeWorker)),s.createWorkerFrom(r,e,t)},_decompressWorker:function(){return this._data instanceof s?this._data.getContentWorker():this._data instanceof c?this._data:new o(this._data)}};for(var u=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],l=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},h=0;h<u.length;h++)n.prototype[u[h]]=l;t.exports=n},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(e,t,r){(function(e){var r,n,i=e.MutationObserver||e.WebKitMutationObserver;if(i){var o=0,a=new i(l),s=e.document.createTextNode("");a.observe(s,{characterData:!0}),r=function(){s.data=o=++o%2}}else if(e.setImmediate||void 0===e.MessageChannel)r="document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){l(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(l,0)};else{var c=new e.MessageChannel;c.port1.onmessage=l,r=function(){c.port2.postMessage(0)}}var u=[];function l(){var e,t;n=!0;for(var r=u.length;r;){for(t=u,u=[],e=-1;++e<r;)t[e]();r=u.length}n=!1}t.exports=function(e){1!==u.push(e)||n||r()}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(e,t,r){var n=e("immediate");function i(){}var o={},a=["REJECTED"],s=["FULFILLED"],c=["PENDING"];function u(e){if("function"!=typeof e)throw new TypeError("resolver must be a function");this.state=c,this.queue=[],this.outcome=void 0,e!==i&&f(this,e)}function l(e,t,r){this.promise=e,"function"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),"function"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function h(e,t,r){n((function(){var n;try{n=t(r)}catch(i){return o.reject(e,i)}n===e?o.reject(e,new TypeError("Cannot resolve promise with itself")):o.resolve(e,n)}))}function d(e){var t=e&&e.then;if(e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof t)return function(){t.apply(e,arguments)}}function f(e,t){var r=!1;function n(t){r||(r=!0,o.reject(e,t))}function i(t){r||(r=!0,o.resolve(e,t))}var a=p((function(){t(i,n)}));"error"===a.status&&n(a.value)}function p(e,t){var r={};try{r.value=e(t),r.status="success"}catch(n){r.status="error",r.value=n}return r}(t.exports=u).prototype.finally=function(e){if("function"!=typeof e)return this;var t=this.constructor;return this.then((function(r){return t.resolve(e()).then((function(){return r}))}),(function(r){return t.resolve(e()).then((function(){throw r}))}))},u.prototype.catch=function(e){return this.then(null,e)},u.prototype.then=function(e,t){if("function"!=typeof e&&this.state===s||"function"!=typeof t&&this.state===a)return this;var r=new this.constructor(i);return this.state!==c?h(r,this.state===s?e:t,this.outcome):this.queue.push(new l(r,e,t)),r},l.prototype.callFulfilled=function(e){o.resolve(this.promise,e)},l.prototype.otherCallFulfilled=function(e){h(this.promise,this.onFulfilled,e)},l.prototype.callRejected=function(e){o.reject(this.promise,e)},l.prototype.otherCallRejected=function(e){h(this.promise,this.onRejected,e)},o.resolve=function(e,t){var r=p(d,t);if("error"===r.status)return o.reject(e,r.value);var n=r.value;if(n)f(e,n);else{e.state=s,e.outcome=t;for(var i=-1,a=e.queue.length;++i<a;)e.queue[i].callFulfilled(t)}return e},o.reject=function(e,t){e.state=a,e.outcome=t;for(var r=-1,n=e.queue.length;++r<n;)e.queue[r].callRejected(t);return e},u.resolve=function(e){return e instanceof this?e:o.resolve(new this(i),e)},u.reject=function(e){var t=new this(i);return o.reject(t,e)},u.all=function(e){var t=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var r=e.length,n=!1;if(!r)return this.resolve([]);for(var a=new Array(r),s=0,c=-1,u=new this(i);++c<r;)l(e[c],c);return u;function l(e,i){t.resolve(e).then((function(e){a[i]=e,++s!==r||n||(n=!0,o.resolve(u,a))}),(function(e){n||(n=!0,o.reject(u,e))}))}},u.race=function(e){if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var t,r=e.length,n=!1;if(!r)return this.resolve([]);for(var a=-1,s=new this(i);++a<r;)t=e[a],this.resolve(t).then((function(e){n||(n=!0,o.resolve(s,e))}),(function(e){n||(n=!0,o.reject(s,e))}));return s}},{immediate:36}],38:[function(e,t,r){var n={};(0,e("./lib/utils/common").assign)(n,e("./lib/deflate"),e("./lib/inflate"),e("./lib/zlib/constants")),t.exports=n},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(e,t,r){var n=e("./zlib/deflate"),i=e("./utils/common"),o=e("./utils/strings"),a=e("./zlib/messages"),s=e("./zlib/zstream"),c=Object.prototype.toString;function u(e){if(!(this instanceof u))return new u(e);this.options=i.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},e||{});var t=this.options;t.raw&&0<t.windowBits?t.windowBits=-t.windowBits:t.gzip&&0<t.windowBits&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new s,this.strm.avail_out=0;var r=n.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(0!==r)throw new Error(a[r]);if(t.header&&n.deflateSetHeader(this.strm,t.header),t.dictionary){var l;if(l="string"==typeof t.dictionary?o.string2buf(t.dictionary):"[object ArrayBuffer]"===c.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,0!==(r=n.deflateSetDictionary(this.strm,l)))throw new Error(a[r]);this._dict_set=!0}}function l(e,t){var r=new u(t);if(r.push(e,!0),r.err)throw r.msg||a[r.err];return r.result}u.prototype.push=function(e,t){var r,a,s=this.strm,u=this.options.chunkSize;if(this.ended)return!1;a=t===~~t?t:!0===t?4:0,"string"==typeof e?s.input=o.string2buf(e):"[object ArrayBuffer]"===c.call(e)?s.input=new Uint8Array(e):s.input=e,s.next_in=0,s.avail_in=s.input.length;do{if(0===s.avail_out&&(s.output=new i.Buf8(u),s.next_out=0,s.avail_out=u),1!==(r=n.deflate(s,a))&&0!==r)return this.onEnd(r),!(this.ended=!0);0!==s.avail_out&&(0!==s.avail_in||4!==a&&2!==a)||("string"===this.options.to?this.onData(o.buf2binstring(i.shrinkBuf(s.output,s.next_out))):this.onData(i.shrinkBuf(s.output,s.next_out)))}while((0<s.avail_in||0===s.avail_out)&&1!==r);return 4===a?(r=n.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,0===r):2!==a||(this.onEnd(0),!(s.avail_out=0))},u.prototype.onData=function(e){this.chunks.push(e)},u.prototype.onEnd=function(e){0===e&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Deflate=u,r.deflate=l,r.deflateRaw=function(e,t){return(t=t||{}).raw=!0,l(e,t)},r.gzip=function(e,t){return(t=t||{}).gzip=!0,l(e,t)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(e,t,r){var n=e("./zlib/inflate"),i=e("./utils/common"),o=e("./utils/strings"),a=e("./zlib/constants"),s=e("./zlib/messages"),c=e("./zlib/zstream"),u=e("./zlib/gzheader"),l=Object.prototype.toString;function h(e){if(!(this instanceof h))return new h(e);this.options=i.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&0<=t.windowBits&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(0<=t.windowBits&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),15<t.windowBits&&t.windowBits<48&&!(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new c,this.strm.avail_out=0;var r=n.inflateInit2(this.strm,t.windowBits);if(r!==a.Z_OK)throw new Error(s[r]);this.header=new u,n.inflateGetHeader(this.strm,this.header)}function d(e,t){var r=new h(t);if(r.push(e,!0),r.err)throw r.msg||s[r.err];return r.result}h.prototype.push=function(e,t){var r,s,c,u,h,d,f=this.strm,p=this.options.chunkSize,m=this.options.dictionary,g=!1;if(this.ended)return!1;s=t===~~t?t:!0===t?a.Z_FINISH:a.Z_NO_FLUSH,"string"==typeof e?f.input=o.binstring2buf(e):"[object ArrayBuffer]"===l.call(e)?f.input=new Uint8Array(e):f.input=e,f.next_in=0,f.avail_in=f.input.length;do{if(0===f.avail_out&&(f.output=new i.Buf8(p),f.next_out=0,f.avail_out=p),(r=n.inflate(f,a.Z_NO_FLUSH))===a.Z_NEED_DICT&&m&&(d="string"==typeof m?o.string2buf(m):"[object ArrayBuffer]"===l.call(m)?new Uint8Array(m):m,r=n.inflateSetDictionary(this.strm,d)),r===a.Z_BUF_ERROR&&!0===g&&(r=a.Z_OK,g=!1),r!==a.Z_STREAM_END&&r!==a.Z_OK)return this.onEnd(r),!(this.ended=!0);f.next_out&&(0!==f.avail_out&&r!==a.Z_STREAM_END&&(0!==f.avail_in||s!==a.Z_FINISH&&s!==a.Z_SYNC_FLUSH)||("string"===this.options.to?(c=o.utf8border(f.output,f.next_out),u=f.next_out-c,h=o.buf2string(f.output,c),f.next_out=u,f.avail_out=p-u,u&&i.arraySet(f.output,f.output,c,u,0),this.onData(h)):this.onData(i.shrinkBuf(f.output,f.next_out)))),0===f.avail_in&&0===f.avail_out&&(g=!0)}while((0<f.avail_in||0===f.avail_out)&&r!==a.Z_STREAM_END);return r===a.Z_STREAM_END&&(s=a.Z_FINISH),s===a.Z_FINISH?(r=n.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===a.Z_OK):s!==a.Z_SYNC_FLUSH||(this.onEnd(a.Z_OK),!(f.avail_out=0))},h.prototype.onData=function(e){this.chunks.push(e)},h.prototype.onEnd=function(e){e===a.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Inflate=h,r.inflate=d,r.inflateRaw=function(e,t){return(t=t||{}).raw=!0,d(e,t)},r.ungzip=d},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(e,t,r){var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var i={arraySet:function(e,t,r,n,i){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),i);else for(var o=0;o<n;o++)e[i+o]=t[r+o]},flattenChunks:function(e){var t,r,n,i,o,a;for(t=n=0,r=e.length;t<r;t++)n+=e[t].length;for(a=new Uint8Array(n),t=i=0,r=e.length;t<r;t++)o=e[t],a.set(o,i),i+=o.length;return a}},o={arraySet:function(e,t,r,n,i){for(var o=0;o<n;o++)e[i+o]=t[r+o]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,i)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,o))},r.setTyped(n)},{}],42:[function(e,t,r){var n=e("./common"),i=!0,o=!0;try{String.fromCharCode.apply(null,[0])}catch(u){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(u){o=!1}for(var a=new n.Buf8(256),s=0;s<256;s++)a[s]=252<=s?6:248<=s?5:240<=s?4:224<=s?3:192<=s?2:1;function c(e,t){if(t<65537&&(e.subarray&&o||!e.subarray&&i))return String.fromCharCode.apply(null,n.shrinkBuf(e,t));for(var r="",a=0;a<t;a++)r+=String.fromCharCode(e[a]);return r}a[254]=a[254]=1,r.string2buf=function(e){var t,r,i,o,a,s=e.length,c=0;for(o=0;o<s;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(i=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(i-56320),o++),c+=r<128?1:r<2048?2:r<65536?3:4;for(t=new n.Buf8(c),o=a=0;a<c;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(i=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(i-56320),o++),r<128?t[a++]=r:(r<2048?t[a++]=192|r>>>6:(r<65536?t[a++]=224|r>>>12:(t[a++]=240|r>>>18,t[a++]=128|r>>>12&63),t[a++]=128|r>>>6&63),t[a++]=128|63&r);return t},r.buf2binstring=function(e){return c(e,e.length)},r.binstring2buf=function(e){for(var t=new n.Buf8(e.length),r=0,i=t.length;r<i;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){var r,n,i,o,s=t||e.length,u=new Array(2*s);for(r=n=0;r<s;)if((i=e[r++])<128)u[n++]=i;else if(4<(o=a[i]))u[n++]=65533,r+=o-1;else{for(i&=2===o?31:3===o?15:7;1<o&&r<s;)i=i<<6|63&e[r++],o--;1<o?u[n++]=65533:i<65536?u[n++]=i:(i-=65536,u[n++]=55296|i>>10&1023,u[n++]=56320|1023&i)}return c(u,n)},r.utf8border=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;0<=r&&128==(192&e[r]);)r--;return r<0||0===r?t:r+a[e[r]]>t?r:t}},{"./common":41}],43:[function(e,t,r){t.exports=function(e,t,r,n){for(var i=65535&e,o=e>>>16&65535,a=0;0!==r;){for(r-=a=2e3<r?2e3:r;o=o+(i=i+t[n++]|0)|0,--a;);i%=65521,o%=65521}return i|o<<16}},{}],44:[function(e,t,r){t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(e,t,r){var n=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t,r,i){var o=n,a=i+r;e^=-1;for(var s=i;s<a;s++)e=e>>>8^o[255&(e^t[s])];return~e}},{}],46:[function(e,t,r){var n,i=e("../utils/common"),o=e("./trees"),a=e("./adler32"),s=e("./crc32"),c=e("./messages"),u=-2,l=258,h=262,d=113;function f(e,t){return e.msg=c[t],t}function p(e){return(e<<1)-(4<e?9:0)}function m(e){for(var t=e.length;0<=--t;)e[t]=0}function g(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(i.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function v(e,t){o._tr_flush_block(e,0<=e.block_start?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,g(e.strm)}function y(e,t){e.pending_buf[e.pending++]=t}function b(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function _(e,t){var r,n,i=e.max_chain_length,o=e.strstart,a=e.prev_length,s=e.nice_match,c=e.strstart>e.w_size-h?e.strstart-(e.w_size-h):0,u=e.window,d=e.w_mask,f=e.prev,p=e.strstart+l,m=u[o+a-1],g=u[o+a];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(u[(r=t)+a]===g&&u[r+a-1]===m&&u[r]===u[o]&&u[++r]===u[o+1]){o+=2,r++;do{}while(u[++o]===u[++r]&&u[++o]===u[++r]&&u[++o]===u[++r]&&u[++o]===u[++r]&&u[++o]===u[++r]&&u[++o]===u[++r]&&u[++o]===u[++r]&&u[++o]===u[++r]&&o<p);if(n=l-(p-o),o=p-l,a<n){if(e.match_start=t,s<=(a=n))break;m=u[o+a-1],g=u[o+a]}}}while((t=f[t&d])>c&&0!=--i);return a<=e.lookahead?a:e.lookahead}function w(e){var t,r,n,o,c,u,l,d,f,p,m=e.w_size;do{if(o=e.window_size-e.lookahead-e.strstart,e.strstart>=m+(m-h)){for(i.arraySet(e.window,e.window,m,m,0),e.match_start-=m,e.strstart-=m,e.block_start-=m,t=r=e.hash_size;n=e.head[--t],e.head[t]=m<=n?n-m:0,--r;);for(t=r=m;n=e.prev[--t],e.prev[t]=m<=n?n-m:0,--r;);o+=m}if(0===e.strm.avail_in)break;if(u=e.strm,l=e.window,d=e.strstart+e.lookahead,p=void 0,(f=o)<(p=u.avail_in)&&(p=f),r=0===p?0:(u.avail_in-=p,i.arraySet(l,u.input,u.next_in,p,d),1===u.state.wrap?u.adler=a(u.adler,l,p,d):2===u.state.wrap&&(u.adler=s(u.adler,l,p,d)),u.next_in+=p,u.total_in+=p,p),e.lookahead+=r,e.lookahead+e.insert>=3)for(c=e.strstart-e.insert,e.ins_h=e.window[c],e.ins_h=(e.ins_h<<e.hash_shift^e.window[c+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[c+3-1])&e.hash_mask,e.prev[c&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=c,c++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<h&&0!==e.strm.avail_in)}function k(e,t){for(var r,n;;){if(e.lookahead<h){if(w(e),e.lookahead<h&&0===t)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-h&&(e.match_length=_(e,r)),e.match_length>=3)if(n=o._tr_tally(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){for(e.match_length--;e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart,0!=--e.match_length;);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(v(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,4===t?(v(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(v(e,!1),0===e.strm.avail_out)?1:2}function x(e,t){for(var r,n,i;;){if(e.lookahead<h){if(w(e),e.lookahead<h&&0===t)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-h&&(e.match_length=_(e,r),e.match_length<=5&&(1===e.strategy||3===e.match_length&&4096<e.strstart-e.match_start)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){for(i=e.strstart+e.lookahead-3,n=o._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!=--e.prev_length;);if(e.match_available=0,e.match_length=2,e.strstart++,n&&(v(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((n=o._tr_tally(e,0,e.window[e.strstart-1]))&&v(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=o._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,4===t?(v(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(v(e,!1),0===e.strm.avail_out)?1:2}function S(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}function E(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new i.Buf16(1146),this.dyn_dtree=new i.Buf16(122),this.bl_tree=new i.Buf16(78),m(this.dyn_ltree),m(this.dyn_dtree),m(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new i.Buf16(16),this.heap=new i.Buf16(573),m(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new i.Buf16(573),m(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function A(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=2,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?42:d,e.adler=2===t.wrap?0:1,t.last_flush=0,o._tr_init(t),0):f(e,u)}function T(e){var t,r=A(e);return 0===r&&((t=e.state).window_size=2*t.w_size,m(t.head),t.max_lazy_match=n[t.level].max_lazy,t.good_match=n[t.level].good_length,t.nice_match=n[t.level].nice_length,t.max_chain_length=n[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=2,t.match_available=0,t.ins_h=0),r}function R(e,t,r,n,o,a){if(!e)return u;var s=1;if(-1===t&&(t=6),n<0?(s=0,n=-n):15<n&&(s=2,n-=16),o<1||9<o||8!==r||n<8||15<n||t<0||9<t||a<0||4<a)return f(e,u);8===n&&(n=9);var c=new E;return(e.state=c).strm=e,c.wrap=s,c.gzhead=null,c.w_bits=n,c.w_size=1<<c.w_bits,c.w_mask=c.w_size-1,c.hash_bits=o+7,c.hash_size=1<<c.hash_bits,c.hash_mask=c.hash_size-1,c.hash_shift=~~((c.hash_bits+3-1)/3),c.window=new i.Buf8(2*c.w_size),c.head=new i.Buf16(c.hash_size),c.prev=new i.Buf16(c.w_size),c.lit_bufsize=1<<o+6,c.pending_buf_size=4*c.lit_bufsize,c.pending_buf=new i.Buf8(c.pending_buf_size),c.d_buf=1*c.lit_bufsize,c.l_buf=3*c.lit_bufsize,c.level=t,c.strategy=a,c.method=r,T(e)}n=[new S(0,0,0,0,(function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(w(e),0===e.lookahead&&0===t)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,v(e,!1),0===e.strm.avail_out)||e.strstart-e.block_start>=e.w_size-h&&(v(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(v(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(v(e,!1),e.strm.avail_out),1)})),new S(4,4,8,4,k),new S(4,5,16,8,k),new S(4,6,32,32,k),new S(4,4,16,16,x),new S(8,16,32,32,x),new S(8,16,128,128,x),new S(8,32,128,256,x),new S(32,128,258,1024,x),new S(32,258,258,4096,x)],r.deflateInit=function(e,t){return R(e,t,8,15,8,0)},r.deflateInit2=R,r.deflateReset=T,r.deflateResetKeep=A,r.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?u:(e.state.gzhead=t,0):u},r.deflate=function(e,t){var r,i,a,c;if(!e||!e.state||5<t||t<0)return e?f(e,u):u;if(i=e.state,!e.output||!e.input&&0!==e.avail_in||666===i.status&&4!==t)return f(e,0===e.avail_out?-5:u);if(i.strm=e,r=i.last_flush,i.last_flush=t,42===i.status)if(2===i.wrap)e.adler=0,y(i,31),y(i,139),y(i,8),i.gzhead?(y(i,(i.gzhead.text?1:0)+(i.gzhead.hcrc?2:0)+(i.gzhead.extra?4:0)+(i.gzhead.name?8:0)+(i.gzhead.comment?16:0)),y(i,255&i.gzhead.time),y(i,i.gzhead.time>>8&255),y(i,i.gzhead.time>>16&255),y(i,i.gzhead.time>>24&255),y(i,9===i.level?2:2<=i.strategy||i.level<2?4:0),y(i,255&i.gzhead.os),i.gzhead.extra&&i.gzhead.extra.length&&(y(i,255&i.gzhead.extra.length),y(i,i.gzhead.extra.length>>8&255)),i.gzhead.hcrc&&(e.adler=s(e.adler,i.pending_buf,i.pending,0)),i.gzindex=0,i.status=69):(y(i,0),y(i,0),y(i,0),y(i,0),y(i,0),y(i,9===i.level?2:2<=i.strategy||i.level<2?4:0),y(i,3),i.status=d);else{var h=8+(i.w_bits-8<<4)<<8;h|=(2<=i.strategy||i.level<2?0:i.level<6?1:6===i.level?2:3)<<6,0!==i.strstart&&(h|=32),h+=31-h%31,i.status=d,b(i,h),0!==i.strstart&&(b(i,e.adler>>>16),b(i,65535&e.adler)),e.adler=1}if(69===i.status)if(i.gzhead.extra){for(a=i.pending;i.gzindex<(65535&i.gzhead.extra.length)&&(i.pending!==i.pending_buf_size||(i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),g(e),a=i.pending,i.pending!==i.pending_buf_size));)y(i,255&i.gzhead.extra[i.gzindex]),i.gzindex++;i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),i.gzindex===i.gzhead.extra.length&&(i.gzindex=0,i.status=73)}else i.status=73;if(73===i.status)if(i.gzhead.name){a=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),g(e),a=i.pending,i.pending===i.pending_buf_size)){c=1;break}c=i.gzindex<i.gzhead.name.length?255&i.gzhead.name.charCodeAt(i.gzindex++):0,y(i,c)}while(0!==c);i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),0===c&&(i.gzindex=0,i.status=91)}else i.status=91;if(91===i.status)if(i.gzhead.comment){a=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),g(e),a=i.pending,i.pending===i.pending_buf_size)){c=1;break}c=i.gzindex<i.gzhead.comment.length?255&i.gzhead.comment.charCodeAt(i.gzindex++):0,y(i,c)}while(0!==c);i.gzhead.hcrc&&i.pending>a&&(e.adler=s(e.adler,i.pending_buf,i.pending-a,a)),0===c&&(i.status=103)}else i.status=103;if(103===i.status&&(i.gzhead.hcrc?(i.pending+2>i.pending_buf_size&&g(e),i.pending+2<=i.pending_buf_size&&(y(i,255&e.adler),y(i,e.adler>>8&255),e.adler=0,i.status=d)):i.status=d),0!==i.pending){if(g(e),0===e.avail_out)return i.last_flush=-1,0}else if(0===e.avail_in&&p(t)<=p(r)&&4!==t)return f(e,-5);if(666===i.status&&0!==e.avail_in)return f(e,-5);if(0!==e.avail_in||0!==i.lookahead||0!==t&&666!==i.status){var _=2===i.strategy?function(e,t){for(var r;;){if(0===e.lookahead&&(w(e),0===e.lookahead)){if(0===t)return 1;break}if(e.match_length=0,r=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(v(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(v(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(v(e,!1),0===e.strm.avail_out)?1:2}(i,t):3===i.strategy?function(e,t){for(var r,n,i,a,s=e.window;;){if(e.lookahead<=l){if(w(e),e.lookahead<=l&&0===t)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&0<e.strstart&&(n=s[i=e.strstart-1])===s[++i]&&n===s[++i]&&n===s[++i]){a=e.strstart+l;do{}while(n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&i<a);e.match_length=l-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(r=o._tr_tally(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(v(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(v(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(v(e,!1),0===e.strm.avail_out)?1:2}(i,t):n[i.level].func(i,t);if(3!==_&&4!==_||(i.status=666),1===_||3===_)return 0===e.avail_out&&(i.last_flush=-1),0;if(2===_&&(1===t?o._tr_align(i):5!==t&&(o._tr_stored_block(i,0,0,!1),3===t&&(m(i.head),0===i.lookahead&&(i.strstart=0,i.block_start=0,i.insert=0))),g(e),0===e.avail_out))return i.last_flush=-1,0}return 4!==t?0:i.wrap<=0?1:(2===i.wrap?(y(i,255&e.adler),y(i,e.adler>>8&255),y(i,e.adler>>16&255),y(i,e.adler>>24&255),y(i,255&e.total_in),y(i,e.total_in>>8&255),y(i,e.total_in>>16&255),y(i,e.total_in>>24&255)):(b(i,e.adler>>>16),b(i,65535&e.adler)),g(e),0<i.wrap&&(i.wrap=-i.wrap),0!==i.pending?0:1)},r.deflateEnd=function(e){var t;return e&&e.state?42!==(t=e.state.status)&&69!==t&&73!==t&&91!==t&&103!==t&&t!==d&&666!==t?f(e,u):(e.state=null,t===d?f(e,-3):0):u},r.deflateSetDictionary=function(e,t){var r,n,o,s,c,l,h,d,f=t.length;if(!e||!e.state||2===(s=(r=e.state).wrap)||1===s&&42!==r.status||r.lookahead)return u;for(1===s&&(e.adler=a(e.adler,t,f,0)),r.wrap=0,f>=r.w_size&&(0===s&&(m(r.head),r.strstart=0,r.block_start=0,r.insert=0),d=new i.Buf8(r.w_size),i.arraySet(d,t,f-r.w_size,r.w_size,0),t=d,f=r.w_size),c=e.avail_in,l=e.next_in,h=e.input,e.avail_in=f,e.next_in=0,e.input=t,w(r);r.lookahead>=3;){for(n=r.strstart,o=r.lookahead-2;r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+3-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++,--o;);r.strstart=n,r.lookahead=2,w(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,e.next_in=l,e.input=h,e.avail_in=c,r.wrap=s,0},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(e,t,r){t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(e,t,r){t.exports=function(e,t){var r,n,i,o,a,s,c,u,l,h,d,f,p,m,g,v,y,b,_,w,k,x,S,E,A;r=e.state,n=e.next_in,E=e.input,i=n+(e.avail_in-5),o=e.next_out,A=e.output,a=o-(t-e.avail_out),s=o+(e.avail_out-257),c=r.dmax,u=r.wsize,l=r.whave,h=r.wnext,d=r.window,f=r.hold,p=r.bits,m=r.lencode,g=r.distcode,v=(1<<r.lenbits)-1,y=(1<<r.distbits)-1;e:do{p<15&&(f+=E[n++]<<p,p+=8,f+=E[n++]<<p,p+=8),b=m[f&v];t:for(;;){if(f>>>=_=b>>>24,p-=_,0==(_=b>>>16&255))A[o++]=65535&b;else{if(!(16&_)){if(!(64&_)){b=m[(65535&b)+(f&(1<<_)-1)];continue t}if(32&_){r.mode=12;break e}e.msg="invalid literal/length code",r.mode=30;break e}w=65535&b,(_&=15)&&(p<_&&(f+=E[n++]<<p,p+=8),w+=f&(1<<_)-1,f>>>=_,p-=_),p<15&&(f+=E[n++]<<p,p+=8,f+=E[n++]<<p,p+=8),b=g[f&y];r:for(;;){if(f>>>=_=b>>>24,p-=_,!(16&(_=b>>>16&255))){if(!(64&_)){b=g[(65535&b)+(f&(1<<_)-1)];continue r}e.msg="invalid distance code",r.mode=30;break e}if(k=65535&b,p<(_&=15)&&(f+=E[n++]<<p,(p+=8)<_&&(f+=E[n++]<<p,p+=8)),c<(k+=f&(1<<_)-1)){e.msg="invalid distance too far back",r.mode=30;break e}if(f>>>=_,p-=_,(_=o-a)<k){if(l<(_=k-_)&&r.sane){e.msg="invalid distance too far back",r.mode=30;break e}if(S=d,(x=0)===h){if(x+=u-_,_<w){for(w-=_;A[o++]=d[x++],--_;);x=o-k,S=A}}else if(h<_){if(x+=u+h-_,(_-=h)<w){for(w-=_;A[o++]=d[x++],--_;);if(x=0,h<w){for(w-=_=h;A[o++]=d[x++],--_;);x=o-k,S=A}}}else if(x+=h-_,_<w){for(w-=_;A[o++]=d[x++],--_;);x=o-k,S=A}for(;2<w;)A[o++]=S[x++],A[o++]=S[x++],A[o++]=S[x++],w-=3;w&&(A[o++]=S[x++],1<w&&(A[o++]=S[x++]))}else{for(x=o-k;A[o++]=A[x++],A[o++]=A[x++],A[o++]=A[x++],2<(w-=3););w&&(A[o++]=A[x++],1<w&&(A[o++]=A[x++]))}break}}break}}while(n<i&&o<s);n-=w=p>>3,f&=(1<<(p-=w<<3))-1,e.next_in=n,e.next_out=o,e.avail_in=n<i?i-n+5:5-(n-i),e.avail_out=o<s?s-o+257:257-(o-s),r.hold=f,r.bits=p}},{}],49:[function(e,t,r){var n=e("../utils/common"),i=e("./adler32"),o=e("./crc32"),a=e("./inffast"),s=e("./inftrees"),c=-2;function u(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function l(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new n.Buf16(320),this.work=new n.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function h(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=1,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new n.Buf32(852),t.distcode=t.distdyn=new n.Buf32(592),t.sane=1,t.back=-1,0):c}function d(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,h(e)):c}function f(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||15<t)?c:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,d(e))):c}function p(e,t){var r,n;return e?(n=new l,(e.state=n).window=null,0!==(r=f(e,t))&&(e.state=null),r):c}var m,g,v=!0;function y(e){if(v){var t;for(m=new n.Buf32(512),g=new n.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(s(1,e.lens,0,288,m,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;s(2,e.lens,0,32,g,0,e.work,{bits:5}),v=!1}e.lencode=m,e.lenbits=9,e.distcode=g,e.distbits=5}function b(e,t,r,i){var o,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new n.Buf8(a.wsize)),i>=a.wsize?(n.arraySet(a.window,t,r-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):(i<(o=a.wsize-a.wnext)&&(o=i),n.arraySet(a.window,t,r-i,o,a.wnext),(i-=o)?(n.arraySet(a.window,t,r-i,i,0),a.wnext=i,a.whave=a.wsize):(a.wnext+=o,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=o))),0}r.inflateReset=d,r.inflateReset2=f,r.inflateResetKeep=h,r.inflateInit=function(e){return p(e,15)},r.inflateInit2=p,r.inflate=function(e,t){var r,l,h,d,f,p,m,g,v,_,w,k,x,S,E,A,T,R,C,I,O,D,B,L,P=0,N=new n.Buf8(4),z=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return c;12===(r=e.state).mode&&(r.mode=13),f=e.next_out,h=e.output,m=e.avail_out,d=e.next_in,l=e.input,p=e.avail_in,g=r.hold,v=r.bits,_=p,w=m,D=0;e:for(;;)switch(r.mode){case 1:if(0===r.wrap){r.mode=13;break}for(;v<16;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(2&r.wrap&&35615===g){N[r.check=0]=255&g,N[1]=g>>>8&255,r.check=o(r.check,N,2,0),v=g=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&g)<<8)+(g>>8))%31){e.msg="incorrect header check",r.mode=30;break}if(8!=(15&g)){e.msg="unknown compression method",r.mode=30;break}if(v-=4,O=8+(15&(g>>>=4)),0===r.wbits)r.wbits=O;else if(O>r.wbits){e.msg="invalid window size",r.mode=30;break}r.dmax=1<<O,e.adler=r.check=1,r.mode=512&g?10:12,v=g=0;break;case 2:for(;v<16;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(r.flags=g,8!=(255&r.flags)){e.msg="unknown compression method",r.mode=30;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=30;break}r.head&&(r.head.text=g>>8&1),512&r.flags&&(N[0]=255&g,N[1]=g>>>8&255,r.check=o(r.check,N,2,0)),v=g=0,r.mode=3;case 3:for(;v<32;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}r.head&&(r.head.time=g),512&r.flags&&(N[0]=255&g,N[1]=g>>>8&255,N[2]=g>>>16&255,N[3]=g>>>24&255,r.check=o(r.check,N,4,0)),v=g=0,r.mode=4;case 4:for(;v<16;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}r.head&&(r.head.xflags=255&g,r.head.os=g>>8),512&r.flags&&(N[0]=255&g,N[1]=g>>>8&255,r.check=o(r.check,N,2,0)),v=g=0,r.mode=5;case 5:if(1024&r.flags){for(;v<16;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}r.length=g,r.head&&(r.head.extra_len=g),512&r.flags&&(N[0]=255&g,N[1]=g>>>8&255,r.check=o(r.check,N,2,0)),v=g=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&(p<(k=r.length)&&(k=p),k&&(r.head&&(O=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),n.arraySet(r.head.extra,l,d,k,O)),512&r.flags&&(r.check=o(r.check,l,k,d)),p-=k,d+=k,r.length-=k),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===p)break e;for(k=0;O=l[d+k++],r.head&&O&&r.length<65536&&(r.head.name+=String.fromCharCode(O)),O&&k<p;);if(512&r.flags&&(r.check=o(r.check,l,k,d)),p-=k,d+=k,O)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===p)break e;for(k=0;O=l[d+k++],r.head&&O&&r.length<65536&&(r.head.comment+=String.fromCharCode(O)),O&&k<p;);if(512&r.flags&&(r.check=o(r.check,l,k,d)),p-=k,d+=k,O)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;v<16;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(g!==(65535&r.check)){e.msg="header crc mismatch",r.mode=30;break}v=g=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=12;break;case 10:for(;v<32;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}e.adler=r.check=u(g),v=g=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=f,e.avail_out=m,e.next_in=d,e.avail_in=p,r.hold=g,r.bits=v,2;e.adler=r.check=1,r.mode=12;case 12:if(5===t||6===t)break e;case 13:if(r.last){g>>>=7&v,v-=7&v,r.mode=27;break}for(;v<3;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}switch(r.last=1&g,v-=1,3&(g>>>=1)){case 0:r.mode=14;break;case 1:if(y(r),r.mode=20,6!==t)break;g>>>=2,v-=2;break e;case 2:r.mode=17;break;case 3:e.msg="invalid block type",r.mode=30}g>>>=2,v-=2;break;case 14:for(g>>>=7&v,v-=7&v;v<32;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if((65535&g)!=(g>>>16^65535)){e.msg="invalid stored block lengths",r.mode=30;break}if(r.length=65535&g,v=g=0,r.mode=15,6===t)break e;case 15:r.mode=16;case 16:if(k=r.length){if(p<k&&(k=p),m<k&&(k=m),0===k)break e;n.arraySet(h,l,d,k,f),p-=k,d+=k,m-=k,f+=k,r.length-=k;break}r.mode=12;break;case 17:for(;v<14;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(r.nlen=257+(31&g),g>>>=5,v-=5,r.ndist=1+(31&g),g>>>=5,v-=5,r.ncode=4+(15&g),g>>>=4,v-=4,286<r.nlen||30<r.ndist){e.msg="too many length or distance symbols",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;v<3;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}r.lens[z[r.have++]]=7&g,g>>>=3,v-=3}for(;r.have<19;)r.lens[z[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,B={bits:r.lenbits},D=s(0,r.lens,0,19,r.lencode,0,r.work,B),r.lenbits=B.bits,D){e.msg="invalid code lengths set",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;A=(P=r.lencode[g&(1<<r.lenbits)-1])>>>16&255,T=65535&P,!((E=P>>>24)<=v);){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(T<16)g>>>=E,v-=E,r.lens[r.have++]=T;else{if(16===T){for(L=E+2;v<L;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(g>>>=E,v-=E,0===r.have){e.msg="invalid bit length repeat",r.mode=30;break}O=r.lens[r.have-1],k=3+(3&g),g>>>=2,v-=2}else if(17===T){for(L=E+3;v<L;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}v-=E,O=0,k=3+(7&(g>>>=E)),g>>>=3,v-=3}else{for(L=E+7;v<L;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}v-=E,O=0,k=11+(127&(g>>>=E)),g>>>=7,v-=7}if(r.have+k>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=30;break}for(;k--;)r.lens[r.have++]=O}}if(30===r.mode)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=30;break}if(r.lenbits=9,B={bits:r.lenbits},D=s(1,r.lens,0,r.nlen,r.lencode,0,r.work,B),r.lenbits=B.bits,D){e.msg="invalid literal/lengths set",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,B={bits:r.distbits},D=s(2,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,B),r.distbits=B.bits,D){e.msg="invalid distances set",r.mode=30;break}if(r.mode=20,6===t)break e;case 20:r.mode=21;case 21:if(6<=p&&258<=m){e.next_out=f,e.avail_out=m,e.next_in=d,e.avail_in=p,r.hold=g,r.bits=v,a(e,w),f=e.next_out,h=e.output,m=e.avail_out,d=e.next_in,l=e.input,p=e.avail_in,g=r.hold,v=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;A=(P=r.lencode[g&(1<<r.lenbits)-1])>>>16&255,T=65535&P,!((E=P>>>24)<=v);){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(A&&!(240&A)){for(R=E,C=A,I=T;A=(P=r.lencode[I+((g&(1<<R+C)-1)>>R)])>>>16&255,T=65535&P,!(R+(E=P>>>24)<=v);){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}g>>>=R,v-=R,r.back+=R}if(g>>>=E,v-=E,r.back+=E,r.length=T,0===A){r.mode=26;break}if(32&A){r.back=-1,r.mode=12;break}if(64&A){e.msg="invalid literal/length code",r.mode=30;break}r.extra=15&A,r.mode=22;case 22:if(r.extra){for(L=r.extra;v<L;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}r.length+=g&(1<<r.extra)-1,g>>>=r.extra,v-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;A=(P=r.distcode[g&(1<<r.distbits)-1])>>>16&255,T=65535&P,!((E=P>>>24)<=v);){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(!(240&A)){for(R=E,C=A,I=T;A=(P=r.distcode[I+((g&(1<<R+C)-1)>>R)])>>>16&255,T=65535&P,!(R+(E=P>>>24)<=v);){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}g>>>=R,v-=R,r.back+=R}if(g>>>=E,v-=E,r.back+=E,64&A){e.msg="invalid distance code",r.mode=30;break}r.offset=T,r.extra=15&A,r.mode=24;case 24:if(r.extra){for(L=r.extra;v<L;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}r.offset+=g&(1<<r.extra)-1,g>>>=r.extra,v-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=30;break}r.mode=25;case 25:if(0===m)break e;if(k=w-m,r.offset>k){if((k=r.offset-k)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=30;break}x=k>r.wnext?(k-=r.wnext,r.wsize-k):r.wnext-k,k>r.length&&(k=r.length),S=r.window}else S=h,x=f-r.offset,k=r.length;for(m<k&&(k=m),m-=k,r.length-=k;h[f++]=S[x++],--k;);0===r.length&&(r.mode=21);break;case 26:if(0===m)break e;h[f++]=r.length,m--,r.mode=21;break;case 27:if(r.wrap){for(;v<32;){if(0===p)break e;p--,g|=l[d++]<<v,v+=8}if(w-=m,e.total_out+=w,r.total+=w,w&&(e.adler=r.check=r.flags?o(r.check,h,w,f-w):i(r.check,h,w,f-w)),w=m,(r.flags?g:u(g))!==r.check){e.msg="incorrect data check",r.mode=30;break}v=g=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;v<32;){if(0===p)break e;p--,g+=l[d++]<<v,v+=8}if(g!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=30;break}v=g=0}r.mode=29;case 29:D=1;break e;case 30:D=-3;break e;case 31:return-4;default:return c}return e.next_out=f,e.avail_out=m,e.next_in=d,e.avail_in=p,r.hold=g,r.bits=v,(r.wsize||w!==e.avail_out&&r.mode<30&&(r.mode<27||4!==t))&&b(e,e.output,e.next_out,w-e.avail_out)?(r.mode=31,-4):(_-=e.avail_in,w-=e.avail_out,e.total_in+=_,e.total_out+=w,r.total+=w,r.wrap&&w&&(e.adler=r.check=r.flags?o(r.check,h,w,e.next_out-w):i(r.check,h,w,e.next_out-w)),e.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),(0==_&&0===w||4===t)&&0===D&&(D=-5),D)},r.inflateEnd=function(e){if(!e||!e.state)return c;var t=e.state;return t.window&&(t.window=null),e.state=null,0},r.inflateGetHeader=function(e,t){var r;return e&&e.state&&2&(r=e.state).wrap?((r.head=t).done=!1,0):c},r.inflateSetDictionary=function(e,t){var r,n=t.length;return e&&e.state?0!==(r=e.state).wrap&&11!==r.mode?c:11===r.mode&&i(1,t,n,0)!==r.check?-3:b(e,t,n,n)?(r.mode=31,-4):(r.havedict=1,0):c},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(e,t,r){var n=e("../utils/common"),i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],o=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],a=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],s=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,c,u,l,h,d){var f,p,m,g,v,y,b,_,w,k=d.bits,x=0,S=0,E=0,A=0,T=0,R=0,C=0,I=0,O=0,D=0,B=null,L=0,P=new n.Buf16(16),N=new n.Buf16(16),z=null,U=0;for(x=0;x<=15;x++)P[x]=0;for(S=0;S<c;S++)P[t[r+S]]++;for(T=k,A=15;1<=A&&0===P[A];A--);if(A<T&&(T=A),0===A)return u[l++]=20971520,u[l++]=20971520,d.bits=1,0;for(E=1;E<A&&0===P[E];E++);for(T<E&&(T=E),x=I=1;x<=15;x++)if(I<<=1,(I-=P[x])<0)return-1;if(0<I&&(0===e||1!==A))return-1;for(N[1]=0,x=1;x<15;x++)N[x+1]=N[x]+P[x];for(S=0;S<c;S++)0!==t[r+S]&&(h[N[t[r+S]]++]=S);if(y=0===e?(B=z=h,19):1===e?(B=i,L-=257,z=o,U-=257,256):(B=a,z=s,-1),x=E,v=l,C=S=D=0,m=-1,g=(O=1<<(R=T))-1,1===e&&852<O||2===e&&592<O)return 1;for(;;){for(b=x-C,w=h[S]<y?(_=0,h[S]):h[S]>y?(_=z[U+h[S]],B[L+h[S]]):(_=96,0),f=1<<x-C,E=p=1<<R;u[v+(D>>C)+(p-=f)]=b<<24|_<<16|w,0!==p;);for(f=1<<x-1;D&f;)f>>=1;if(0!==f?(D&=f-1,D+=f):D=0,S++,0==--P[x]){if(x===A)break;x=t[r+h[S]]}if(T<x&&(D&g)!==m){for(0===C&&(C=T),v+=E,I=1<<(R=x-C);R+C<A&&!((I-=P[R+C])<=0);)R++,I<<=1;if(O+=1<<R,1===e&&852<O||2===e&&592<O)return 1;u[m=D&g]=T<<24|R<<16|v-l}}return 0!==D&&(u[v+D]=x-C<<24|64<<16),d.bits=T,0}},{"../utils/common":41}],51:[function(e,t,r){t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(e,t,r){var n=e("../utils/common");function i(e){for(var t=e.length;0<=--t;)e[t]=0}var o=256,a=286,s=30,c=15,u=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],l=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],d=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],f=new Array(576);i(f);var p=new Array(60);i(p);var m=new Array(512);i(m);var g=new Array(256);i(g);var v=new Array(29);i(v);var y,b,_,w=new Array(s);function k(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}function x(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function S(e){return e<256?m[e]:m[256+(e>>>7)]}function E(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function A(e,t,r){e.bi_valid>16-r?(e.bi_buf|=t<<e.bi_valid&65535,E(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=r-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function T(e,t,r){A(e,r[2*t],r[2*t+1])}function R(e,t){for(var r=0;r|=1&e,e>>>=1,r<<=1,0<--t;);return r>>>1}function C(e,t,r){var n,i,o=new Array(16),a=0;for(n=1;n<=c;n++)o[n]=a=a+r[n-1]<<1;for(i=0;i<=t;i++){var s=e[2*i+1];0!==s&&(e[2*i]=R(o[s]++,s))}}function I(e){var t;for(t=0;t<a;t++)e.dyn_ltree[2*t]=0;for(t=0;t<s;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function O(e){8<e.bi_valid?E(e,e.bi_buf):0<e.bi_valid&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function D(e,t,r,n){var i=2*t,o=2*r;return e[i]<e[o]||e[i]===e[o]&&n[t]<=n[r]}function B(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&D(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!D(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function L(e,t,r){var n,i,a,s,c=0;if(0!==e.last_lit)for(;n=e.pending_buf[e.d_buf+2*c]<<8|e.pending_buf[e.d_buf+2*c+1],i=e.pending_buf[e.l_buf+c],c++,0===n?T(e,i,t):(T(e,(a=g[i])+o+1,t),0!==(s=u[a])&&A(e,i-=v[a],s),T(e,a=S(--n),r),0!==(s=l[a])&&A(e,n-=w[a],s)),c<e.last_lit;);T(e,256,t)}function P(e,t){var r,n,i,o=t.dyn_tree,a=t.stat_desc.static_tree,s=t.stat_desc.has_stree,u=t.stat_desc.elems,l=-1;for(e.heap_len=0,e.heap_max=573,r=0;r<u;r++)0!==o[2*r]?(e.heap[++e.heap_len]=l=r,e.depth[r]=0):o[2*r+1]=0;for(;e.heap_len<2;)o[2*(i=e.heap[++e.heap_len]=l<2?++l:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=a[2*i+1]);for(t.max_code=l,r=e.heap_len>>1;1<=r;r--)B(e,o,r);for(i=u;r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],B(e,o,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,o[2*i]=o[2*r]+o[2*n],e.depth[i]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,o[2*r+1]=o[2*n+1]=i,e.heap[1]=i++,B(e,o,1),2<=e.heap_len;);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,i,o,a,s,u=t.dyn_tree,l=t.max_code,h=t.stat_desc.static_tree,d=t.stat_desc.has_stree,f=t.stat_desc.extra_bits,p=t.stat_desc.extra_base,m=t.stat_desc.max_length,g=0;for(o=0;o<=c;o++)e.bl_count[o]=0;for(u[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<573;r++)m<(o=u[2*u[2*(n=e.heap[r])+1]+1]+1)&&(o=m,g++),u[2*n+1]=o,l<n||(e.bl_count[o]++,a=0,p<=n&&(a=f[n-p]),s=u[2*n],e.opt_len+=s*(o+a),d&&(e.static_len+=s*(h[2*n+1]+a)));if(0!==g){do{for(o=m-1;0===e.bl_count[o];)o--;e.bl_count[o]--,e.bl_count[o+1]+=2,e.bl_count[m]--,g-=2}while(0<g);for(o=m;0!==o;o--)for(n=e.bl_count[o];0!==n;)l<(i=e.heap[--r])||(u[2*i+1]!==o&&(e.opt_len+=(o-u[2*i+1])*u[2*i],u[2*i+1]=o),n--)}}(e,t),C(o,l,e.bl_count)}function N(e,t,r){var n,i,o=-1,a=t[1],s=0,c=7,u=4;for(0===a&&(c=138,u=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)i=a,a=t[2*(n+1)+1],++s<c&&i===a||(s<u?e.bl_tree[2*i]+=s:0!==i?(i!==o&&e.bl_tree[2*i]++,e.bl_tree[32]++):s<=10?e.bl_tree[34]++:e.bl_tree[36]++,o=i,u=(s=0)===a?(c=138,3):i===a?(c=6,3):(c=7,4))}function z(e,t,r){var n,i,o=-1,a=t[1],s=0,c=7,u=4;for(0===a&&(c=138,u=3),n=0;n<=r;n++)if(i=a,a=t[2*(n+1)+1],!(++s<c&&i===a)){if(s<u)for(;T(e,i,e.bl_tree),0!=--s;);else 0!==i?(i!==o&&(T(e,i,e.bl_tree),s--),T(e,16,e.bl_tree),A(e,s-3,2)):s<=10?(T(e,17,e.bl_tree),A(e,s-3,3)):(T(e,18,e.bl_tree),A(e,s-11,7));o=i,u=(s=0)===a?(c=138,3):i===a?(c=6,3):(c=7,4)}}i(w);var U=!1;function j(e,t,r,i){var o,a,s;A(e,0+(i?1:0),3),a=t,s=r,O(o=e),E(o,s),E(o,~s),n.arraySet(o.pending_buf,o.window,a,s,o.pending),o.pending+=s}r._tr_init=function(e){U||(function(){var e,t,r,n,i,o=new Array(16);for(n=r=0;n<28;n++)for(v[n]=r,e=0;e<1<<u[n];e++)g[r++]=n;for(g[r-1]=n,n=i=0;n<16;n++)for(w[n]=i,e=0;e<1<<l[n];e++)m[i++]=n;for(i>>=7;n<s;n++)for(w[n]=i<<7,e=0;e<1<<l[n]-7;e++)m[256+i++]=n;for(t=0;t<=c;t++)o[t]=0;for(e=0;e<=143;)f[2*e+1]=8,e++,o[8]++;for(;e<=255;)f[2*e+1]=9,e++,o[9]++;for(;e<=279;)f[2*e+1]=7,e++,o[7]++;for(;e<=287;)f[2*e+1]=8,e++,o[8]++;for(C(f,287,o),e=0;e<s;e++)p[2*e+1]=5,p[2*e]=R(e,5);y=new k(f,u,257,a,c),b=new k(p,l,0,s,c),_=new k(new Array(0),h,0,19,7)}(),U=!0),e.l_desc=new x(e.dyn_ltree,y),e.d_desc=new x(e.dyn_dtree,b),e.bl_desc=new x(e.bl_tree,_),e.bi_buf=0,e.bi_valid=0,I(e)},r._tr_stored_block=j,r._tr_flush_block=function(e,t,r,n){var i,a,s=0;0<e.level?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<o;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),P(e,e.l_desc),P(e,e.d_desc),s=function(e){var t;for(N(e,e.dyn_ltree,e.l_desc.max_code),N(e,e.dyn_dtree,e.d_desc.max_code),P(e,e.bl_desc),t=18;3<=t&&0===e.bl_tree[2*d[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(a=e.static_len+3+7>>>3)<=i&&(i=a)):i=a=r+5,r+4<=i&&-1!==t?j(e,t,r,n):4===e.strategy||a===i?(A(e,2+(n?1:0),3),L(e,f,p)):(A(e,4+(n?1:0),3),function(e,t,r,n){var i;for(A(e,t-257,5),A(e,r-1,5),A(e,n-4,4),i=0;i<n;i++)A(e,e.bl_tree[2*d[i]+1],3);z(e,e.dyn_ltree,t-1),z(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,s+1),L(e,e.dyn_ltree,e.dyn_dtree)),I(e),n&&O(e)},r._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(g[r]+o+1)]++,e.dyn_dtree[2*S(t)]++),e.last_lit===e.lit_bufsize-1},r._tr_align=function(e){var t;A(e,2,3),T(e,256,f),16===(t=e).bi_valid?(E(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):8<=t.bi_valid&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}},{"../utils/common":41}],53:[function(e,t,r){t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(e,t,r){(function(e){!function(e){if(!e.setImmediate){var t,r,n,i,o=1,a={},s=!1,c=e.document,u=Object.getPrototypeOf&&Object.getPrototypeOf(e);u=u&&u.setTimeout?u:e,t="[object process]"==={}.toString.call(e.process)?function(e){process.nextTick((function(){h(e)}))}:function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=r,t}}()?(i="setImmediate$"+Math.random()+"$",e.addEventListener?e.addEventListener("message",d,!1):e.attachEvent("onmessage",d),function(t){e.postMessage(i+t,"*")}):e.MessageChannel?((n=new MessageChannel).port1.onmessage=function(e){h(e.data)},function(e){n.port2.postMessage(e)}):c&&"onreadystatechange"in c.createElement("script")?(r=c.documentElement,function(e){var t=c.createElement("script");t.onreadystatechange=function(){h(e),t.onreadystatechange=null,r.removeChild(t),t=null},r.appendChild(t)}):function(e){setTimeout(h,0,e)},u.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var r=new Array(arguments.length-1),n=0;n<r.length;n++)r[n]=arguments[n+1];var i={callback:e,args:r};return a[o]=i,t(o),o++},u.clearImmediate=l}function l(e){delete a[e]}function h(e){if(s)setTimeout(h,0,e);else{var t=a[e];if(t){s=!0;try{!function(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(undefined,r)}}(t)}finally{l(e),s=!1}}}}function d(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(i)&&h(+t.data.slice(i.length))}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[10])(10)}))}));var jS=new WeakMap;function FS(e,t){return function(e){return function(t,r,n){return n.value=e(n.value,r),n}}(((r,n)=>function(){for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];let a=jS.get(this);a||(a=new Map,jS.set(this,a));let s=t(...i),c=a.get(s);if(!c||c<=0){r.apply(this,i);let t=setTimeout((()=>{var e;null==(e=jS.get(this))||e.delete(s)}),e);a.set(s,t)}else{clearTimeout(c);let t=window.setTimeout((()=>{var e;r.apply(this,i),null==(e=jS.get(this))||e.delete(s)}),e);a.set(s,t)}}))}var MS,qS="audio-frame",WS=((MS=WS||{})[MS.TRACE=0]="TRACE",MS[MS.DEBUG=1]="DEBUG",MS[MS.INFO=2]="INFO",MS[MS.WARN=3]="WARN",MS[MS.ERROR=4]="ERROR",MS[MS.NONE=5]="NONE",MS),VS="Log",HS="Network",GS="Dump",ZS="Dumping",YS="End",KS="Upload Log",XS="Export Log",$S="Hide",JS="goBottom",QS="backTop",eE="Clear Log",tE="Uplink",rE="Downlink",nE="No Data",iE="Basic",oE="Duration",aE="Video",sE="Audio",cE="Screen",uE={"play() error: NotAllowedError:":{tips:e=>e.includes("main <video>")||e.includes("main <audio>")?"Remote stream auto play is restricted and playback fails":"Local stream playback failure, generally only occurs in Android WeChat devices, no need to handle",color:"red",class:"red"},"updateStream() try to recover local stream":{tips:"Device acquisition exception, automatic recovery is in progress"},"updateStream() recover local stream successfully":{tips:"Device acquisition exception, automatic recovery is successful"},"updateStream() failed to recover local stream":{tips:"Device acquisition exception, automatic recovery failed"},"main stream - video track is muted":{tips:"Indicates that the received video data is not enough for playback. This is usually caused by network reasons. When enough data is received for playback, the state will change to unmuted."},"main stream - audio track is muted":{tips:"Indicates that the received audio data is not enough for playback. This is usually caused by network reasons. When enough data is received for playback, the state will change to unmuted."},"auxiliary stream - video track is muted":{tips:"Indicates that the received screen sharing data is not enough for playback. This is usually caused by network reasons. When enough data is received for playback, the state will change to unmuted."},"main stream - video track is unable to provide media output":{tips:"Indicates that the received video data is not enough for playback. This is usually caused by network reasons. When enough data is received for playback, the state will change to unmuted."},"main stream - audio track is unable to provide media output":{tips:"Indicates that the received audio data is not enough for playback. This is usually caused by network reasons. When enough data is received for playback, the state will change to unmuted."},"auxiliary stream - video track is unable to provide media output":{tips:"Indicates that the received screen sharing data is not enough for playback. This is usually caused by network reasons. When enough data is received for playback, the state will change to unmuted."},"main stream - video track is unmuted":{tips:"Received enough playback video data"},"main stream - audio track is unmuted":{tips:"Received enough playback audio data"},"auxiliary stream - video track is unmuted":{tips:"Received enough playback screen sharing data"},"main stream - audio player track is ended":{tips:"Remote audio track stopped"},"main stream - video player track is ended":{tips:"Remote video track stopped"},"auxiliary stream - video player track is ended":{tips:"Received enough playback screen sharing data"},"stream - video track is muted":{tips:"Camera capture is paused, usually due to the device being occupied by other applications or the browser media permission being revoked. The client needs to re-capture.",color:"orange"},"stream - video track is unable to provide media output":{tips:"The camera acquisition is paused, usually because the device is occupied by other applications or the browser media permissions are revoked, and the customer needs to re-acquire. SDK v4.11.4+ will automatically resume acquisition without customer intervention",color:"orange"},"video track is unable to provide media output":{tips:e=>e.includes("↓")?"The remote track is temporarily unable to decode data, which may be caused by network fluctuations":"The camera acquisition is paused, usually because the device is occupied by other applications or the browser media permissions are revoked. The SDK will automatically resume the acquisition.",color:"orange"},"stream - video player track is ended":{tips:"The camera acquisition stops, usually because the device is unplugged. In this case, the SDK will automatically resume acquisition, and the access side does not need to handle it. It may also be caused by the device being occupied by other applications or the browser media permissions being reclaimed. It is recommended to remind the user to re-acquire on the page."},"video player track is ended":{tips:"The camera acquisition stops, usually because the device is unplugged. In this case, the SDK will automatically resume acquisition, and the access side does not need to handle it. It may also be caused by the device being occupied by other applications or the browser media permissions being reclaimed. It is recommended to remind the user to re-acquire on the page."},"stream - audio track is muted":{tips:"Microphone capture is paused, usually due to the device being occupied by other applications or the browser media permission being revoked. The client needs to re-capture.",color:"orange"},"audio track is unable to provide media output":{tips:e=>e.includes("↓")?"The remote track is temporarily unable to decode data, which may be caused by network fluctuations":"Microphone acquisition is paused, usually because the device is occupied by other applications or the browser media permissions are revoked. The SDK will automatically resume the acquisition.",color:"orange"},"stream - audio track is unable to provide media output":{tips:"The microphone acquisition is paused, usually because the device is occupied by other applications or the browser media permissions are revoked. The SDK will automatically resume the acquisition.",color:"orange"},"stream - audio player track is ended":{tips:"The microphone acquisition stops, usually because the device is unplugged. In this case, the SDK will automatically resume acquisition, and the access side does not need to handle it. It may also be caused by the device being occupied by other applications or the browser media permissions being reclaimed. It is recommended to remind the user to re-acquire on the page."},"audio player track is ended":{tips:"The microphone acquisition stops, usually because the device is unplugged. In this case, the SDK will automatically resume acquisition, and the access side does not need to handle it. It may also be caused by the device being occupied by other applications or the browser media permissions being reclaimed. It is recommended to remind the user to re-acquire on the page."},"is adding audio track to current published local stream":{tips:"add audio track",color:"orange"},"is removing audio track from current published local stream":{tips:"remove audio track",color:"orange"},"is removing video track from current published local stream":{tips:"remove video track",color:"orange"},"is adding video track to current published local stream":{tips:"add video track",color:"orange"},"is replacing audio track to current published local main stream":{tips:"replace audio track",color:"orange"},"is replacing video track to current published local main stream":{tips:"replace video track",color:"orange"},"downlink network quality change":{tips:"Downstream network quality change: 1: excellent, 2: good, 3: average, 4: poor, 5: extremely poor"},"uplink network quality change":{tips:"Upstream network quality change: 1: excellent, 2: good, 3: average, 4: poor, 5: extremely poor"},"localStream mute video":{tips:"mute upstream video stream",color:"orange"},"localStream unmute video":{tips:"unmute upstream video stream",color:"orange"},"localStream mute audio":{tips:"mute upstream audio stream",color:"orange"},"localStream unmute audio":{tips:"unmute upstream audio stream",color:"orange"},"black detected":{tips:"Detect black screen, which means fps = 0. It is usually caused by network, and the network will be recovered after a while. If the network is normal but has not been restored, it is an abnormal situation."},'main stream start to play with options: {"muted":true}':{tips:"Play the remote stream in silent mode. Generally, the remote stream does not need to be played in silent mode. You need to confirm whether the parameters passed in the client stream.play API call are correct.",color:"orange"},"main stream - audio player is starting playing":{tips:"Remote stream audio playback success",color:"#0dd90d",class:"success"},"main stream - video player is starting playing":{tips:"Remote stream video playback success",color:"#0dd90d",class:"success"},"video player is playing":{tips:"Local video playback success",color:"#0dd90d",class:"success"},"audio player is playing":{tips:"Local audio playback success",color:"#0dd90d",class:"success"},"stream - video player is starting playing":{tips:"Local video playback success",color:"#0dd90d",class:"success"},"stream - audio player is starting playing":{tips:"Local audio playback success",color:"#0dd90d",class:"success"},"switch camera success":{tips:"Switch camera success",color:"#0dd90d",class:"success"},"switch microphone success":{tips:"Switch microphone success",color:"#0dd90d",class:"success"},gotStream:{tips:"Local stream capture success"},"local stream is published successfully":{tips:"Publish success",color:"#0dd90d",class:"success"},"encoderImplementation change to OpenH264":{tips:"Use software encoding"},"encoderImplementation change to ExternalEncoder":{tips:"Use hardware encoding"},"getUserMedia with constraints":{tips:"Start media (camera/microphone) capture"},"getDisplayMedia with constraints":{tips:"Start screen sharing capture"},"client-banned":{tips:"Kicked out of the room",color:"red",class:"red",textColor:"#fff"},user_timeout:{tips:"The backend is too long without receiving SDK heartbeat, which is usually caused by the JS thread being blocked for a long time. If the reproduction probability is high, the application browser Performance tool analyzes where the JS thread is blocked.",color:"red",class:"red",textColor:"#fff"},"schedule failed":{tips:"Signaling request failed, does not affect the normal room entry process, SDK will connect to the default signaling domain."},"updateStream() video flag is true, but no camera detected, set video to false":{tips:"When trying to resume camera acquisition, the camera acquisition is not resumed because it is detected that there is no camera.",color:"orange",class:"orange",textColor:"#fff"},"qualityLimitationReason change to bandwidth":{tips:"bandwidth estimation is not enough to limit the quality of the encoding, which may cause the code rate, frame rate, and resolution to be lowered.",color:"orange",class:"orange",textColor:"#fff"},"qualityLimitationReason change to cpu":{tips:"cpu load is too high to limit the quality of the encoding, which may cause the code rate, frame rate, and resolution to be lowered.",color:"orange",class:"orange",textColor:"#fff"},"visibility change: hidden":{tips:"User page switched to backend, in mobile devices, this will cause the device to pause the capture, and it will be restored when the user switches backend to the frontend.",color:"orange",class:"orange",textColor:"#fff"},"visibility change: visible":{tips:"User page switched to frontend",color:"orange",class:"orange",textColor:"#fff"},"main stream - audio player is paused":{tips:"The remote stream audio playback is paused, which usually has two possibilities:\n        1. The business side passes in the div tag container for playing audio and video to be removed by the business side from the DOM.\n        2. If the user is in Chrome 70 and below, when the div container is moved, the playback will be paused.",color:"orange",class:"orange",textColor:"#fff"},"main stream - video player is paused":{tips:"The remote stream video playback is paused, which usually has two possibilities:\n 1. The business side passes in the div tag container for playing audio and video to be removed by the business side from the DOM.\n 2. If the user is in Chrome 70 and below, when the div container is moved, the playback will be paused.",color:"orange",class:"orange",textColor:"#fff"},'"displaySurface":"window"':{tips:"Screen sharing captures the application window",color:"blue",class:"blue",textColor:"#fff"},'"displaySurface":"monitor"':{tips:"Screen sharing captures the entire screen",color:"blue",class:"blue",textColor:"#fff"},'"displaySurface":"browser"':{tips:"Screen sharing captures a certain tab page",color:"blue",class:"blue",textColor:"#fff"},updateMediaSettings:{tips:"The resolution and frame rate below are the actual resolution and frame rate captured",color:"blue",class:"blue",textColor:"#fff"},"main stream start to play with":{tips:"Call remoteStream.play interface",color:"blue",class:"blue",textColor:"#fff"},"stream start to play with":{tips:"Call localStream.play interface",color:"blue",class:"blue",textColor:"#fff"},"main setAudioVolume to 0":{tips:"Business side calls remoteStream.setAudioVolume(0) interface to set the playback volume to 0, which will cause the playback to be silent",color:"orange",class:"orange",textColor:"#fff"},"screen sharing was stopped because the video track is ended":{tips:"Screen sharing capture stopped, possible reasons: user clicks the stop button, the window/page shared by the user is closed, etc.",color:"orange",class:"orange",textColor:"#fff"},"Join() => joining room":{tips:"Call client.join interface",color:"blue",class:"blue",textColor:"#fff"},"publish() => publishing local stream":{tips:"Call client.publish interface",color:"blue",class:"blue",textColor:"#fff"},"subscribe() => subscribe to":{tips:"Call client.subscribe interface",color:"blue",class:"blue",textColor:"#fff"},"switchRole() => switch role":{tips:"Call client.switchRole interface",color:"blue",class:"blue",textColor:"#fff"}};["enterRoom","exitRoom","switchRole","destroy","startLocalAudio","updateLocalAudio","stopLocalAudio","startLocalVideo","updateLocalVideo","stopLocalVideo","startScreenShare","updateScreenShare","stopScreenShare","startRemoteVideo","updateRemoteVideo","stopRemoteVideo","muteRemoteAudio","setRemoteAudioVolume"].forEach((e=>{uE["".concat(e,"()")]={tips:"Call trtc.".concat(e),color:"blue",class:"blue",textColor:"#fff"}}));var lE=Object.keys(uE),hE=((e,t,r)=>(r=null!=e?CS(BS(e)):{},((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of DS(t))!LS.call(e,i)&&i!==r&&IS(e,i,{get:()=>t[i],enumerable:!(n=OS(t,i))||n.enumerable});return e})(!t&&e&&e.__esModule?r:IS(r,"default",{value:e,enumerable:!0}),e)))(US()),dE="trtc_debug_dialog",fE="".concat(dE,"_activated"),pE="".concat(dE,"_switch"),mE="".concat(dE,"_mask"),gE="".concat(dE,"_panel"),vE="".concat(dE,"_tabber"),yE="".concat(dE,"_tab"),bE="".concat(dE,"_topbar"),_E="".concat(dE,"_toptab"),wE="".concat(dE,"_content"),kE="".concat(dE,"_has_topbar"),xE="".concat(wE,"_log"),SE="".concat(wE,"_network"),EE="".concat(dE,"_dump"),AE="".concat(SE,"_title"),TE="".concat(SE,"_basic"),RE="".concat(SE,"_local"),CE="".concat(SE,"_remote"),IE="".concat(EE,"_title"),OE="".concat(EE,"_local"),DE="".concat(EE,"_remote"),BE="".concat(DE,"_hook"),LE="".concat(dE,"_banner"),PE="".concat(LE,"_item"),NE="".concat(dE,"_toolbar"),zE="".concat(dE,"_tool"),UE="".concat(dE,"_item"),jE="".concat(UE,"_debug"),FE="".concat(UE,"_info"),ME="".concat(UE,"_warn"),qE="".concat(UE,"_error"),WE="".concat(dE,"_back_top"),VE="".concat(dE,"_go_bottom"),HE="".concat(EE,"_local_audio"),GE="".concat(EE,"_local_video_main"),ZE="".concat(EE,"_local_video_aux"),YE="".concat(dE,"_tooltip"),KE="".concat(TE,"_network_info"),XE="".concat(TE,"_room_info"),$E="".concat(xE,"_controller"),JE="success",QE="warning",eA="failed",tA=0,rA=class extends HTMLElement{constructor(){super(),this.attachShadow({mode:"open"}),this.addStyle(),this.addDialog()}addDialog(){var e;let t=document.createElement("template");t.innerHTML='<div id="'.concat(dE,'" class="').concat(dE,'"><div class="').concat(pE,'">Debug#').concat(++tA,'</div><div class="').concat(mE,'"></div><div class="').concat(gE,'"><div class="').concat(vE,'"><a class="').concat(yE," ").concat(fE,'" data-tab="log">').concat(VS,'</a><a class="').concat(yE,'" data-tab="network">').concat(HS,'</a><a class="').concat(yE,'" data-tab="dump">').concat(GS,'</a></div><div class="').concat($E,'"><div data-type="back_top">').concat(QS,'</div><div data-type="go_bottom">').concat(JS,'</div><div data-type="clear_log">').concat(eE,'</div></div><div class="').concat(bE,'"><a class="').concat(_E," ").concat(fE,'" data-type="all">All</a><a class="').concat(_E,'" data-type="debug">Debug</a><a class="').concat(_E,'" data-type="info">Info</a><a class="').concat(_E,'" data-type="warn">Warn</a><a class="').concat(_E,'" data-type="error">Error</a></div><div class="').concat(wE," ").concat(kE,'"><div class="').concat(xE,'"></div><div class="').concat(SE,'"><div class="').concat(AE,'">').concat(iE,'</div><div class="').concat(TE,'"><div class="').concat(XE,'"></div><div class="').concat(KE,'">').concat(nE,'</div></div><div class="').concat(AE,'">').concat(tE,'</div><div class="').concat(RE,'">').concat(nE,'</div><div class="').concat(AE,'">').concat(rE,'</div><div class="').concat(CE,'">').concat(nE,'</div></div><div class="').concat(EE,'"><div class="').concat(IE,'">').concat(tE,'</div><div class="').concat(OE,'"><div id="').concat(HE,'"><span>').concat(sE," - ").concat(oE,'(s):</span><input type="number" min=0 value=10></input><button class="dump">').concat(GS,'</button><button class="end-dump" disabled>').concat(YS,'</button></div><div id="').concat(GE,'"><span>').concat(aE," - ").concat(oE,'(s):</span><input type="number" min=0 value=10></input><button class="dump">').concat(GS,'</button><button class="end-dump" disabled>').concat(YS,'</button></div><div id="').concat(ZE,'"><span>').concat(cE," - ").concat(oE,'(s):</span><input type="number" min=0 value=10></input><button class="dump">').concat(GS,'</button><button class="end-dump" disabled>').concat(YS,'</button></div></div><div class="').concat(IE,'">').concat(rE,'</div><div class="').concat(DE,'"><div class="').concat(BE,"\">userId:<input id=\"dump-remote-video-hook-userid\" value='*' />duration:<input id=\"dump-remote-video-hook-duration\" min='1' value='10' type='number' />mediaType:<select id=\"dump-remote-video-hook-mediaType\" value='main' ><option value='main' checked>Video</option><option value='sub'>Screen</option></select><button id=\"dump-remote-video-hook-button\">dump if video is available</button></div></div></div></div><div class=\"").concat(LE,'"></div><div class="').concat(NE,'"><a id="trtc_upload" class="').concat(zE,'">').concat(KS,'</a><a id="trtc_export" class="').concat(zE,'">').concat(XS,'</a><a id="trtc_hide" class="').concat(zE,'">').concat($S,"</a></div></div></div>"),null==(e=this.shadowRoot)||e.appendChild(t.content.firstChild)}addStyle(){var e;let t=document.createElement("style");t.textContent="#".concat(dE,"{--DD-BG-0:#ededed;--DD-BG-1:#f7f7f7;--DD-BG-2:#fff;--DD-BORDER:rgba(0,0,0,0.1);--DD-ERROR:red;--DD-WARN:#fa9d3b;--DD-INFO:#000;--DD-DEBUG:#10aeff;--DD-WARN-BG:#fff3cc;--DD-ERROR-BG:#ffe6e6;}#").concat(dE," a,.").concat(pE,"{cursor:pointer;}.").concat(dE,"{color:black;}.").concat(pE,"{display:block;position:fixed;right:50px;bottom:").concat(150+50*tA,"px;border-radius:5px;background-color:#07c160;color:#fff;padding:5px 10px;z-index:10000;}.").concat(mE,"{display:none;position:fixed;left:0;top:0;right:0;bottom:0;z-index:10001;background-color:rgb(0,0,0,0.6);}.").concat(gE,"{background-color:#ededed;display:none;position:fixed;min-height:85%;left:60%;right:0;bottom:0;z-index:10002;border:1px solid var(--DD-BG-0);}@media (max-width:768px) {.").concat(gE,"{left:0;}}.").concat(vE,"{display:flex;height:2rem;border-bottom:1px solid var(--DD-BORDER);}.").concat(yE,"{padding:0 20px;border-right:1px solid rgba(0,0,0,0.1);line-height:2rem;color:black;}.").concat(yE,".").concat(fE,"{background-color:var(--DD-BG-2);}.").concat(yE,".disabled{cursor:not-allowed;background-color:#ededed;color:#999;}.").concat(bE,"{height:2rem;display:flex;flex-direction:row;}.").concat(_E,"{width:20%;text-align:center;line-height:2rem;border-right:1px solid var(--DD-BORDER);border-bottom:1px solid var(--DD-BORDER);color:black;}.").concat(_E,".").concat(fE,"{border-bottom:1px solid #1485ee;background-color:var(--DD-BG-2);}.").concat(wE,"{background-color:#fff;position:absolute;left:0;right:0;top:2rem;bottom:8rem;overflow-y:scroll;overflow-x:hidden;}.").concat(wE,".").concat(kE,"{top:4rem;bottom:10rem}.").concat(SE,"{display:none;}.").concat(EE,"{display:none;}.").concat(AE,",.").concat(IE,"{line-height:2rem;text-align:center;background-color:var(--DD-BG-0);border-bottom:1px solid var(--DD-BORDER);}.").concat(TE,",.").concat(RE,",.").concat(CE,"{text-align:center;border-bottom:1px solid var(--DD-BORDER);}.").concat(OE,",.").concat(DE,"{display:flex;flex-direction:column;padding:0.5rem 0;justify-content:center;align-items:center;}.").concat(OE," div input{margin:0 10px;height:1.5rem;width:4rem;}.").concat(OE," div {padding:10px 0;border-radius:5px;}.").concat(OE," div button,.").concat(DE," div button {cursor:pointer;height:1.8rem;border-radius:5px;margin-right:5px;}.").concat(OE," div button[disabled],.").concat(DE," div button[disabled]{cursor:not-allowed;}.").concat(DE," select {height:1.8rem;margin:0 10px;}.").concat(DE," div input{margin:0 10px;height:1.5rem;width:4rem;}.").concat(BE," {line-height:3rem;padding:0.5rem;}.").concat(AE,"{text-align:center;background-color:var(--DD-BG-0);border-bottom:1px solid var(--DD-BORDER);}.").concat(TE,",.").concat(RE,",.").concat(CE,"{text-align:center;border-bottom:1px solid var(--DD-BORDER);}.").concat(UE,"{border-bottom:1px solid var(--DD-BG-0);padding:5px 10px;overflow-wrap: break-word;}.").concat(qE,"{color:var(--DD-ERROR);background-color:var(--DD-ERROR-BG);}.").concat(ME,"{color:var(--DD-WARN);background-color:var(--DD-WARN-BG);}div .").concat(FE,"{color:var(--DD-INFO);}.").concat(jE,"{color:var(--DD-DEBUG);}.").concat(LE,"{position:absolute;left:0;right:0;bottom:2rem;height:6rem;border-bottom:1px solid var(--DD-BORDER);display:flex;flex-direction:column;overflow-y:scroll;background-color:var(--DD-BG-1);}.").concat(PE,"{border-bottom:1px solid var(--DD-BORDER);padding:5px 10px;overflow-wrap: break-word;}.").concat(NE,"{position:absolute;line-height:2rem;display:flex;flex-direction:row;left:0;right:0;bottom:0;}.").concat(zE,"{flex:1;color:black;text-align:center;background-color:#ededed;border-right:1px solid var(--DD-BORDER);}#").concat(dE," .").concat(WE,",#").concat(dE," .").concat(VE,"{position:absolute;height:2rem;width:6rem;right:2rem;border-radius:5px;background-color:rgba(30,136,229,0.8);z-index:10003;cursor:pointer;padding:0 5px;line-height:2rem;color:white;text-align:center;}.").concat(VE,"{bottom:9rem;}.").concat(WE,"{bottom:11.5rem;}#").concat(dE," .").concat(zE,".disable{cursor:not-allowed;background-color:#ededed;color:#999;}.").concat(YE," {position:relative;display:inline-block;}.").concat(YE," .tooltiptext{visibility:hidden;width:180px;background-color:black;color:#fff;text-align:center;padding:5px 0;border-radius:6px;position:absolute;}.").concat(YE," .tooltip-top {bottom:125%;left:50%;margin-left:-90px;}.").concat(YE,":hover .tooltiptext{visibility:visible;}.").concat($E,"{position:absolute;left:0;right:0;bottom:8rem;height:2rem;display:flex;align-items:center;}.").concat($E," div{flex:1;cursor:pointer;border-right:1px solid var(--DD-BORDER);text-align: center;}"),null==(e=this.shadowRoot)||e.appendChild(t)}};customElements.define("trtc-basic-debug-dialog",rA);var nA=class{constructor(e,t,r,n){zS(this,"_dialogNode",null),zS(this,"_switchNode",null),zS(this,"_maskNode",null),zS(this,"_panelNode",null),zS(this,"_logContentNode",null),zS(this,"_networkContentNode",null),zS(this,"_dumpContentNode",null),zS(this,"_tabNodes",[]),zS(this,"_toptabNodes",[]),zS(this,"_hasMove",!1),zS(this,"_showDialog",!1),zS(this,"_isMobile",!1),zS(this,"_showLogType","all"),zS(this,"prevBytesSent",0),zS(this,"prevBytesReceived",0),zS(this,"_core"),zS(this,"_log"),zS(this,"_logList",[]),zS(this,"isUploading",!1),zS(this,"publishMap",new Map),zS(this,"dumpLocalVideoMap"),zS(this,"dumpRemoteVideoMap"),zS(this,"eventMap",new Map),zS(this,"switchPos",{x:0,y:0,startX:0,startY:0,endX:0,endY:0,isTouchEnd:!1}),zS(this,"handleLeaveSuccess",(e=>{let{room:t}=e;var r;if(!this.hitTest(t))return;let n=null==(r=this._dialogNode)?void 0:r.querySelector(".".concat(DE));for(let i of this.publishMap.values())null==n||n.removeChild(i);this.publishMap.clear()})),zS(this,"handleJoinSuccess",(e=>{let{room:t}=e;var r;if(!this.hitTest(t))return;(null==(r=this._dialogNode)?void 0:r.querySelector(".".concat(XE))).innerHTML="sdkAppId: ".concat(t.sdkAppId,"<br/>roomId: ").concat(t.roomId,"<br/>userId: ").concat(t.userId,"<br/>")})),zS(this,"handleRemoteVideoAvailable",(e=>{let t="sub"===e.streamType?"auxiliary":"main";this.renderDumpTab(e.userId,"remote",t,"publish")})),zS(this,"handleRemoteVideoUnavailable",(e=>{let t="sub"===e.streamType?"auxiliary":"main";this.renderDumpTab(e.userId,"remote",t,"unpublish")})),zS(this,"handleRemoteAudioAvailable",(e=>{this.renderDumpTab(e.userId,"remote","audio","publish")})),zS(this,"handleRemoteAudioUnavailable",(e=>{this.renderDumpTab(e.userId,"remote","audio","unpublish")})),zS(this,"renderDumpTab",((e,t,r,n)=>{var i,o;let a={main:"video",auxiliary:"screen",audio:"audio"},s=(e,t)=>{var r,n;let i=document.createElement("div");i.innerText="".concat(e," - ").concat(a[t]," - ").concat(oE,"(s):");let o=document.createElement("input");o.type="number",o.min="0",o.value="10",o.id="dump_remote_".concat(t,"_").concat(e);let s=document.createElement("button");s.innerText=YS,s.disabled=!0;let c=document.createElement("button");c.innerText=GS,c.onclick=()=>{let r,n,i=Number(o.value);if("audio"===t)({download:r,onDumpEnd:n}=this.dumpAudio("remote",i,e));else{if(!this.isSupportDumpVideo())return;({download:r,onDumpEnd:n}=this.dumpVideo(t,"remote",i,e))}r&&(this.inactiveButton(c,ZS),i>0?setTimeout((()=>{null==r||r(),this.activeButton(c,GS)}),1e3*i):(this.activeButton(s),s.onclick=()=>{s.onclick=null,null==r||r(),null==n||n(),this.activeButton(c,GS),this.inactiveButton(s)}))},i.appendChild(o),i.appendChild(c),i.appendChild(s),i.style.cssText="padding:10px;";let u=document.createElement("div");return u.style.cssText="width:100%;display:flex;justify-content:center;border-top: 1px solid var(--DD-BORDER);",u.appendChild(i),null==(n=null==(r=this._dialogNode)?void 0:r.querySelector(".".concat(DE)))||n.appendChild(u),u};if("remote"===t)if("publish"===n){let n=s(e,r);this.publishMap.set("".concat(e,"-").concat(t,"-").concat(r),n)}else{let n=this.publishMap.get("".concat(e,"-").concat(t,"-").concat(r));if(!n)return;null==(o=null==(i=this._dialogNode)?void 0:i.querySelector(".".concat(DE)))||o.removeChild(n),this.publishMap.delete("".concat(e,"-").concat(t,"-").concat(r))}})),zS(this,"handleStatistics",(e=>{var t,r,n;let i=(e.bytesSent-this.prevBytesSent)/2,o=(e.bytesReceived-this.prevBytesReceived)/2;this.prevBytesSent=e.bytesSent,this.prevBytesReceived=e.bytesReceived,(null==(t=this._dialogNode)?void 0:t.querySelector(".".concat(KE))).innerHTML="rtt: ".concat(e.rtt," ms upLoss: ").concat(e.upLoss,"% downLoss: ").concat(e.downLoss,"% <br>\n        ↑ ").concat(i/1e3," KB/s (").concat(8*i/1e3," Kb/s) <br>\n        ↓ ").concat(o/1e3," KB/s (").concat(8*o/1e3," Kb/s) <br>\n      ");let a=null==(r=this._dialogNode)?void 0:r.querySelector(".".concat(RE)),s="";e.localStatistics.audio.bitrate&&(s="<li>local audio: ".concat(e.localStatistics.audio.bitrate," Kb/s audioLevel: ").concat(e.localStatistics.audio.audioLevel.toFixed(5),"</li>")),e.localStatistics.video.length>0&&(s+="".concat(e.localStatistics.video.map((e=>"<li>local ".concat(e.videoType," video: ").concat(e.bitrate," Kb/s ").concat(e.width,"*").concat(e.height," ").concat(e.frameRate,"fps</li>"))).join(""))),a.innerHTML=""===s?"".concat(nE):s;let c=null==(n=this._dialogNode)?void 0:n.querySelector(".".concat(CE)),u="";e.remoteStatistics.length>0&&e.remoteStatistics.forEach((e=>{let t="remote ".concat(e.userId,":");e.audio.bitrate>0&&(t+="<li>audio: ".concat(e.audio.bitrate," Kb/s audioLevel: ").concat(e.audio.audioLevel.toFixed(5),"</li>")),e.video.length>0&&e.video.forEach((e=>{t+="<li>".concat(e.videoType," video: ").concat(e.bitrate," Kb/s ").concat(e.width,"*").concat(e.height," ").concat(e.frameRate,"fps</li>")})),u+=t})),c.innerHTML=""===u?"".concat(nE):u})),zS(this,"pad",(e=>e<10?"0".concat(e):"".concat(e))),this._core=e,this._log=t,this.dumpLocalVideoMap=r,this.dumpRemoteVideoMap=n,this.initCustomElements(),this.synchronizeLog(),this.installEvents()}initCustomElements(){this._isMobile=(()=>{let e=window.navigator&&window.navigator.userAgent||"",t=/iPad/i.test(e)||/iPhone/i.test(e),r=/Android/i.test(e);return t||r})();let e=document.createElement("trtc-basic-debug-dialog");document.body.appendChild(e);let t=e.shadowRoot;if(this._dialogNode=t,!t)return;this._switchNode=t.querySelector(".".concat(pE)),this._addEventListener(this._switchNode,"click",this.onSwitchClick.bind(this));let r=getComputedStyle(this._switchNode);this.switchPos.x=parseInt(r.right,10)||50,this.switchPos.y=parseInt(r.bottom,10)||100+50*tA,this._isMobile?(this._switchNode.ontouchstart=this.onTouchStart.bind(this),this._switchNode.ontouchend=this.onTouchEnd.bind(this),this._switchNode.ontouchmove=this.onTouchMove.bind(this)):this._switchNode.onmousedown=this.onMouseDown.bind(this),this._maskNode=t.querySelector(".".concat(mE)),this._addEventListener(this._maskNode,"click",this.onMaskClick.bind(this)),this._panelNode=t.querySelector(".".concat(gE)),t.querySelector(".".concat($E)).childNodes.forEach((e=>{switch(e.getAttribute("data-type")){case"go_bottom":this._addEventListener(e,"click",this.onGoBottomClick.bind(this));break;case"back_top":this._addEventListener(e,"click",this.onBackTopClick.bind(this));break;case"clear_log":this._addEventListener(e,"click",this.onClearLogClick.bind(this))}})),this._logContentNode=t.querySelector(".".concat(xE)),this._networkContentNode=t.querySelector(".".concat(SE)),this._dumpContentNode=t.querySelector(".".concat(EE)),this._tabNodes=Array.from(t.querySelector(".".concat(vE)).children),this._tabNodes.forEach((e=>{this._addEventListener(e,"click",this.onTabClick.bind(this))})),this._toptabNodes=Array.from(t.querySelector(".".concat(bE)).children),this._toptabNodes.forEach((e=>{this._addEventListener(e,"click",this.onToptabClick.bind(this))}));let n=t.querySelector("#trtc_upload");this._addEventListener(n,"click",this.onUploadClick.bind(this));let i=t.querySelector("#trtc_export");this._addEventListener(i,"click",this.onExportClick.bind(this));let o=t.querySelector("#trtc_hide");this._addEventListener(o,"click",this.onHideClick.bind(this));let a=t.querySelector("#".concat(GE," .dump")),s=t.querySelector("#".concat(GE," .end-dump")),c=Number(t.querySelector("#".concat(GE," input")).value);this._addEventListener(a,"click",this.getDumpButtonClickHandler.bind(this,a,s,c,"main","local"));let u=t.querySelector("#".concat(ZE," .dump")),l=t.querySelector("#".concat(ZE," .end-dump")),h=Number(t.querySelector("#".concat(ZE," input")).value);this._addEventListener(u,"click",this.getDumpButtonClickHandler.bind(this,u,l,h,"auxiliary","local"));let d=t.querySelector("#".concat(HE," button"));this._addEventListener(d,"click",(()=>{let e=Number(t.querySelector("#".concat(HE," input")).value),{download:r,onDumpEnd:n}=this.dumpAudio("local",e);if(r)if(this.inactiveButton(d,ZS),e>0)setTimeout((()=>{r(),this.activeButton(d,GS)}),1e3*e);else{let e=t.querySelector("#".concat(HE," .end-dump"));this.activeButton(e),e.onclick=()=>{e.onclick=null,r(),null==n||n(),this.activeButton(d,GS),this.inactiveButton(e)}}else this.addInfoToBanner(eA,"Failed to dump, please check if the current user has started the audio")}));let f=t.querySelector("#dump-remote-video-hook-userid"),p=t.querySelector("#dump-remote-video-hook-duration"),m=t.querySelector("#dump-remote-video-hook-mediaType"),g=t.querySelector("#dump-remote-video-hook-button");g.onclick=()=>{let e=Number(p.value);if(e<1)return void this.addInfoToBanner(eA,"duration at least 1s");let t=f.value;if(!t)return void this.addInfoToBanner(eA,"please input user id");"*"===t&&this.inactiveButton(g);let r,n,i=m.value,o="remote_".concat(t,"_").concat("sub"===i?"auxiliary":i),a=o=>{if((o.userId===t||"*"===t)&&o.streamType===i){if(this.addInfoToBanner(JE,"[".concat(o.userId,"_").concat(i,"] starting to dump video, duration: ").concat(e,"s")),({download:r,onDumpEnd:n}=this.dumpVideo("sub"===o.streamType?"auxiliary":o.streamType,"remote",e,o.userId)),!r)return;setTimeout((()=>{null==r||r(),null==n||n(),"*"!==t&&this._core.trtc.off(this._core.TRTC.EVENT.REMOTE_VIDEO_AVAILABLE,a)}),1e3*e)}};this._core.trtc.on(this._core.TRTC.EVENT.REMOTE_VIDEO_AVAILABLE,a),this.addInfoToBanner(JE,"[".concat(o,"] waiting to dump video from ").concat("*"===t?"all remote users":"user ".concat(t),", duration: ").concat(e,"s"))}}synchronizeLog(){this._core.loggerManager.getQueue().forEach((e=>{this.updateLog(e)}))}isSupportDumpVideo(){return!(!this._core.rtcDectection.IS_INSERTABLE_STREAM_SUPPORTED&&!this._core.rtcDectection.IS_SCRIPT_TRANSFORM_SUPPORTED)||(this.addInfoToBanner(eA,"Current browser does not support dumping video"),!1)}_addEventListener(e,t,r,n){e&&(e.addEventListener(t,r,n),this.eventMap.set(e,{type:t,listener:r}))}_removeEventListener(){this.eventMap.forEach(((e,t)=>{let{type:r,listener:n}=e;t&&t.removeEventListener(r,n)})),this.eventMap.clear(),this._switchNode&&(this._switchNode.onmousedown=null,this._switchNode.ontouchmove=null,this._switchNode.ontouchstart=null,this._switchNode.ontouchend=null)}closeDialog(){this.uninstallEvents(),this._removeEventListener(),this._dialogNode&&(document.body.removeChild(this._dialogNode),this._dialogNode=null)}updateLog(e){var t;this._logList.push(e);let r=document.createElement("div");switch(r.classList.add(UE),e.level){case 4:r.classList.add(qE);break;case 2:r.classList.add(FE);break;case 3:r.classList.add(ME);break;case 1:r.classList.add(jE)}r.textContent=e.log,"all"!==this._showLogType&&!r.classList.contains("".concat(UE,"_").concat(this._showLogType))&&(r.style.display="none"),null==(t=this._logContentNode)||t.appendChild(r),this.addHelper(r)}getDumpButtonClickHandler(e,t,r,n,i){if(!this.isSupportDumpVideo())return;let{download:o,onDumpEnd:a}=this.dumpVideo(n,i,r);if(o)return this.addInfoToBanner(JE,"[".concat(i,"_").concat(n,"] starting to dump...")),this.inactiveButton(e,ZS),void(r>0?setTimeout((()=>{o(),this.activeButton(e,GS)}),1e3*r):(this.activeButton(t),t.onclick=()=>{t.onclick=null,null==a||a(),o(),this.activeButton(e,GS),this.inactiveButton(t)}));this.addInfoToBanner(JE,"[".concat(i,"_").concat(n,"] waiting to dump..."));let s=o=>{let{track:a}=o;if(a.room===this._core.room&&a.streamType===n){this.addInfoToBanner(JE,"[".concat(i,"_").concat(n,"] starting to dump")),"local"===i&&this._core.innerEmitter.off("104",s);let{download:o,onDumpEnd:a}=this.dumpVideo(n,i,r);if(!o)return;r>0?setTimeout((()=>{o(),this.activeButton(e,GS)}),1e3*r):(this.activeButton(t),t.onclick=()=>{t.onclick=null,null==a||a(),o(),this.activeButton(e,GS),this.inactiveButton(t)})}};s.bind(this),"local"===i&&this._core.innerEmitter.on("104",s),this.inactiveButton(e,ZS)}onSwitchClick(){this._hasMove?this._hasMove=!1:this._showDialog?this.hideDialog():(this._isMobile&&(this._maskNode.style.display="block"),this._panelNode.style.display="block",this._switchNode.style.display="none",this._showDialog=!0,this.logContentGoBottom())}onTabClick(e){var t,r,n,i,o;let a=e.target;null==(t=this._tabNodes)||t.forEach((e=>{e.classList.remove(fE)})),a.classList.add(fE);let s=a.getAttribute("data-tab");"log"===s?(null==(n=null==(r=this._dialogNode)?void 0:r.querySelector(".".concat(wE)))||n.classList.add(kE),this._logContentNode.style.display="block",this._networkContentNode.style.display="none",this._dumpContentNode.style.display="none",this.logContentGoBottom()):(null==(o=null==(i=this._dialogNode)?void 0:i.querySelector(".".concat(wE)))||o.classList.remove(kE),"network"===s?(this._networkContentNode.style.display="block",this._logContentNode.style.display="none",this._dumpContentNode.style.display="none"):"dump"===s&&(this._dumpContentNode.style.display="block",this._networkContentNode.style.display="none",this._logContentNode.style.display="none"))}onToptabClick(e){var t,r;let n=e.target;null==(t=this._toptabNodes)||t.forEach((e=>{e.classList.remove(fE)})),n.classList.add(fE),this._showLogType=n.getAttribute("data-type");let i=null==(r=this._logContentNode)?void 0:r.querySelectorAll(".".concat(UE)),o="".concat(UE,"_").concat(this._showLogType);i.forEach((e=>{e.classList.contains(o)||"all"===this._showLogType?e.style.display="block":e.style.display="none"})),this.logContentGoBottom()}onExportClick(){let e=this._logList.reduce(((e,t)=>"".concat(e+t.log,"\n")),""),t=new Blob([e],{type:"text/plain"}),r=document.createElement("a"),n=URL.createObjectURL(t);r.href=n;let i="trtc_".concat(void 0===this._core.room.sdkAppId?"EmptySdkAppId":this._core.room.sdkAppId,"_").concat(void 0===this._core.room.userId?"EmptyUserId":this._core.room.userId,"_").concat(this.formatStartDumpTime(+new Date),".log");r.download=i,r.style.display="none",document.body.appendChild(r),r.click(),URL.revokeObjectURL(n),document.body.removeChild(r),this.addInfoToBanner(JE,"Export success: The log file name is ".concat(i)),this._core.kvStatManager.addSuccessEvent({key:592703})}addInfoToBanner(e,t){var r;let n=null==(r=this._dialogNode)?void 0:r.querySelector(".".concat(LE)),i=document.createElement("div");i.classList.add(PE);let o=e===JE?"🟩":e===QE?"🟧":"🟥";i.innerText="".concat(o," [").concat(this.getCurrentTime(),"] ").concat(t),n.appendChild(i),n.scrollTop=n.scrollHeight}async onUploadClick(){var e;if(this.isUploading)return;this.isUploading=!0;let t=null==(e=this._dialogNode)?void 0:e.querySelector("#trtc_upload");t.innerText="Uploading...",null==t||t.classList.add(".disable");let r=this._logList.reduce(((e,t)=>"".concat(e+t.log,"\n")),""),n=new Blob([r],{type:"text/plain"}),i=new FormData;i.append("file",n,"trtc.log"),i.append("sdkAppId","".concat(this._core.room.sdkAppId||"EmptySdkAppId")),i.append("userId","".concat(this._core.room.userId||"EmptyUserId")),fetch("https://service.trtc.qcloud.com/api/uploadLogFile",{method:"POST",body:i}).then((e=>e.json())).then((e=>{this.addInfoToBanner(e.type,e.message_en||"Upload Failed: Please use local export"),this.isUploading=!1,this._log.info(e.message_en),this._core.kvStatManager.addSuccessEvent({key:592704})})).catch((()=>{this.addInfoToBanner(eA,"Upload Failed: Please use local export"),this._log.info("Upload Failed: Please use local export"),this._core.kvStatManager.addFailedEvent({key:592704})})).finally((()=>{t.innerText=KS,null==t||t.classList.remove(".disable"),this.isUploading=!1}))}onHideClick(){this.hideDialog()}onMaskClick(){this.hideDialog()}hideDialog(){this._isMobile&&(this._maskNode.style.display="none"),this._panelNode.style.display="none",this._switchNode.style.display="block",this._showDialog=!1}onBackTopClick(){var e,t;null==(t=null==(e=this._dialogNode)?void 0:e.querySelector(".".concat(wE)))||t.scrollTo(0,0)}onGoBottomClick(){this.logContentGoBottom()}onClearLogClick(){this._logList=[],this._logContentNode&&(this._logContentNode.innerHTML="")}logContentGoBottom(){var e;let t=null==(e=this._dialogNode)?void 0:e.querySelector(".".concat(wE));null==t||t.scrollTo(0,null==t?void 0:t.scrollHeight)}onMouseDown(e){e.preventDefault(),this.switchPos.startX=e.pageX,this.switchPos.startY=e.pageY,document.onmousemove=this.onMouseMove.bind(this),document.onmouseup=this.onMouseUp.bind(this)}onMouseMove(e){e.cancelable&&e.preventDefault(),this._hasMove=!0;let t=this.switchPos.x-(e.pageX-this.switchPos.startX),r=this.switchPos.y-(e.pageY-this.switchPos.startY);[t,r]=this.getSafeAreaXY(t,r),this._switchNode.style.right="".concat(t,"px"),this._switchNode.style.bottom="".concat(r,"px"),this.switchPos.endX=t,this.switchPos.endY=r}onMouseUp(){this.switchPos.startX=0,this.switchPos.startY=0,this.switchPos.x=this.switchPos.endX,this.switchPos.y=this.switchPos.endY,document.onmousemove=null,document.onmouseup=null}onTouchStart(e){this.switchPos.startX=e.touches[0].pageX,this.switchPos.startY=e.touches[0].pageY,this.switchPos.isTouchEnd=!1}onTouchEnd(){this.switchPos.isTouchEnd&&(this.switchPos.startX=0,this.switchPos.startY=0,this.switchPos.isTouchEnd=!1,this.switchPos.x=this.switchPos.endX,this.switchPos.y=this.switchPos.endY)}onTouchMove(e){if(e.touches.length<=0)return;let t=e.touches[0].pageX-this.switchPos.startX,r=e.touches[0].pageY-this.switchPos.startY,n=Math.floor(this.switchPos.x-t),i=Math.floor(this.switchPos.y-r);[n,i]=this.getSafeAreaXY(n,i),this._switchNode.style.right="".concat(n,"px"),this._switchNode.style.bottom="".concat(i,"px"),this.switchPos.endX=n,this.switchPos.endY=i,this.switchPos.isTouchEnd=!0,e.cancelable&&e.preventDefault()}getSafeAreaXY(e,t){let r=Math.max(document.documentElement.offsetWidth,window.innerWidth),n=Math.max(document.documentElement.offsetHeight,window.innerHeight);return e+this._switchNode.offsetWidth>r&&(e=r-this._switchNode.offsetWidth),t+this._switchNode.offsetHeight>n&&(t=n-this._switchNode.offsetHeight),e<0&&(e=0),t<20&&(t=20),[e,t]}hitTest(e){return e===this._core.room}installEvents(){this._core.innerEmitter.on("264",this.handleLog,this),this._core.innerEmitter.on("53",this.handleLeaveSuccess,this),this._core.innerEmitter.on("27",this.handleJoinSuccess,this),this._core.trtc.on(this._core.TRTC.EVENT.STATISTICS,this.handleStatistics,this),this._core.trtc.on(this._core.TRTC.EVENT.REMOTE_AUDIO_AVAILABLE,this.handleRemoteAudioAvailable),this._core.trtc.on(this._core.TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE,this.handleRemoteAudioUnavailable),this._core.trtc.on(this._core.TRTC.EVENT.REMOTE_VIDEO_AVAILABLE,this.handleRemoteVideoAvailable),this._core.trtc.on(this._core.TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE,this.handleRemoteVideoUnavailable),this._core.room.on("dump",this.handleDumpFromScriptTransform,this)}handleDumpFromScriptTransform(e){if(!e.isAudio)if(e.userId){let t=this.dumpRemoteVideoMap["".concat(e.userId,"-").concat(e.streamType)];t&&(t.data.push(e.data),t.duration>0&&Date.now()-t.startTimestamp>1e3*t.duration&&t.onDumpEnd())}else{let t="main"===e.streamType?"main":"sub",r=this.dumpLocalVideoMap[t];r&&(r.data.push(e.data),r.duration>0&&Date.now()-r.startTimestamp>1e3*r.duration&&r.onDumpEnd())}}uninstallEvents(){this._core.innerEmitter.off("264",this.handleLog,this),this._core.innerEmitter.off("53",this.handleLeaveSuccess,this),this._core.innerEmitter.off("27",this.handleJoinSuccess,this),this._core.trtc.off(this._core.TRTC.EVENT.STATISTICS,this.handleStatistics,this),this._core.trtc.off(this._core.TRTC.EVENT.REMOTE_AUDIO_AVAILABLE,this.handleRemoteAudioAvailable),this._core.trtc.off(this._core.TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE,this.handleRemoteAudioUnavailable),this._core.trtc.off(this._core.TRTC.EVENT.REMOTE_VIDEO_AVAILABLE,this.handleRemoteVideoAvailable),this._core.trtc.off(this._core.TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE,this.handleRemoteVideoUnavailable),this._core.room.off("dump",this.handleDumpFromScriptTransform,this)}inactiveButton(e,t){e.disabled=!0,t&&(e.innerText=t)}activeButton(e,t){e.disabled=!1,t&&(e.innerText=t)}handleLog(e){this.updateLog(e.log)}addHelper(e){let t=/((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/g,r=lE,n=e.innerText;n=n.split("\n").map((e=>{if((e=e.replace(/</g,"&lt;").replace(/>/g,"&gt;")).includes("failed"))return e.includes("on autoplay-failed")?e:"<span class='red' style=\"background:red; color:#fff;\">".concat(e,"</span>");if(e.includes("clientIp:")){let r=e.match(t);return null==r||r.forEach((t=>{e=e.replace(t,'<a href="javascript:void(0);" target=""><span class=\'ip\' style="background:#e1dfdf">'.concat(t,"</span></a>"))})),e}return e.includes("URL of current page")&&(e=e.replace(/URL of current page: (.+)/,'URL of current page: <a href="$1">$1</a>')),e})).join("\n"),r.forEach((t=>{if(e.innerText.includes(t)){let r="";r="function"==typeof uE[t].tips?uE[t].tips(e.innerText):uE[t].tips,n=n.replace(new RegExp("".concat((e=>e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d").replace(">","&gt;").replace(">","&lt;"))(t)),"gi"),'<span class="'.concat(YE,'" style="background:').concat(uE[t].color||"#e1dfdf","; color: ").concat(uE[t].textColor||"#000",'">').concat(t,'\n            <span class="tooltiptext tooltip-top">').concat(r,"</span>\n          </span>"))}})),e.innerHTML=n}getCurrentTime(){let e=new Date,t=this.pad(e.getHours()),r=this.pad(e.getMinutes()),n=this.pad(e.getSeconds());return"".concat(t,":").concat(r,":").concat(n)}dumpVideo(e,t,r,n){let i,o=Date.now(),a=this.formatStartDumpTime(o),s=[],c="".concat(t,"_").concat(n,"_").concat(e);if("local"===t){if(!this.hasLocalTrack(e,"video"))return{};i=()=>{delete this.dumpLocalVideoMap["auxiliary"===e?"sub":"main"]},this.dumpLocalVideoMap["auxiliary"===e?"sub":"main"]={startTimestamp:o,data:s,duration:r,onDumpEnd:i}}if("remote"===t&&n){if(i=()=>{delete this.dumpRemoteVideoMap["".concat(n,"-").concat(e)]},this.dumpRemoteVideoMap["".concat(n,"-").concat(e)])return this.addInfoToBanner(QE,"[".concat(c,"] dump already exists.")),{};this.dumpRemoteVideoMap["".concat(n,"-").concat(e)]={startTimestamp:o,data:s,duration:r,onDumpEnd:i}}return{download:()=>{if(0===s.length)return void this.addInfoToBanner(eA,"[".concat(c,"] dump failed. Reason: data is empty"));let r;"main"===e?r="video":"auxiliary"===e&&(r="screen");let i=URL.createObjectURL(new Blob(s)),o=document.createElement("a"),u="local"===t?"local_".concat(r,"_").concat(a,".h264"):"remote-".concat(n,"_").concat(r,"_").concat(a,".h264");o.href=i,o.download=u,o.style.display="none",document.body.appendChild(o),o.click(),URL.revokeObjectURL(i),o.remove(),this.addInfoToBanner(JE,"[".concat(c,"] dump success. File name is: ").concat(u))},onDumpEnd:i}}hasLocalTrack(e,t){for(let r of this._core.room.localTracks)if(r.streamType===e&&r.kind===t)return!0;return!1}dumpAudio(e,t,r){var n;let i=null,o=[],a=[],s=[],c=new AbortController,u=Date.now(),l=this.formatStartDumpTime(u),h=this._core.room.audioManager._localAudioPipline,{sampleRate:d}=this._core.room.audioManager.audioContext;function f(e){let{data:t}=e;s[0]=s[0]?s[0].concat(t):[t]}if("local"===e){if(!this._core.room.audioManager.hasAudioTrack)return{};if(null!=h&&h.source.node&&(this._core.trtc.on(qS,f),a.push("local_audio_raw_capture_".concat(d,"_").concat(1))),null!=h&&h.denoiser.node&&(o.push(h.denoiser.node),a.push("local_audio_denoiser_processed_".concat(d,"_").concat(1))),this._core.room.audioManager.mixWeight>1){let e=this._core.room.audioManager.audioContext.createMediaStreamSource(h.stream);o.push(e),a.push("local_audio_destination_".concat(d,"_").concat(1))}}else if("remote"===e&&void 0!==r){let e=null==(n=this._core.room.remotePublishedUserMap.get(r))?void 0:n.remoteAudioTrack;if(null==e||!e.mediaTrack)return{};let t=this._core.createAudioNode(e.mediaTrack);o.push(t),a.push("remote_".concat(r,"_").concat(d,"_").concat(1))}let p=async()=>{let t=new hE.default;for(let e=0;e<a.length;e++)t.file("".concat(a[e],".pcm"),new Blob(s[e]));let r=await t.generateAsync({type:"blob"}),n=URL.createObjectURL(r),o=document.createElement("a");o.href=n,o.download="".concat(e,"-audio-").concat(l,".zip"),o.style.display="none",document.body.appendChild(o),o.click(),URL.revokeObjectURL(n),o.remove(),this.addInfoToBanner(JE,"Dump success, file name is: ".concat(e,"-").concat(l,".zip")),this._core.trtc.off(qS,f),i&&clearTimeout(i),c.abort("download")};return t>0&&(i=Number(setTimeout((()=>{c.abort("timeout")}),1e3*t))),o.length>0&&this._core.startGetPCM(o).then((t=>t.pipeTo(new WritableStream({write(t){t.forEach(((t,r)=>{s[r="local"===e?r+1:r]=s[r]?s[r].concat(t[0]):[t[0]]}))}}),c).catch((()=>p)))),{download:p,onDumpEnd:()=>{c.abort("timeout")}}}formatStartDumpTime(e){let t=new Date(e),r=t.getFullYear(),n=this.pad(t.getMonth()+1),i=this.pad(t.getDate()),o=this.pad(t.getHours()),a=this.pad(t.getMinutes()),s=this.pad(t.getSeconds()),c=-t.getTimezoneOffset(),u=Math.floor(Math.abs(c)/60),l=Math.abs(c)%60,h=c>=0?"+":"-";return"".concat(r,"-").concat(n,"-").concat(i,"T").concat(o,":").concat(a,":").concat(s).concat(h).concat(this.pad(u),":").concat(this.pad(l))}};
/*! Bundled license information:

	jszip/dist/jszip.min.js:
	  (*!
	  
	  JSZip v3.10.1 - A JavaScript class for generating and reading zip files
	  <http://stuartk.com/jszip>
	  
	  (c) 2009-2016 Stuart Knightley <stuart [at] stuartk.com>
	  Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/main/LICENSE.markdown.
	  
	  JSZip uses the library pako released under the MIT license :
	  https://github.com/nodeca/pako/blob/main/LICENSE
	  *)
	*/
return NS([FS(300,(()=>"export"))],nA.prototype,"onExportClick",1),NS([FS(300,(()=>"upload"))],nA.prototype,"onUploadClick",1),nA}));
