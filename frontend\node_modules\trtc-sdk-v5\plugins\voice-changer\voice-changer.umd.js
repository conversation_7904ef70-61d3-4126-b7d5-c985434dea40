!function(A,B){"object"==typeof exports&&"undefined"!=typeof module?module.exports=B():"function"==typeof define&&define.amd?define(B):(A="undefined"!=typeof globalThis?globalThis:A||self).VoiceChanger=B()}(this,(function(){"use strict";function A(A,B,f,v,g,w,P){try{var t=A[w](P),Q=t.value}catch(A){return void f(A)}t.done?B(Q):Promise.resolve(Q).then(v,g)}function B(B){return function(){var f=this,v=arguments;return new Promise((function(g,w){var P=B.apply(f,v);function t(B){A(P,g,w,t,Q,"next",B)}function Q(B){A(P,g,w,t,Q,"throw",B)}t(void 0)}))}}function f(A,B){if(!(A instanceof B))throw new TypeError("Cannot call a class as a function")}function v(A,B,f){return B&&function(A,B){for(var f=0;f<B.length;f++){var v=B[f];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(A,w(v.key),v)}}(A.prototype,B),Object.defineProperty(A,"prototype",{writable:!1}),A}function g(){g=function(){return B};var A,B={},f=Object.prototype,v=f.hasOwnProperty,w=Object.defineProperty||function(A,B,f){A[B]=f.value},P="function"==typeof Symbol?Symbol:{},t=P.iterator||"@@iterator",Q=P.asyncIterator||"@@asyncIterator",e=P.toStringTag||"@@toStringTag";function C(A,B,f){return Object.defineProperty(A,B,{value:f,enumerable:!0,configurable:!0,writable:!0}),A[B]}try{C({},"")}catch(A){C=function(A,B,f){return A[B]=f}}function n(A,B,f,v){var g=B&&B.prototype instanceof E?B:E,P=Object.create(g.prototype),t=new T(v||[]);return w(P,"_invoke",{value:z(A,f,t)}),P}function r(A,B,f){try{return{type:"normal",arg:A.call(B,f)}}catch(A){return{type:"throw",arg:A}}}B.wrap=n;var D="suspendedStart",o="suspendedYield",s="executing",c="completed",i={};function E(){}function a(){}function u(){}var H={};C(H,t,(function(){return this}));var h=Object.getPrototypeOf,I=h&&h(h(y([])));I&&I!==f&&v.call(I,t)&&(H=I);var L=u.prototype=E.prototype=Object.create(H);function F(A){["next","throw","return"].forEach((function(B){C(A,B,(function(A){return this._invoke(B,A)}))}))}function G(A,B){function f(g,w,P,t){var Q=r(A[g],A,w);if("throw"!==Q.type){var e=Q.arg,C=e.value;return C&&"object"==typeof C&&v.call(C,"__await")?B.resolve(C.__await).then((function(A){f("next",A,P,t)}),(function(A){f("throw",A,P,t)})):B.resolve(C).then((function(A){e.value=A,P(e)}),(function(A){return f("throw",A,P,t)}))}t(Q.arg)}var g;w(this,"_invoke",{value:function(A,v){function w(){return new B((function(B,g){f(A,v,B,g)}))}return g=g?g.then(w,w):w()}})}function z(B,f,v){var g=D;return function(w,P){if(g===s)throw Error("Generator is already running");if(g===c){if("throw"===w)throw P;return{value:A,done:!0}}for(v.method=w,v.arg=P;;){var t=v.delegate;if(t){var Q=M(t,v);if(Q){if(Q===i)continue;return Q}}if("next"===v.method)v.sent=v._sent=v.arg;else if("throw"===v.method){if(g===D)throw g=c,v.arg;v.dispatchException(v.arg)}else"return"===v.method&&v.abrupt("return",v.arg);g=s;var e=r(B,f,v);if("normal"===e.type){if(g=v.done?c:o,e.arg===i)continue;return{value:e.arg,done:v.done}}"throw"===e.type&&(g=c,v.method="throw",v.arg=e.arg)}}}function M(B,f){var v=f.method,g=B.iterator[v];if(g===A)return f.delegate=null,"throw"===v&&B.iterator.return&&(f.method="return",f.arg=A,M(B,f),"throw"===f.method)||"return"!==v&&(f.method="throw",f.arg=new TypeError("The iterator does not provide a '"+v+"' method")),i;var w=r(g,B.iterator,f.arg);if("throw"===w.type)return f.method="throw",f.arg=w.arg,f.delegate=null,i;var P=w.arg;return P?P.done?(f[B.resultName]=P.value,f.next=B.nextLoc,"return"!==f.method&&(f.method="next",f.arg=A),f.delegate=null,i):P:(f.method="throw",f.arg=new TypeError("iterator result is not an object"),f.delegate=null,i)}function b(A){var B={tryLoc:A[0]};1 in A&&(B.catchLoc=A[1]),2 in A&&(B.finallyLoc=A[2],B.afterLoc=A[3]),this.tryEntries.push(B)}function p(A){var B=A.completion||{};B.type="normal",delete B.arg,A.completion=B}function T(A){this.tryEntries=[{tryLoc:"root"}],A.forEach(b,this),this.reset(!0)}function y(B){if(B||""===B){var f=B[t];if(f)return f.call(B);if("function"==typeof B.next)return B;if(!isNaN(B.length)){var g=-1,w=function f(){for(;++g<B.length;)if(v.call(B,g))return f.value=B[g],f.done=!1,f;return f.value=A,f.done=!0,f};return w.next=w}}throw new TypeError(typeof B+" is not iterable")}return a.prototype=u,w(L,"constructor",{value:u,configurable:!0}),w(u,"constructor",{value:a,configurable:!0}),a.displayName=C(u,e,"GeneratorFunction"),B.isGeneratorFunction=function(A){var B="function"==typeof A&&A.constructor;return!!B&&(B===a||"GeneratorFunction"===(B.displayName||B.name))},B.mark=function(A){return Object.setPrototypeOf?Object.setPrototypeOf(A,u):(A.__proto__=u,C(A,e,"GeneratorFunction")),A.prototype=Object.create(L),A},B.awrap=function(A){return{__await:A}},F(G.prototype),C(G.prototype,Q,(function(){return this})),B.AsyncIterator=G,B.async=function(A,f,v,g,w){void 0===w&&(w=Promise);var P=new G(n(A,f,v,g),w);return B.isGeneratorFunction(f)?P:P.next().then((function(A){return A.done?A.value:P.next()}))},F(L),C(L,e,"Generator"),C(L,t,(function(){return this})),C(L,"toString",(function(){return"[object Generator]"})),B.keys=function(A){var B=Object(A),f=[];for(var v in B)f.push(v);return f.reverse(),function A(){for(;f.length;){var v=f.pop();if(v in B)return A.value=v,A.done=!1,A}return A.done=!0,A}},B.values=y,T.prototype={constructor:T,reset:function(B){if(this.prev=0,this.next=0,this.sent=this._sent=A,this.done=!1,this.delegate=null,this.method="next",this.arg=A,this.tryEntries.forEach(p),!B)for(var f in this)"t"===f.charAt(0)&&v.call(this,f)&&!isNaN(+f.slice(1))&&(this[f]=A)},stop:function(){this.done=!0;var A=this.tryEntries[0].completion;if("throw"===A.type)throw A.arg;return this.rval},dispatchException:function(B){if(this.done)throw B;var f=this;function g(v,g){return t.type="throw",t.arg=B,f.next=v,g&&(f.method="next",f.arg=A),!!g}for(var w=this.tryEntries.length-1;w>=0;--w){var P=this.tryEntries[w],t=P.completion;if("root"===P.tryLoc)return g("end");if(P.tryLoc<=this.prev){var Q=v.call(P,"catchLoc"),e=v.call(P,"finallyLoc");if(Q&&e){if(this.prev<P.catchLoc)return g(P.catchLoc,!0);if(this.prev<P.finallyLoc)return g(P.finallyLoc)}else if(Q){if(this.prev<P.catchLoc)return g(P.catchLoc,!0)}else{if(!e)throw Error("try statement without catch or finally");if(this.prev<P.finallyLoc)return g(P.finallyLoc)}}}},abrupt:function(A,B){for(var f=this.tryEntries.length-1;f>=0;--f){var g=this.tryEntries[f];if(g.tryLoc<=this.prev&&v.call(g,"finallyLoc")&&this.prev<g.finallyLoc){var w=g;break}}w&&("break"===A||"continue"===A)&&w.tryLoc<=B&&B<=w.finallyLoc&&(w=null);var P=w?w.completion:{};return P.type=A,P.arg=B,w?(this.method="next",this.next=w.finallyLoc,i):this.complete(P)},complete:function(A,B){if("throw"===A.type)throw A.arg;return"break"===A.type||"continue"===A.type?this.next=A.arg:"return"===A.type?(this.rval=this.arg=A.arg,this.method="return",this.next="end"):"normal"===A.type&&B&&(this.next=B),i},finish:function(A){for(var B=this.tryEntries.length-1;B>=0;--B){var f=this.tryEntries[B];if(f.finallyLoc===A)return this.complete(f.completion,f.afterLoc),p(f),i}},catch:function(A){for(var B=this.tryEntries.length-1;B>=0;--B){var f=this.tryEntries[B];if(f.tryLoc===A){var v=f.completion;if("throw"===v.type){var g=v.arg;p(f)}return g}}throw Error("illegal catch attempt")},delegateYield:function(B,f,v){return this.delegate={iterator:y(B),resultName:f,nextLoc:v},"next"===this.method&&(this.arg=A),i}},B}function w(A){var B=function(A,B){if("object"!=typeof A||!A)return A;var f=A[Symbol.toPrimitive];if(void 0!==f){var v=f.call(A,B);if("object"!=typeof v)return v;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(A)}(A,"string");return"symbol"==typeof B?B:B+""}function P(A){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},P(A)}var t=Object.defineProperty,Q=function(A,B,f){return function(A,B,f){return B in A?t(A,B,{enumerable:!0,configurable:!0,writable:!0,value:f}):A[B]=f}(A,"symbol"!==P(B)?B+"":B,f)};function e(A){return C.apply(this,arguments)}function C(){return(C=B(g().mark((function A(B){var f,v,w,P,t,Q,e,C;return g().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return f=B.sdkAppId,v=B.userId,w=B.userSig,P=B.core,Q=Math.round((new Date).getTime()/1e3),A.prev=2,A.next=5,P.schedule.getAbilityConfig(f,P.schedule.ScheduleRequestType.TRTC_AUTO_CONF,{sdkAppId:f,userId:v,userSig:w,timestamp:Q});case 5:if(e=A.sent,P.log.info("voiceChanger response: ".concat(JSON.stringify(e))),C=e.data,1!==(null==(t=null==C?void 0:C.trtcAutoConf)?void 0:t.vc_chg)){A.next=10;break}return A.abrupt("return",{auth:!0});case 10:return A.abrupt("return",{auth:!1});case 13:return A.prev=13,A.t0=A.catch(2),P.log.error("voiceChanger fetch error",A.t0),A.abrupt("return",{auth:!1});case 17:case"end":return A.stop()}}),A,null,[[2,13]])})))).apply(this,arguments)}function n(A){for(var B,f="number"==typeof A?A:50,v=48e3,g=new Float32Array(v),w=Math.PI/180,P=0;P<v;++P)B=2*P/v-1,g[P]=(3+f)*B*20*w/(Math.PI+f*Math.abs(B));return g}var r=.05,D=.1,o=function(){return v((function A(B){f(this,A),this.context=B,this.previousPitch=-1,this.init()}),[{key:"init",value:function(){var A=this.context,B=A.createGain(),f=A.createGain(),v=A.createBufferSource(),g=A.createBufferSource(),w=A.createBufferSource(),P=A.createBufferSource();this.shiftDownBuffer=this.createDelayTimeBuffer(A,D,r,!1),this.shiftUpBuffer=this.createDelayTimeBuffer(A,D,r,!0),v.buffer=this.shiftDownBuffer,g.buffer=this.shiftDownBuffer,w.buffer=this.shiftUpBuffer,P.buffer=this.shiftUpBuffer,v.loop=!0,g.loop=!0,w.loop=!0,P.loop=!0;var t=A.createGain(),Q=A.createGain(),e=A.createGain(),C=A.createGain();e.gain.value=0,C.gain.value=0,v.connect(t),g.connect(Q),w.connect(e),P.connect(C);var n=A.createGain(),o=A.createGain(),s=A.createDelay(),c=A.createDelay();t.connect(n),Q.connect(o),e.connect(n),C.connect(o),n.connect(s.delayTime),o.connect(c.delayTime);var i=A.createBufferSource(),E=A.createBufferSource(),a=this.createFadeBuffer(A,D,r);i.buffer=a,E.buffer=a,i.loop=!0,E.loop=!0;var u=A.createGain(),H=A.createGain();u.gain.value=0,H.gain.value=0,i.connect(u.gain),E.connect(H.gain),B.connect(s),B.connect(c),s.connect(u),c.connect(H),u.connect(f),H.connect(f);var h=A.currentTime+.05,I=h+D-r;v.start(h),g.start(I),w.start(h),P.start(I),i.start(h),E.start(I),this.A1=v,this.A2=g,this.A3=w,this.A4=P,this.B1=t,this.B2=Q,this.B3=e,this.B4=C,this.C1=n,this.C2=o,this.input=B,this.output=f,this.fade1=i,this.fade2=E,this.mix1=u,this.mix2=H,this.delay1=s,this.delay2=c,this.setDelay(.1)}},{key:"setDelay",value:function(A){this.C1.gain.setTargetAtTime(.5*A,0,.01),this.C2.gain.setTargetAtTime(.5*A,0,.01)}},{key:"setPitchOffset",value:function(A){A>0?(this.B1.gain.value=0,this.B2.gain.value=0,this.B3.gain.value=1,this.B4.gain.value=1):(this.B1.gain.value=1,this.B2.gain.value=1,this.B3.gain.value=0,this.B4.gain.value=0),this.setDelay(.1*Math.abs(A)),this.previousPitch=A}},{key:"createFadeBuffer",value:function(A,B,f){for(var v=B*A.sampleRate,g=v+(B-2*f)*A.sampleRate,w=A.createBuffer(1,g,A.sampleRate),P=w.getChannelData(0),t=f*A.sampleRate,Q=t,e=v-t,C=0;C<v;++C){var n=void 0;n=C<Q?Math.sqrt(C/t):C>=e?Math.sqrt(1-(C-e)/t):1,P[C]=n}for(var r=v;r<g;++r)P[r]=0;return w}},{key:"createDelayTimeBuffer",value:function(A,B,f,v){for(var g=B*A.sampleRate,w=g+(B-2*f)*A.sampleRate,P=A.createBuffer(1,w,A.sampleRate),t=P.getChannelData(0),Q=0;Q<g;++Q)t[Q]=v?(g-Q)/w:Q/g;for(var e=g;e<w;++e)t[e]=0;return P}},{key:"destroy",value:function(){this.A1.stop(),this.A2.stop(),this.A3.stop(),this.A4.stop(),this.fade1.stop(),this.fade2.stop(),this.A1.disconnect(),this.A2.disconnect(),this.A3.disconnect(),this.A4.disconnect(),this.B1.disconnect(),this.B2.disconnect(),this.B3.disconnect(),this.B4.disconnect(),this.C1.disconnect(),this.C2.disconnect(),this.input.disconnect(),this.output.disconnect(),this.fade1.disconnect(),this.fade2.disconnect(),this.mix1.disconnect(),this.mix2.disconnect(),this.delay1.disconnect(),this.delay2.disconnect()}}])}(),s=function(){return v((function A(B){f(this,A),this.context=B,this.init()}),[{key:"init",value:function(){var A=this.context.createWaveShaper();A.curve=n(30);var B=this.context.createBiquadFilter();B.type="lowpass",B.frequency.value=3e3;var f=this.context.createBiquadFilter();f.type="lowpass",f.frequency.value=3e3;var v=this.context.createBiquadFilter();v.type="highpass",v.frequency.value=300;var g=this.context.createBiquadFilter();g.type="highpass",g.frequency.value=300;var w=this.context.createDynamicsCompressor();B.connect(f),f.connect(v),v.connect(g),g.connect(A),A.connect(w),this.waveShaper=A,this.lpf1=B,this.lpf2=f,this.hpf1=v,this.hpf2=g,this.compressor=w,this.inputNode=this.lpf1,this.outputNode=this.compressor}},{key:"getInputNode",value:function(){return this.inputNode}},{key:"getOutputNode",value:function(){return this.outputNode}},{key:"destroy",value:function(){this.waveShaper.disconnect(),this.lpf1.disconnect(),this.lpf2.disconnect(),this.hpf1.disconnect(),this.hpf2.disconnect(),this.compressor.disconnect()}}])}(),c=function(){return v((function A(B){f(this,A),this.context=B,this.init()}),[{key:"init",value:function(){this.waveShaper=this.context.createWaveShaper(),this.waveShaper.curve=n(7),this.pitch=new o(this.context),this.pitch.setPitchOffset(-.25),this.lpf1=this.context.createBiquadFilter(),this.lpf1.type="lowpass",this.lpf1.frequency.value=5e3,this.lpf2=this.context.createBiquadFilter(),this.lpf2.type="lowpass",this.lpf2.frequency.value=5e3,this.hpf1=this.context.createBiquadFilter(),this.hpf1.type="highpass",this.hpf1.frequency.value=100,this.hpf2=this.context.createBiquadFilter(),this.hpf2.type="highpass",this.hpf2.frequency.value=100,this.compress=this.context.createDynamicsCompressor(),this.lpf1.connect(this.lpf2),this.lpf2.connect(this.hpf1),this.hpf1.connect(this.hpf2),this.hpf2.connect(this.waveShaper),this.waveShaper.connect(this.compress),this.pitch.output.connect(this.lpf1),this.inputNode=this.pitch.input,this.outputNode=this.compress}},{key:"getInputNode",value:function(){return this.inputNode}},{key:"getOutputNode",value:function(){return this.outputNode}},{key:"destroy",value:function(){this.waveShaper.disconnect(),this.lpf1.disconnect(),this.lpf2.disconnect(),this.hpf1.disconnect(),this.hpf2.disconnect(),this.compress.disconnect(),this.pitch.destroy()}}])}(),i=function(){return v((function A(B){f(this,A),this.context=B,this.init()}),[{key:"init",value:function(){var A=this.context.createGain(),B=new o(this.context);B.setPitchOffset(-.1);var f=new o(this.context);f.setPitchOffset(-.3);var v=new o(this.context);v.setPitchOffset(-.4);var g=new o(this.context);g.setPitchOffset(-.8);var w=this.context.createDynamicsCompressor();B.output.connect(w),f.output.connect(w),v.output.connect(w),g.output.connect(w),this.dee=B,this.deep=f,this.deeper=v,this.deepest=g,this.compressor=w,A.connect(B.input),A.connect(f.input),A.connect(v.input),A.connect(g.input),this.inputNode=A,this.outputNode=w}},{key:"getInputNode",value:function(){return this.inputNode}},{key:"getOutputNode",value:function(){return this.outputNode}},{key:"destroy",value:function(){this.dee.destroy(),this.deep.destroy(),this.deeper.destroy(),this.deepest.destroy(),this.inputNode.disconnect(),this.compressor.disconnect()}}])}(),E=function(){return v((function A(B){f(this,A),this.context=B}),[{key:"init",value:(A=B(g().mark((function A(){return g().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return A.t0=this.context,A.next=3,fetch("data:audio/wav;base64,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");case 3:return A.next=5,A.sent.arrayBuffer();case 5:return A.t1=A.sent,A.next=8,A.t0.decodeAudioData.call(A.t0,A.t1);case 8:this.airyBuffer=A.sent,this.convolver=this.context.createConvolver(),this.convolver.buffer=this.airyBuffer,this.convolver.normalize=!0,this.inputNode=this.convolver,this.outputNode=this.convolver;case 14:case"end":return A.stop()}}),A,this)}))),function(){return A.apply(this,arguments)})},{key:"getInputNode",value:function(){return this.inputNode}},{key:"getOutputNode",value:function(){return this.outputNode}},{key:"destroy",value:function(){this.convolver.disconnect(),this.airyBuffer=null}}]);var A}(),a=function(){return v((function A(B,v,g){f(this,A),this.context=B,this.streamSource=v,this.destination=g,this.init()}),[{key:"init",value:function(){this.delayNode=this.context.createDelay(),this.delayNode.delayTime.value=.03,this.osc=this.context.createOscillator(),this.gain=this.context.createGain(),this.wetGain=this.context.createGain(),this.gain.gain.value=.002,this.osc.type="sine",this.osc.frequency.value=4.5,this.osc.connect(this.gain),this.gain.connect(this.delayNode.delayTime),this.delayNode.connect(this.wetGain),this.osc.start(0)}},{key:"getInputNode",value:function(){return this.delayNode}},{key:"getOutputNode",value:function(){return this.wetGain}},{key:"destroy",value:function(){this.delayNode.disconnect(),this.osc.stop(),this.osc.disconnect(),this.gain.disconnect(),this.wetGain.disconnect()}}])}(),u=function(){return v((function A(B){f(this,A),this.context=B,this.init()}),[{key:"init",value:function(){var A=this.context.createWaveShaper();A.curve=n(30);var B=this.context.createBiquadFilter();B.type="lowpass",B.frequency.value=2e3;var f=this.context.createBiquadFilter();f.type="lowpass",f.frequency.value=2e3;var v=this.context.createBiquadFilter();v.type="highpass",v.frequency.value=500;var g=this.context.createBiquadFilter();g.type="highpass",g.frequency.value=500;var w=this.context.createDynamicsCompressor();B.connect(f),f.connect(v),v.connect(g),g.connect(A),A.connect(w),this.waveShaper=A,this.lpf1=B,this.lpf2=f,this.hpf1=v,this.hpf2=g,this.compressor=w,this.inputNode=B,this.outputNode=w}},{key:"getInputNode",value:function(){return this.inputNode}},{key:"getOutputNode",value:function(){return this.outputNode}},{key:"destroy",value:function(){this.waveShaper.disconnect(),this.lpf1.disconnect(),this.lpf2.disconnect(),this.hpf1.disconnect(),this.hpf2.disconnect(),this.compressor.disconnect()}}])}(),H=function(){return v((function A(B){f(this,A),this.context=B,this.init()}),[{key:"init",value:function(){var A=this.context.createGain(),B=this.context.createOscillator();B.frequency.value=10,B.type="sawtooth";var f=this.context.createOscillator();f.frequency.value=50,f.type="sawtooth";var v=this.context.createOscillator();v.frequency.value=30,v.type="sawtooth";var g=this.context.createGain();g.gain.value=.007;var w=this.context.createGain();w.gain.value=.007;var P=this.context.createDelay();P.delayTime.value=.01;var t=this.context.createDelay();t.delayTime.value=.01;var Q=this.context.createBiquadFilter();Q.type="lowpass",Q.frequency.value=2e3;var e=this.context.createDynamicsCompressor(),C=this.context.createDynamicsCompressor(),n=this.context.createDynamicsCompressor(),r=this.context.createDynamicsCompressor(),D=this.context.createDynamicsCompressor();B.connect(g),f.connect(g),g.connect(P.delayTime),A.connect(C),C.connect(P),P.connect(n),n.connect(Q),Q.connect(D),v.connect(w),w.connect(t.delayTime),e.connect(t),t.connect(r),r.connect(Q),Q.connect(D),B.start(0),f.start(0),v.start(0),A.connect(e),this.oscillator1=B,this.oscillator2=f,this.oscillator3=v,this.oscillatorGain=g,this.oscillatorGain2=w,this.delay=P,this.delay2=t,this.filter=Q,this.compressor=e,this.compressor2=C,this.compressor3=n,this.compressor4=r,this.compressor5=D,this.inputNode=A,this.outputNode=D}},{key:"getInputNode",value:function(){return this.inputNode}},{key:"getOutputNode",value:function(){return this.outputNode}},{key:"destroy",value:function(){this.oscillator1.disconnect(),this.oscillator2.disconnect(),this.oscillator3.disconnect(),this.oscillatorGain.disconnect(),this.oscillatorGain2.disconnect(),this.delay.disconnect(),this.delay2.disconnect(),this.filter.disconnect(),this.compressor.disconnect(),this.compressor2.disconnect(),this.compressor3.disconnect(),this.compressor4.disconnect(),this.compressor5.disconnect(),this.inputNode.disconnect()}}])}(),h=function(){return v((function A(B){f(this,A),this.context=B,this.init()}),[{key:"init",value:function(){this.gain=this.context.createGain(),this.gain.value=1}},{key:"getInputNode",value:function(){return this.gain}},{key:"getOutputNode",value:function(){return this.gain}},{key:"destroy",value:function(){this.gain.disconnect(),this.gain=null}}])}(),I=function(){return v((function A(B){f(this,A),this.context=B,this.init()}),[{key:"init",value:function(){this.pitch=new o(this.context),this.pitch.setPitchOffset(1.8),this.waveShaper=this.context.createWaveShaper(),this.waveShaper.curve=n(3),this.preFilter=this.context.createBiquadFilter(),this.preFilter.type="peaking",this.preFilter.frequency.value=1200,this.preFilter.Q.value=2,this.preFilter.gain.value=8,this.lpf=this.context.createBiquadFilter(),this.lpf.type="lowpass",this.lpf.frequency.value=4500,this.hpf=this.context.createBiquadFilter(),this.hpf.type="highpass",this.hpf.frequency.value=200,this.chorus=new L(this.context,{rate:1.8,depth:.003,delay:.005}),this.compressor=this.context.createDynamicsCompressor(),this.compressor.threshold.value=-30,this.compressor.ratio.value=4,this.compressor.attack.value=.05,this.pitch.output.connect(this.preFilter),this.preFilter.connect(this.waveShaper),this.waveShaper.connect(this.lpf),this.lpf.connect(this.hpf),this.hpf.connect(this.chorus.input),this.chorus.output.connect(this.compressor),this.inputNode=this.pitch.input,this.outputNode=this.compressor}},{key:"getInputNode",value:function(){return this.inputNode}},{key:"getOutputNode",value:function(){return this.outputNode}},{key:"destroy",value:function(){[this.waveShaper,this.preFilter,this.lpf,this.hpf,this.compressor].forEach((function(A){A.disconnect()})),this.chorus.destroy(),this.pitch.destroy()}}])}(),L=function(){return v((function A(B,v){var g=this;f(this,A),this.context=B,this.delayNodes=[],this.gainNodes=[],Array.from({length:3}).forEach((function(A,f){var w=B.createDelay();w.delayTime.value=v.delay+.001*Math.sin(f);var P=B.createOscillator();P.frequency.value=v.rate*(f+1);var t=B.createGain();t.gain.value=v.depth,P.connect(t),t.connect(w.delayTime),g.delayNodes.push(w),P.start()})),this.input=B.createGain(),this.output=B.createGain(),this.delayNodes.forEach((function(A,f){g.input.connect(A);var v=B.createGain();v.gain.value=.3,A.connect(v),v.connect(g.output)}))}),[{key:"destroy",value:function(){this.delayNodes.forEach((function(A){return A.disconnect()}))}}])}(),F=function(){return v((function A(B){f(this,A),Q(this,"context"),Q(this,"mediaStream"),Q(this,"streamSource"),Q(this,"destination"),Q(this,"inputNode"),Q(this,"outputNode"),Q(this,"pitch"),Q(this,"changer"),Q(this,"preType",-1),Q(this,"isEnable",!1),this.context=B}),[{key:"getInputNode",value:function(){var A,B;return(null==(A=this.changer)?void 0:A.getInputNode())||(null==(B=this.pitch)?void 0:B.input)}},{key:"getOutputNode",value:function(){var A,B;return(null==(A=this.changer)?void 0:A.getOutputNode())||(null==(B=this.pitch)?void 0:B.output)}},{key:"setPitch",value:function(A){this.changer&&(this.changer.destroy(),this.changer=null),this.pitch||(this.pitch=new o(this.context)),this.pitch.setPitchOffset(A)}},{key:"getPitchInputNode",value:function(){var A;return null==(A=this.pitch)?void 0:A.input}},{key:"setEffect",value:(w=B(g().mark((function A(B){var f;return g().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:if(this.preType!==B){A.next=2;break}return A.abrupt("return");case 2:null==(f=this.changer)||f.destroy(),this.changer=null,A.t0=B,A.next=0===A.t0?7:1===A.t0?9:2===A.t0?11:3===A.t0?13:4===A.t0?15:5===A.t0?17:6===A.t0?19:7===A.t0?21:8===A.t0?23:9===A.t0?25:10===A.t0?27:11===A.t0?29:12===A.t0?33:37;break;case 7:return this.changer=new h(this.context),A.abrupt("break",39);case 9:return this.setPitch(.3),A.abrupt("break",39);case 11:return this.setPitch(1.1),A.abrupt("break",39);case 13:return this.setPitch(-.6),A.abrupt("break",39);case 15:return this.changer=new s(this.context),A.abrupt("break",39);case 17:return this.changer=new c(this.context),A.abrupt("break",39);case 19:return this.changer=new a(this.context),A.abrupt("break",39);case 21:return this.changer=new H(this.context),A.abrupt("break",39);case 23:return this.setPitch(-.4),A.abrupt("break",39);case 25:return this.changer=new u(this.context),A.abrupt("break",39);case 27:return this.changer=new i(this.context),A.abrupt("break",39);case 29:return this.changer=new E(this.context),A.next=32,this.changer.init();case 32:return A.abrupt("break",39);case 33:return this.changer=new I(this.context),A.next=36,this.changer.init();case 36:return A.abrupt("break",39);case 37:return this.changer=new h(this.context),A.abrupt("break",39);case 39:this.preType=B,this.isEnable=!0;case 41:case"end":return A.stop()}}),A,this)}))),function(A){return w.apply(this,arguments)})},{key:"close",value:(A=B(g().mark((function A(){return g().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:if(this.isEnable){A.next=2;break}return A.abrupt("return");case 2:this.destroy();case 3:case"end":return A.stop()}}),A,this)}))),function(){return A.apply(this,arguments)})},{key:"destroy",value:function(){var A,B;null==(A=this.changer)||A.destroy(),null==(B=this.pitch)||B.destroy(),this.pitch=null,this.changer=null,this.isEnable=!1}}]);var A,w}(),G=function(){function A(B){f(this,A),this.core=B,Q(this,"log"),Q(this,"_core"),Q(this,"processor"),Q(this,"pitchValue"),Q(this,"voiceType"),this.log=B.log.createChild({id:"".concat(this.getAlias())}),this._core=B}return v(A,[{key:"getName",value:function(){return A.Name}},{key:"getAlias",value:function(){return"voiceChanger"}},{key:"getGroup",value:function(){return"voiceChanger"}},{key:"getValidateRule",value:function(A){switch(A){case"start":return this.startValidateRule(this._core);case"update":return{name:"VoiceChangerUpdateOptions",type:"object",required:!0,allowEmpty:!1,properties:{voiceType:{type:"number",required:!0},pitch:{type:"number",required:!1}}};case"stop":return{name:"VoiceChangerStopOptions",required:!1}}}},{key:"startValidateRule",value:function(A){var B=this._core.errorModule,f=B.RtcError,v=B.ErrorCode,g=B.ErrorCodeDictionary;return{name:"options",required:!0,type:"object",properties:{sdkAppId:{type:"number",required:!0},userId:{type:"string",required:!0},userSig:{type:"string",required:!0},voiceType:{type:"number",required:!0}},validate:function(B,w,P,t){if(!A.room.audioManager.hasAudioTrack)throw new f({code:v.INVALID_OPERATION,extraCode:g.INVALID_OPERATION_NEED_AUDIO,fnName:P})}}}},{key:"start",value:(P=B(g().mark((function A(B){var f,v,w,P,t,Q,C,n,r,D,o;return g().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return A.next=2,e({sdkAppId:B.sdkAppId,userId:B.userId,userSig:B.userSig,core:this._core});case 2:if(f=A.sent,v=f.auth,w=B.voiceType,P=B.pitch,t=this._core.errorModule,Q=t.RtcError,C=t.ErrorCode,n=t.ErrorCodeDictionary,v){A.next=11;break}throw this.log.error("server code: ".concat(C.SERVER_ERROR," response: ").concat(n.NEED_TO_BUY)),this._core.kvStatManager.addFailedEvent({key:552700,error:n.NEED_TO_BUY}),r=this._core.utils.isOverseaSdkAppId(B.sdkAppId)?"https://trtc.io/document/56025":"https://cloud.tencent.com/document/product/647/85386",new Q({code:n.NEED_TO_BUY,messageParams:{value:"Voice Changer",url:r}});case 11:if(this.processor||(this.processor=new F(this._core.room.audioManager.audioContext)),void 0!==P&&this.processor.setPitch(Number(P)),A.t0=void 0!==w,!A.t0){A.next=17;break}return A.next=17,this.processor.setEffect(Number(w));case 17:D=this.processor.getInputNode(),o=this.processor.getOutputNode(),this._core.room.audioManager.addVoiceChanger(D,o),this.pitchValue=P,this.voiceType=w,this._core.kvStatManager.addEnum({key:552701,value:w}),this._core.kvStatManager.addSuccessEvent({key:552700}),this.log.info("voice change start success, voiceType: ".concat(w));case 25:case"end":return A.stop()}}),A,this)}))),function(A){return P.apply(this,arguments)})},{key:"update",value:(w=B(g().mark((function A(B){var f,v,w,P;return g().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:if(this.voiceType!==B.voiceType&&this.processor){A.next=2;break}return A.abrupt("return");case 2:if(this._core.room.audioManager.removeVoiceChanger(),f=B.voiceType,void 0!==(v=B.pitch)&&this.processor.setPitch(Number(v)),A.t0=void 0!==f,!A.t0){A.next=9;break}return A.next=9,this.processor.setEffect(Number(f));case 9:w=this.processor.getInputNode(),P=this.processor.getOutputNode(),this._core.room.audioManager.addVoiceChanger(w,P),this.pitchValue=v,this.voiceType=f,this._core.kvStatManager.addEnum({key:552701,value:f}),this.log.info("voice change update success, voiceType: ".concat(f));case 16:case"end":return A.stop()}}),A,this)}))),function(A){return w.apply(this,arguments)})},{key:"stop",value:function(){this.destroy(),this.log.info("voice change stop success")}},{key:"destroy",value:function(){var A;this._core.room.audioManager.removeVoiceChanger(),null==(A=this.processor)||A.close(),this.processor=void 0}}]);var w,P}();return Q(G,"Name","VoiceChanger"),G}));
