@echo off
chcp 65001 >nul

echo [INFO] 快速测试腾讯云直播平台后端...

cd backend

echo [INFO] 检查配置文件...
if not exist "config\.env" (
    echo [INFO] 复制配置文件...
    copy "config\.env.example" "config\.env" >nul
)

echo [INFO] 下载依赖...
go mod tidy

echo [INFO] 编译项目...
go build -o bin/server.exe cmd/server/main.go
if errorlevel 1 (
    echo [ERROR] 编译失败
    pause
    exit /b 1
)

echo [INFO] 编译成功！
echo [INFO] 启动服务器...
echo [INFO] 访问地址: http://localhost:8080/health
echo [INFO] 按 Ctrl+C 停止服务
echo.

bin\server.exe
