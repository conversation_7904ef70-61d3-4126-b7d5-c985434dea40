var __create=Object.create,__defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropNames=Object.getOwnPropertyNames,__getOwnPropSymbols=Object.getOwnPropertySymbols,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,t,r)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,__spreadValues=(e,t)=>{for(var r in t||(t={}))__hasOwnProp.call(t,r)&&__defNormalProp(e,r,t[r]);if(__getOwnPropSymbols)for(var r of __getOwnPropSymbols(t))__propIsEnum.call(t,r)&&__defNormalProp(e,r,t[r]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t)),__commonJS=(e,t)=>function(){return t||(0,e[__getOwnPropNames(e)[0]])((t={exports:{}}).exports,t),t.exports},__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of __getOwnPropNames(t))__hasOwnProp.call(e,o)||o===r||__defProp(e,o,{get:()=>t[o],enumerable:!(n=__getOwnPropDesc(t,o))||n.enumerable});return e},__toESM=(e,t,r)=>(r=null!=e?__create(__getProtoOf(e)):{},__copyProps(!t&&e&&e.__esModule?r:__defProp(r,"default",{value:e,enumerable:!0}),e)),__decorateClass=(e,t,r,n)=>{for(var o,a=n>1?void 0:n?__getOwnPropDesc(t,r):t,i=e.length-1;i>=0;i--)(o=e[i])&&(a=(n?o(t,r,a):o(a))||a);return n&&a&&__defProp(t,r,a),a},__publicField=(e,t,r)=>__defNormalProp(e,"symbol"!=typeof t?t+"":t,r),require_eventemitter3=__commonJS({"../node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js"(e,t){"use strict";var r=Object.prototype.hasOwnProperty,n="~";function o(){}function a(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,r,o,i){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new a(r,o||e,i),u=n?n+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],s]:e._events[u].push(s):(e._events[u]=s,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function u(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),u.prototype.eventNames=function(){var e,t,o=[];if(0===this._eventsCount)return o;for(t in e=this._events)r.call(e,t)&&o.push(n?t.slice(1):t);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},u.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,a=r.length,i=new Array(a);o<a;o++)i[o]=r[o].fn;return i},u.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},u.prototype.emit=function(e,t,r,o,a,i){var s=n?n+e:e;if(!this._events[s])return!1;var u,c,l=this._events[s],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,r),!0;case 4:return l.fn.call(l.context,t,r,o),!0;case 5:return l.fn.call(l.context,t,r,o,a),!0;case 6:return l.fn.call(l.context,t,r,o,a,i),!0}for(c=1,u=new Array(d-1);c<d;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var p,f=l.length;for(c=0;c<f;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),d){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,r);break;case 4:l[c].fn.call(l[c].context,t,r,o);break;default:if(!u)for(p=1,u=new Array(d-1);p<d;p++)u[p-1]=arguments[p];l[c].fn.apply(l[c].context,u)}}return!0},u.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},u.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},u.prototype.removeListener=function(e,t,r,o){var a=n?n+e:e;if(!this._events[a])return this;if(!t)return s(this,a),this;var i=this._events[a];if(i.fn)i.fn!==t||o&&!i.once||r&&i.context!==r||s(this,a);else{for(var u=0,c=[],l=i.length;u<l;u++)(i[u].fn!==t||o&&!i[u].once||r&&i[u].context!==r)&&c.push(i[u]);c.length?this._events[a]=1===c.length?c[0]:c:s(this,a)}return this},u.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&s(this,t)):(this._events=new o,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=n,u.EventEmitter=u,void 0!==t&&(t.exports=u)}}),import_eventemitter3=__toESM(require_eventemitter3(),1),instance=Symbol("instance"),abortCtrl=Symbol("abortCtrl"),cacheResult=Symbol("cacheResult"),MiddleState=class{constructor(e,t,r){this.oldState=e,this.newState=t,this.action=r,this.aborted=!1}abort(e){this.aborted=!0,setState.call(e,this.oldState,new Error(`action '${this.action}' aborted`))}toString(){return`${this.action}ing`}},FSMError=class extends Error{constructor(e,t,r){super(t),this.state=e,this.message=t,this.cause=r}};function thenAble(e){return"object"==typeof e&&e&&"then"in e}var stateDiagram=new Map;function ChangeState(e,t,r={}){return(n,o,a)=>{const i=r.action||o;if(!r.context){const r=stateDiagram.get(n)||[];stateDiagram.has(n)||stateDiagram.set(n,r),r.push({from:e,to:t,action:i})}const s=a.value;a.value=function(...n){let o=this;if(r.context&&(o=FSM.get("function"==typeof r.context?r.context.call(this,...n):r.context)),o.state===t)return r.sync?o[cacheResult]:Promise.resolve(o[cacheResult]);o.state instanceof MiddleState&&o.state.action==r.abortAction&&o.state.abort(o);let a=null;Array.isArray(e)?0==e.length?o.state instanceof MiddleState&&o.state.abort(o):"string"==typeof o.state&&e.includes(o.state)||(a=new FSMError(o._state,`${o.name} ${i} to ${t} failed: current state ${o._state} not from ${e.join("|")}`)):e!==o.state&&(a=new FSMError(o._state,`${o.name} ${i} to ${t} failed: current state ${o._state} not from ${e}`));const u=e=>{if(r.fail&&r.fail.call(this,e),r.sync){if(r.ignoreError)return e;throw e}return r.ignoreError?Promise.resolve(e):Promise.reject(e)};if(a)return u(a);const c=o.state,l=new MiddleState(c,t,i);setState.call(o,l);const d=e=>{var n;return o[cacheResult]=e,l.aborted||(setState.call(o,t),null===(n=r.success)||void 0===n||n.call(this,o[cacheResult])),e},p=e=>(setState.call(o,c,e),u(e));try{const e=s.apply(this,n);return thenAble(e)?e.then(d).catch(p):r.sync?d(e):Promise.resolve(d(e))}catch(r){return p(new FSMError(o._state,`${o.name} ${i} from ${e} to ${t} failed: ${r}`,r instanceof Error?r:new Error(String(r))))}}}}function Includes(...e){return(t,r,n)=>{const o=n.value,a=r;n.value=function(...t){if(!e.includes(this.state.toString()))throw new FSMError(this.state,`${this.name} ${a} failed: current state ${this.state} not in ${e}`);return o.apply(this,t)}}}var sendDevTools=(()=>{const e="undefined"!=typeof window&&window.__AFSM__,t="undefined"!=typeof importScripts;return e?(e,t)=>{window.dispatchEvent(new CustomEvent(e,{detail:t}))}:t?(e,t)=>{postMessage({type:e,payload:t})}:()=>{}})();function setState(e,t){const r=this._state;this._state=e;const n=e.toString();e&&this.emit(n,r),this.emit(FSM.STATECHANGED,e,r,t),this.updateDevTools({value:e,old:r,err:t instanceof Error?t.message:String(t)})}var FSM=class e extends import_eventemitter3.default{constructor(t,r,n){super(),this.name=t,this.groupName=r,this._state=e.INIT,t||(t=Date.now().toString(36)),n?Object.setPrototypeOf(this,n):n=Object.getPrototypeOf(this),r||(this.groupName=this.constructor.name);const o=n[instance];o?this.name=o.name+"-"+o.count++:n[instance]={name:this.name,count:0},this.updateDevTools({diagram:this.stateDiagram})}get stateDiagram(){const e=Object.getPrototypeOf(this),t=stateDiagram.get(e)||[];let r=new Set,n=[],o=[];const a=new Set,i=Object.getPrototypeOf(e);stateDiagram.has(i)&&(i.stateDiagram.forEach((e=>r.add(e))),i.allStates.forEach((e=>a.add(e)))),t.forEach((({from:e,to:t,action:r})=>{"string"==typeof e?n.push({from:e,to:t,action:r}):e.length?e.forEach((e=>{n.push({from:e,to:t,action:r})})):o.push({to:t,action:r})})),n.forEach((({from:e,to:t,action:n})=>{a.add(e),a.add(t),a.add(n+"ing"),r.add(`${e} --\x3e ${n}ing : ${n}`),r.add(`${n}ing --\x3e ${t} : ${n} 🟢`),r.add(`${n}ing --\x3e ${e} : ${n} 🔴`)})),o.forEach((({to:e,action:t})=>{r.add(`${t}ing --\x3e ${e} : ${t} 🟢`),a.forEach((n=>{n!==e&&r.add(`${n} --\x3e ${t}ing : ${t}`)}))}));const s=[...r];return Object.defineProperties(e,{stateDiagram:{value:s},allStates:{value:a}}),s}static get(t){let r;return"string"==typeof t?(r=e.instances.get(t),r||e.instances.set(t,r=new e(t,void 0,Object.create(e.prototype)))):(r=e.instances2.get(t),r||e.instances2.set(t,r=new e(t.constructor.name,void 0,Object.create(e.prototype)))),r}static getState(t){var r;return null===(r=e.get(t))||void 0===r?void 0:r.state}updateDevTools(t={}){sendDevTools(e.UPDATEAFSM,Object.assign({name:this.name,group:this.groupName},t))}get state(){return this._state}set state(e){setState.call(this,e)}};FSM.STATECHANGED="stateChanged",FSM.UPDATEAFSM="updateAFSM",FSM.INIT="[*]",FSM.ON="on",FSM.OFF="off",FSM.instances=new Map,FSM.instances2=new WeakMap;var VideoDecoderHard=class extends FSM{constructor(){super(...arguments),__publicField(this,"decoder"),__publicField(this,"config")}async initialize(){this.decoder=new VideoDecoder({output:e=>{this.emit("videoFrame",e)},error:e=>{this.close(),this.emit("error",e)}})}configure(e){this.config=e,this.decoder.configure(__spreadProps(__spreadValues({},e),{codec:this.getCodec(e)}))}getCodec(e){switch(e.codec){case"hevc":return"hvc1.1.6.L0.***********.9A.BC";case"av1":return"av01.0.05M.08";case"avc":return"avc1.420028";default:return e.codec}}decode(e){"configured"===this.decoder.state&&this.decoder.decode(new EncodedVideoChunk(e))}flush(){this.decoder.flush()}reset(){this.decoder.reset()}close(){"closed"!==this.decoder.state&&this.decoder.close()}};function WorkerScripts(){var e;self.onmessage=t=>{if("init"===t.data.type){const{canvas:r,wasmScript:n,wasmBinary:o}=t.data,a=null==r?void 0:r.getContext("2d");let i=0,s=0;const u={wasmBinary:o,postRun:()=>{e=new u.VideoDecoder({videoInfo(e,t){i=e,s=t,console.log("video info",e,t)},yuvData(e,t){const n=i*s,o=n>>2;let c=u.HEAPU32[e>>2],l=u.HEAPU32[1+(e>>2)],d=u.HEAPU32[2+(e>>2)],p=u.HEAPU8.subarray(c,c+n),f=u.HEAPU8.subarray(l,l+o),h=u.HEAPU8.subarray(d,d+o);const m=new Uint8Array(n+o+o);m.set(p),m.set(f,n),m.set(h,n+o);const v=new VideoFrame(m,{codedWidth:i,codedHeight:s,format:"I420",timestamp:t});r?(null==a||a.drawImage(v,0,0,r.width,r.height),null==a||a.commit()):self.postMessage({type:"yuvData",videoFrame:v},[v])}}),self.postMessage({type:"ready"})}};Function("var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;return "+n)()(u)}else if("decode"===t.data.type){const{packet:r}=t.data;null==e||e.decode(r.data,"key"==r.type,r.timestamp)}else if("setCodec"===t.data.type){const{codec:r,format:n,description:o}=t.data;null==e||e.setCodec(r,n,null!=o?o:"")}}}__decorateClass([ChangeState([FSM.INIT,"closed"],"initialized")],VideoDecoderHard.prototype,"initialize",1),__decorateClass([ChangeState("initialized","configured",{sync:!0})],VideoDecoderHard.prototype,"configure",1),__decorateClass([Includes("configured")],VideoDecoderHard.prototype,"decode",1),__decorateClass([ChangeState([],FSM.INIT,{sync:!0})],VideoDecoderHard.prototype,"reset",1),__decorateClass([ChangeState([],"closed",{ignoreError:!0,sync:!0})],VideoDecoderHard.prototype,"close",1);var VideoDecoderSoftBase=class extends FSM{constructor(e,t,r=!1,n,o=!1){super(),this.createModule=e,this.wasmBinary=t,this.workerMode=r,this.canvas=n,this.yuvMode=o,__publicField(this,"worker"),__publicField(this,"decoder"),__publicField(this,"config"),__publicField(this,"module",{}),__publicField(this,"width",0),__publicField(this,"height",0)}async initialize(e){var t;if(this.workerMode){const e=new RegExp("\\{(.+)\\}","s").exec(WorkerScripts.toString())[1];this.worker=new Worker(URL.createObjectURL(new Blob([e],{type:"text/javascript"})));const r=null==(t=this.canvas)?void 0:t.transferControlToOffscreen(),n=await this.wasmBinary;return console.warn("worker mode",n),this.worker.postMessage({type:"init",canvas:r,wasmScript:this.createModule.toString(),wasmBinary:n},r?[r,n]:[n]),new Promise((e=>{this.worker.onmessage=t=>{if("ready"===t.data.type)delete this.wasmBinary,e(),console.warn("worker mode initialize success");else if("yuvData"===t.data.type){const{videoFrame:e}=t.data;this.emit("videoFrame",e)}}}))}const r=this.module;return this.wasmBinary&&(r.wasmBinary=await this.wasmBinary),r.print=e=>console.log(e),r.printErr=e=>console.log(`[JS] ERROR: ${e}`),r.onAbort=()=>console.log("[JS] FATAL: WASM ABORTED"),new Promise((t=>{r.postRun=e=>{this.decoder=new this.module.VideoDecoder(this),console.log("video soft decoder initialize success"),t()},e&&Object.assign(r,e),this.createModule(r)}))}configure(e){var t,r,n;this.config=e;const o=this.config.codec.startsWith("avc")?"avc":"hevc",a=this.config.description?"avc"==o?"avcc":"hvcc":"annexb";null==(r=this.decoder)||r.setCodec(o,a,null!=(t=this.config.description)?t:""),null==(n=this.worker)||n.postMessage({type:"setCodec",codec:o,format:a,description:this.config.description})}decode(e){var t,r;null==(t=this.decoder)||t.decode(e.data,"key"==e.type,e.timestamp),"configured"===this.state&&(null==(r=this.worker)||r.postMessage({type:"decode",packet:e}))}flush(){}reset(){this.config=void 0,this.decoder&&this.decoder.clear()}close(){this.removeAllListeners(),this.decoder&&(this.decoder.clear(),this.decoder.delete())}videoInfo(e,t){this.width=e,this.height=t;let r={width:e,height:t};this.emit("videoCodecInfo",r)}yuvData(e,t){if(!this.module)return;const r=this.width*this.height,n=r>>2;let o=this.module.HEAPU32[e>>2],a=this.module.HEAPU32[1+(e>>2)],i=this.module.HEAPU32[2+(e>>2)],s=this.module.HEAPU8.subarray(o,o+r),u=this.module.HEAPU8.subarray(a,a+n),c=this.module.HEAPU8.subarray(i,i+n);if(this.yuvMode)return void this.emit("videoFrame",{y:s,u:u,v:c,timestamp:t});const l=new Uint8Array(r+n+n);l.set(s),l.set(u,r),l.set(c,r+n),this.emit("videoFrame",new VideoFrame(l,{codedWidth:this.width,codedHeight:this.height,format:"I420",timestamp:t}))}errorInfo(e){let t={errMsg:e};this.emit("error",t)}};__decorateClass([ChangeState([FSM.INIT,"closed"],"initialized")],VideoDecoderSoftBase.prototype,"initialize",1),__decorateClass([ChangeState("initialized","configured",{sync:!0})],VideoDecoderSoftBase.prototype,"configure",1),__decorateClass([ChangeState([],FSM.INIT,{sync:!0})],VideoDecoderSoftBase.prototype,"reset",1),__decorateClass([ChangeState([],"closed",{sync:!0})],VideoDecoderSoftBase.prototype,"close",1);var Module=(()=>{var e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(t={}){var r,n,o=t;o.ready=new Promise(((e,t)=>{r=e,n=t}));var a,i,s,u=Object.assign({},o),c="./this.program",l="object"==typeof window,d="function"==typeof importScripts,p=("object"==typeof process&&"object"==typeof process.versions&&process.versions.node,"");(l||d)&&(d?p=self.location.href:"undefined"!=typeof document&&document.currentScript&&(p=document.currentScript.src),e&&(p=e),p=0!==p.indexOf("blob:")?p.substr(0,p.replace(/[?#].*/,"").lastIndexOf("/")+1):"",a=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},d&&(s=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),i=(e,t,r)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?t(n.response):r()},n.onerror=r,n.send(null)});var f,h=o.print||console.log.bind(console),m=o.printErr||console.error.bind(console);Object.assign(o,u),u=null,o.arguments&&o.arguments,o.thisProgram&&(c=o.thisProgram),o.quit&&o.quit,o.wasmBinary&&(f=o.wasmBinary);var v,y;o.noExitRuntime;"object"!=typeof WebAssembly&&x("no native wasm support detected");var g,w,_,E,b,$,P,T,C,k=!1;var S=[],A=[],D=[];var F=0,O=null,M=null;function R(e){F++,o.monitorRunDependencies&&o.monitorRunDependencies(F)}function j(e){if(F--,o.monitorRunDependencies&&o.monitorRunDependencies(F),0==F&&(null!==O&&(clearInterval(O),O=null),M)){var t=M;M=null,t()}}function x(e){o.onAbort&&o.onAbort(e),m(e="Aborted("+e+")"),k=!0,e+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(e);throw n(t),t}var I,N,U,W;function B(e){return e.startsWith("data:application/octet-stream;base64,")}function V(e){if(e==I&&f)return new Uint8Array(f);if(s)return s(e);throw"both async and sync fetching of the wasm failed"}function L(e,t,r){return function(e){return f||!l&&!d||"function"!=typeof fetch?Promise.resolve().then((()=>V(e))):fetch(e,{credentials:"same-origin"}).then((t=>{if(!t.ok)throw"failed to load wasm binary file at '"+e+"'";return t.arrayBuffer()})).catch((()=>V(e)))}(e).then((e=>WebAssembly.instantiate(e,t))).then((e=>e)).then(r,(e=>{m("failed to asynchronously prepare wasm: "+e),x(e)}))}B(I="videodec.wasm")||(N=I,I=o.locateFile?o.locateFile(N,p):p+N);var z=e=>{for(;e.length>0;)e.shift()(o)};function H(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){$[this.ptr+4>>2]=e},this.get_type=function(){return $[this.ptr+4>>2]},this.set_destructor=function(e){$[this.ptr+8>>2]=e},this.get_destructor=function(){return $[this.ptr+8>>2]},this.set_caught=function(e){e=e?1:0,g[this.ptr+12|0]=e},this.get_caught=function(){return 0!=g[this.ptr+12|0]},this.set_rethrown=function(e){e=e?1:0,g[this.ptr+13|0]=e},this.get_rethrown=function(){return 0!=g[this.ptr+13|0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t)},this.set_adjusted_ptr=function(e){$[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return $[this.ptr+16>>2]},this.get_exception_ptr=function(){if(qt(this.get_type()))return $[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}var G={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,n=e.length-1;n>=0;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=G.isAbs(e),r="/"===e.substr(-1);return(e=G.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var t=G.splitPath(e),r=t[0],n=t[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."},basename:e=>{if("/"===e)return"/";var t=(e=(e=G.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments);return G.normalize(e.join("/"))},join2:(e,t)=>G.normalize(e+"/"+t)},q=e=>(q=(()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return e=>crypto.getRandomValues(e);x("initRandomDevice")})())(e),X={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var n=r>=0?arguments[r]:se.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,t=G.isAbs(n)}return(t?"/":"")+(e=G.normalizeArray(e.split("/").filter((e=>!!e)),!t).join("/"))||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=X.resolve(e).substr(1),t=X.resolve(t).substr(1);for(var n=r(e.split("/")),o=r(t.split("/")),a=Math.min(n.length,o.length),i=a,s=0;s<a;s++)if(n[s]!==o[s]){i=s;break}var u=[];for(s=i;s<n.length;s++)u.push("..");return(u=u.concat(o.slice(i))).join("/")}},K="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,Y=(e,t,r)=>{for(var n=t+r,o=t;e[o]&&!(o>=n);)++o;if(o-t>16&&e.buffer&&K)return K.decode(e.subarray(t,o));for(var a="";t<o;){var i=e[t++];if(128&i){var s=63&e[t++];if(192!=(224&i)){var u=63&e[t++];if((i=224==(240&i)?(15&i)<<12|s<<6|u:(7&i)<<18|s<<12|u<<6|63&e[t++])<65536)a+=String.fromCharCode(i);else{var c=i-65536;a+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else a+=String.fromCharCode((31&i)<<6|s)}else a+=String.fromCharCode(i)}return a},J=[],Z=e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n<=127?t++:n<=2047?t+=2:n>=55296&&n<=57343?(t+=4,++r):t+=3}return t},Q=(e,t,r,n)=>{if(!(n>0))return 0;for(var o=r,a=r+n-1,i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++i);if(s<=127){if(r>=a)break;t[r++]=s}else if(s<=2047){if(r+1>=a)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=a)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=a)break;t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}return t[r]=0,r-o};function ee(e,t,r){var n=r>0?r:Z(e)+1,o=new Array(n),a=Q(e,o,0,o.length);return t&&(o.length=a),o}var te={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){te.ttys[e]={input:[],output:[],ops:t},se.registerDevice(e,te.stream_ops)},stream_ops:{open:function(e){var t=te.ttys[e.node.rdev];if(!t)throw new se.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.fsync(e.tty)},fsync:function(e){e.tty.ops.fsync(e.tty)},read:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.get_char)throw new se.ErrnoError(60);for(var a=0,i=0;i<n;i++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new se.ErrnoError(29)}if(void 0===s&&0===a)throw new se.ErrnoError(6);if(null==s)break;a++,t[r+i]=s}return a&&(e.node.timestamp=Date.now()),a},write:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.put_char)throw new se.ErrnoError(60);try{for(var a=0;a<n;a++)e.tty.ops.put_char(e.tty,t[r+a])}catch(e){throw new se.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),a}},default_tty_ops:{get_char:function(e){return(()=>{if(!J.length){var e=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(e=window.prompt("Input: "))&&(e+="\n"):"function"==typeof readline&&null!==(e=readline())&&(e+="\n"),!e)return null;J=ee(e,!0)}return J.shift()})()},put_char:function(e,t){null===t||10===t?(h(Y(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},fsync:function(e){e.output&&e.output.length>0&&(h(Y(e.output,0)),e.output=[])},ioctl_tcgets:function(e){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets:function(e,t,r){return 0},ioctl_tiocgwinsz:function(e){return[24,80]}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(m(Y(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},fsync:function(e){e.output&&e.output.length>0&&(m(Y(e.output,0)),e.output=[])}}},re=e=>{x()},ne={ops_table:null,mount:e=>ne.createNode(null,"/",16895,0),createNode(e,t,r,n){if(se.isBlkdev(r)||se.isFIFO(r))throw new se.ErrnoError(63);ne.ops_table||(ne.ops_table={dir:{node:{getattr:ne.node_ops.getattr,setattr:ne.node_ops.setattr,lookup:ne.node_ops.lookup,mknod:ne.node_ops.mknod,rename:ne.node_ops.rename,unlink:ne.node_ops.unlink,rmdir:ne.node_ops.rmdir,readdir:ne.node_ops.readdir,symlink:ne.node_ops.symlink},stream:{llseek:ne.stream_ops.llseek}},file:{node:{getattr:ne.node_ops.getattr,setattr:ne.node_ops.setattr},stream:{llseek:ne.stream_ops.llseek,read:ne.stream_ops.read,write:ne.stream_ops.write,allocate:ne.stream_ops.allocate,mmap:ne.stream_ops.mmap,msync:ne.stream_ops.msync}},link:{node:{getattr:ne.node_ops.getattr,setattr:ne.node_ops.setattr,readlink:ne.node_ops.readlink},stream:{}},chrdev:{node:{getattr:ne.node_ops.getattr,setattr:ne.node_ops.setattr},stream:se.chrdev_stream_ops}});var o=se.createNode(e,t,r,n);return se.isDir(o.mode)?(o.node_ops=ne.ops_table.dir.node,o.stream_ops=ne.ops_table.dir.stream,o.contents={}):se.isFile(o.mode)?(o.node_ops=ne.ops_table.file.node,o.stream_ops=ne.ops_table.file.stream,o.usedBytes=0,o.contents=null):se.isLink(o.mode)?(o.node_ops=ne.ops_table.link.node,o.stream_ops=ne.ops_table.link.stream):se.isChrdev(o.mode)&&(o.node_ops=ne.ops_table.chrdev.node,o.stream_ops=ne.ops_table.chrdev.stream),o.timestamp=Date.now(),e&&(e.contents[t]=o,e.timestamp=o.timestamp),o},getFileDataAsTypedArray:e=>e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0),expandFileStorage(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var n=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr(e){var t={};return t.dev=se.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,se.isDir(e.mode)?t.size=4096:se.isFile(e.mode)?t.size=e.usedBytes:se.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&ne.resizeFileStorage(e,t.size)},lookup(e,t){throw se.genericErrors[44]},mknod:(e,t,r,n)=>ne.createNode(e,t,r,n),rename(e,t,r){if(se.isDir(e.mode)){var n;try{n=se.lookupNode(t,r)}catch(e){}if(n)for(var o in n.contents)throw new se.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir(e,t){var r=se.lookupNode(e,t);for(var n in r.contents)throw new se.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink(e,t,r){var n=ne.createNode(e,t,41471,0);return n.link=r,n},readlink(e){if(!se.isLink(e.mode))throw new se.ErrnoError(28);return e.link}},stream_ops:{read(e,t,r,n,o){var a=e.node.contents;if(o>=e.node.usedBytes)return 0;var i=Math.min(e.node.usedBytes-o,n);if(i>8&&a.subarray)t.set(a.subarray(o,o+i),r);else for(var s=0;s<i;s++)t[r+s]=a[o+s];return i},write(e,t,r,n,o,a){if(!n)return 0;var i=e.node;if(i.timestamp=Date.now(),t.subarray&&(!i.contents||i.contents.subarray)){if(a)return i.contents=t.subarray(r,r+n),i.usedBytes=n,n;if(0===i.usedBytes&&0===o)return i.contents=t.slice(r,r+n),i.usedBytes=n,n;if(o+n<=i.usedBytes)return i.contents.set(t.subarray(r,r+n),o),n}if(ne.expandFileStorage(i,o+n),i.contents.subarray&&t.subarray)i.contents.set(t.subarray(r,r+n),o);else for(var s=0;s<n;s++)i.contents[o+s]=t[r+s];return i.usedBytes=Math.max(i.usedBytes,o+n),n},llseek(e,t,r){var n=t;if(1===r?n+=e.position:2===r&&se.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new se.ErrnoError(28);return n},allocate(e,t,r){ne.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap(e,t,r,n,o){if(!se.isFile(e.node.mode))throw new se.ErrnoError(43);var a,i,s=e.node.contents;if(2&o||s.buffer!==g.buffer){if((r>0||r+t<s.length)&&(s=s.subarray?s.subarray(r,r+t):Array.prototype.slice.call(s,r,r+t)),i=!0,!(a=re()))throw new se.ErrnoError(48);g.set(s,a)}else i=!1,a=s.byteOffset;return{ptr:a,allocated:i}},msync:(e,t,r,n,o)=>(ne.stream_ops.write(e,t,0,n,r,!1),0)}},oe=(e,t,r,n)=>{var o=n?"":`al ${e}`;i(e,(r=>{var n;n=`Loading data file "${e}" failed (no arrayBuffer).`,r||x(n),t(new Uint8Array(r)),o&&j()}),(t=>{if(!r)throw`Loading data file "${e}" failed.`;r()})),o&&R()},ae=o.preloadPlugins||[];function ie(e,t){var r=0;return e&&(r|=365),t&&(r|=146),r}var se={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:(e,t={})=>{if(!(e=X.resolve(e)))return{path:"",node:null};if((t=Object.assign({follow_mount:!0,recurse_count:0},t)).recurse_count>8)throw new se.ErrnoError(32);for(var r=e.split("/").filter((e=>!!e)),n=se.root,o="/",a=0;a<r.length;a++){var i=a===r.length-1;if(i&&t.parent)break;if(n=se.lookupNode(n,r[a]),o=G.join2(o,r[a]),se.isMountpoint(n)&&(!i||i&&t.follow_mount)&&(n=n.mounted.root),!i||t.follow)for(var s=0;se.isLink(n.mode);){var u=se.readlink(o);if(o=X.resolve(G.dirname(o),u),n=se.lookupPath(o,{recurse_count:t.recurse_count+1}).node,s++>40)throw new se.ErrnoError(32)}}return{path:o,node:n}},getPath:e=>{for(var t;;){if(se.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?`${r}/${t}`:r+t:r}t=t?`${e.name}/${t}`:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,n=0;n<t.length;n++)r=(r<<5)-r+t.charCodeAt(n)|0;return(e+r>>>0)%se.nameTable.length},hashAddNode:e=>{var t=se.hashName(e.parent.id,e.name);e.name_next=se.nameTable[t],se.nameTable[t]=e},hashRemoveNode:e=>{var t=se.hashName(e.parent.id,e.name);if(se.nameTable[t]===e)se.nameTable[t]=e.name_next;else for(var r=se.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=se.mayLookup(e);if(r)throw new se.ErrnoError(r,e);for(var n=se.hashName(e.id,t),o=se.nameTable[n];o;o=o.name_next){var a=o.name;if(o.parent.id===e.id&&a===t)return o}return se.lookup(e,t)},createNode:(e,t,r,n)=>{var o=new se.FSNode(e,t,r,n);return se.hashAddNode(o),o},destroyNode:e=>{se.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>!(49152&~e),flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>se.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{var t=se.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{se.lookupNode(e,t);return 20}catch(e){}return se.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var n;try{n=se.lookupNode(e,t)}catch(e){return e.errno}var o=se.nodePermissions(e,"wx");if(o)return o;if(r){if(!se.isDir(n.mode))return 54;if(se.isRoot(n)||se.getPath(n)===se.cwd())return 10}else if(se.isDir(n.mode))return 31;return 0},mayOpen:(e,t)=>e?se.isLink(e.mode)?32:se.isDir(e.mode)&&("r"!==se.flagsToPermissionString(t)||512&t)?31:se.nodePermissions(e,se.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:()=>{for(var e=0;e<=se.MAX_OPEN_FDS;e++)if(!se.streams[e])return e;throw new se.ErrnoError(33)},getStreamChecked:e=>{var t=se.getStream(e);if(!t)throw new se.ErrnoError(8);return t},getStream:e=>se.streams[e],createStream:(e,t=-1)=>(se.FSStream||(se.FSStream=function(){this.shared={}},se.FSStream.prototype={},Object.defineProperties(se.FSStream.prototype,{object:{get(){return this.node},set(e){this.node=e}},isRead:{get(){return 1!=(2097155&this.flags)}},isWrite:{get(){return!!(2097155&this.flags)}},isAppend:{get(){return 1024&this.flags}},flags:{get(){return this.shared.flags},set(e){this.shared.flags=e}},position:{get(){return this.shared.position},set(e){this.shared.position=e}}})),e=Object.assign(new se.FSStream,e),-1==t&&(t=se.nextfd()),e.fd=t,se.streams[t]=e,e),closeStream:e=>{se.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=se.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new se.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{se.devices[e]={stream_ops:t}},getDevice:e=>se.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var n=r.pop();t.push(n),r.push.apply(r,n.mounts)}return t},syncfs:(e,t)=>{"function"==typeof e&&(t=e,e=!1),se.syncFSRequests++,se.syncFSRequests>1&&m(`warning: ${se.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`);var r=se.getMounts(se.root.mount),n=0;function o(e){return se.syncFSRequests--,t(e)}function a(e){if(e)return a.errored?void 0:(a.errored=!0,o(e));++n>=r.length&&o(null)}r.forEach((t=>{if(!t.type.syncfs)return a(null);t.type.syncfs(t,e,a)}))},mount:(e,t,r)=>{var n,o="/"===r,a=!r;if(o&&se.root)throw new se.ErrnoError(10);if(!o&&!a){var i=se.lookupPath(r,{follow_mount:!1});if(r=i.path,n=i.node,se.isMountpoint(n))throw new se.ErrnoError(10);if(!se.isDir(n.mode))throw new se.ErrnoError(54)}var s={type:e,opts:t,mountpoint:r,mounts:[]},u=e.mount(s);return u.mount=s,s.root=u,o?se.root=u:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),u},unmount:e=>{var t=se.lookupPath(e,{follow_mount:!1});if(!se.isMountpoint(t.node))throw new se.ErrnoError(28);var r=t.node,n=r.mounted,o=se.getMounts(n);Object.keys(se.nameTable).forEach((e=>{for(var t=se.nameTable[e];t;){var r=t.name_next;o.includes(t.mount)&&se.destroyNode(t),t=r}})),r.mounted=null;var a=r.mount.mounts.indexOf(n);r.mount.mounts.splice(a,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var n=se.lookupPath(e,{parent:!0}).node,o=G.basename(e);if(!o||"."===o||".."===o)throw new se.ErrnoError(28);var a=se.mayCreate(n,o);if(a)throw new se.ErrnoError(a);if(!n.node_ops.mknod)throw new se.ErrnoError(63);return n.node_ops.mknod(n,o,t,r)},create:(e,t)=>(t=void 0!==t?t:438,t&=4095,t|=32768,se.mknod(e,t,0)),mkdir:(e,t)=>(t=void 0!==t?t:511,t&=1023,t|=16384,se.mknod(e,t,0)),mkdirTree:(e,t)=>{for(var r=e.split("/"),n="",o=0;o<r.length;++o)if(r[o]){n+="/"+r[o];try{se.mkdir(n,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),t|=8192,se.mknod(e,t,r)),symlink:(e,t)=>{if(!X.resolve(e))throw new se.ErrnoError(44);var r=se.lookupPath(t,{parent:!0}).node;if(!r)throw new se.ErrnoError(44);var n=G.basename(t),o=se.mayCreate(r,n);if(o)throw new se.ErrnoError(o);if(!r.node_ops.symlink)throw new se.ErrnoError(63);return r.node_ops.symlink(r,n,e)},rename:(e,t)=>{var r,n,o=G.dirname(e),a=G.dirname(t),i=G.basename(e),s=G.basename(t);if(r=se.lookupPath(e,{parent:!0}).node,n=se.lookupPath(t,{parent:!0}).node,!r||!n)throw new se.ErrnoError(44);if(r.mount!==n.mount)throw new se.ErrnoError(75);var u,c=se.lookupNode(r,i),l=X.relative(e,a);if("."!==l.charAt(0))throw new se.ErrnoError(28);if("."!==(l=X.relative(t,o)).charAt(0))throw new se.ErrnoError(55);try{u=se.lookupNode(n,s)}catch(e){}if(c!==u){var d=se.isDir(c.mode),p=se.mayDelete(r,i,d);if(p)throw new se.ErrnoError(p);if(p=u?se.mayDelete(n,s,d):se.mayCreate(n,s))throw new se.ErrnoError(p);if(!r.node_ops.rename)throw new se.ErrnoError(63);if(se.isMountpoint(c)||u&&se.isMountpoint(u))throw new se.ErrnoError(10);if(n!==r&&(p=se.nodePermissions(r,"w")))throw new se.ErrnoError(p);se.hashRemoveNode(c);try{r.node_ops.rename(c,n,s)}catch(e){throw e}finally{se.hashAddNode(c)}}},rmdir:e=>{var t=se.lookupPath(e,{parent:!0}).node,r=G.basename(e),n=se.lookupNode(t,r),o=se.mayDelete(t,r,!0);if(o)throw new se.ErrnoError(o);if(!t.node_ops.rmdir)throw new se.ErrnoError(63);if(se.isMountpoint(n))throw new se.ErrnoError(10);t.node_ops.rmdir(t,r),se.destroyNode(n)},readdir:e=>{var t=se.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new se.ErrnoError(54);return t.node_ops.readdir(t)},unlink:e=>{var t=se.lookupPath(e,{parent:!0}).node;if(!t)throw new se.ErrnoError(44);var r=G.basename(e),n=se.lookupNode(t,r),o=se.mayDelete(t,r,!1);if(o)throw new se.ErrnoError(o);if(!t.node_ops.unlink)throw new se.ErrnoError(63);if(se.isMountpoint(n))throw new se.ErrnoError(10);t.node_ops.unlink(t,r),se.destroyNode(n)},readlink:e=>{var t=se.lookupPath(e).node;if(!t)throw new se.ErrnoError(44);if(!t.node_ops.readlink)throw new se.ErrnoError(28);return X.resolve(se.getPath(t.parent),t.node_ops.readlink(t))},stat:(e,t)=>{var r=se.lookupPath(e,{follow:!t}).node;if(!r)throw new se.ErrnoError(44);if(!r.node_ops.getattr)throw new se.ErrnoError(63);return r.node_ops.getattr(r)},lstat:e=>se.stat(e,!0),chmod:(e,t,r)=>{var n;"string"==typeof e?n=se.lookupPath(e,{follow:!r}).node:n=e;if(!n.node_ops.setattr)throw new se.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&t|-4096&n.mode,timestamp:Date.now()})},lchmod:(e,t)=>{se.chmod(e,t,!0)},fchmod:(e,t)=>{var r=se.getStreamChecked(e);se.chmod(r.node,t)},chown:(e,t,r,n)=>{var o;"string"==typeof e?o=se.lookupPath(e,{follow:!n}).node:o=e;if(!o.node_ops.setattr)throw new se.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown:(e,t,r)=>{se.chown(e,t,r,!0)},fchown:(e,t,r)=>{var n=se.getStreamChecked(e);se.chown(n.node,t,r)},truncate:(e,t)=>{if(t<0)throw new se.ErrnoError(28);var r;"string"==typeof e?r=se.lookupPath(e,{follow:!0}).node:r=e;if(!r.node_ops.setattr)throw new se.ErrnoError(63);if(se.isDir(r.mode))throw new se.ErrnoError(31);if(!se.isFile(r.mode))throw new se.ErrnoError(28);var n=se.nodePermissions(r,"w");if(n)throw new se.ErrnoError(n);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{var r=se.getStreamChecked(e);if(!(2097155&r.flags))throw new se.ErrnoError(28);se.truncate(r.node,t)},utime:(e,t,r)=>{var n=se.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(t,r)})},open:(e,t,r)=>{if(""===e)throw new se.ErrnoError(44);var n;if(r=void 0===r?438:r,r=64&(t="string"==typeof t?function(e){var t={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[e];if(void 0===t)throw new Error(`Unknown file open mode: ${e}`);return t}(t):t)?4095&r|32768:0,"object"==typeof e)n=e;else{e=G.normalize(e);try{n=se.lookupPath(e,{follow:!(131072&t)}).node}catch(e){}}var a=!1;if(64&t)if(n){if(128&t)throw new se.ErrnoError(20)}else n=se.mknod(e,r,0),a=!0;if(!n)throw new se.ErrnoError(44);if(se.isChrdev(n.mode)&&(t&=-513),65536&t&&!se.isDir(n.mode))throw new se.ErrnoError(54);if(!a){var i=se.mayOpen(n,t);if(i)throw new se.ErrnoError(i)}512&t&&!a&&se.truncate(n,0),t&=-131713;var s=se.createStream({node:n,path:se.getPath(n),flags:t,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return s.stream_ops.open&&s.stream_ops.open(s),!o.logReadFiles||1&t||(se.readFiles||(se.readFiles={}),e in se.readFiles||(se.readFiles[e]=1)),s},close:e=>{if(se.isClosed(e))throw new se.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{se.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(se.isClosed(e))throw new se.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new se.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new se.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,n,o)=>{if(n<0||o<0)throw new se.ErrnoError(28);if(se.isClosed(e))throw new se.ErrnoError(8);if(1==(2097155&e.flags))throw new se.ErrnoError(8);if(se.isDir(e.node.mode))throw new se.ErrnoError(31);if(!e.stream_ops.read)throw new se.ErrnoError(28);var a=void 0!==o;if(a){if(!e.seekable)throw new se.ErrnoError(70)}else o=e.position;var i=e.stream_ops.read(e,t,r,n,o);return a||(e.position+=i),i},write:(e,t,r,n,o,a)=>{if(n<0||o<0)throw new se.ErrnoError(28);if(se.isClosed(e))throw new se.ErrnoError(8);if(!(2097155&e.flags))throw new se.ErrnoError(8);if(se.isDir(e.node.mode))throw new se.ErrnoError(31);if(!e.stream_ops.write)throw new se.ErrnoError(28);e.seekable&&1024&e.flags&&se.llseek(e,0,2);var i=void 0!==o;if(i){if(!e.seekable)throw new se.ErrnoError(70)}else o=e.position;var s=e.stream_ops.write(e,t,r,n,o,a);return i||(e.position+=s),s},allocate:(e,t,r)=>{if(se.isClosed(e))throw new se.ErrnoError(8);if(t<0||r<=0)throw new se.ErrnoError(28);if(!(2097155&e.flags))throw new se.ErrnoError(8);if(!se.isFile(e.node.mode)&&!se.isDir(e.node.mode))throw new se.ErrnoError(43);if(!e.stream_ops.allocate)throw new se.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,n,o)=>{if(2&n&&!(2&o)&&2!=(2097155&e.flags))throw new se.ErrnoError(2);if(1==(2097155&e.flags))throw new se.ErrnoError(2);if(!e.stream_ops.mmap)throw new se.ErrnoError(43);return e.stream_ops.mmap(e,t,r,n,o)},msync:(e,t,r,n,o)=>e.stream_ops.msync?e.stream_ops.msync(e,t,r,n,o):0,munmap:e=>0,ioctl:(e,t,r)=>{if(!e.stream_ops.ioctl)throw new se.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:(e,t={})=>{if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error(`Invalid encoding type "${t.encoding}"`);var r,n=se.open(e,t.flags),o=se.stat(e).size,a=new Uint8Array(o);return se.read(n,a,0,o,0),"utf8"===t.encoding?r=Y(a,0):"binary"===t.encoding&&(r=a),se.close(n),r},writeFile:(e,t,r={})=>{r.flags=r.flags||577;var n=se.open(e,r.flags,r.mode);if("string"==typeof t){var o=new Uint8Array(Z(t)+1),a=Q(t,o,0,o.length);se.write(n,o,0,a,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");se.write(n,t,0,t.byteLength,void 0,r.canOwn)}se.close(n)},cwd:()=>se.currentPath,chdir:e=>{var t=se.lookupPath(e,{follow:!0});if(null===t.node)throw new se.ErrnoError(44);if(!se.isDir(t.node.mode))throw new se.ErrnoError(54);var r=se.nodePermissions(t.node,"x");if(r)throw new se.ErrnoError(r);se.currentPath=t.path},createDefaultDirectories:()=>{se.mkdir("/tmp"),se.mkdir("/home"),se.mkdir("/home/<USER>")},createDefaultDevices:()=>{se.mkdir("/dev"),se.registerDevice(se.makedev(1,3),{read:()=>0,write:(e,t,r,n,o)=>n}),se.mkdev("/dev/null",se.makedev(1,3)),te.register(se.makedev(5,0),te.default_tty_ops),te.register(se.makedev(6,0),te.default_tty1_ops),se.mkdev("/dev/tty",se.makedev(5,0)),se.mkdev("/dev/tty1",se.makedev(6,0));var e=new Uint8Array(1024),t=0,r=()=>(0===t&&(t=q(e).byteLength),e[--t]);se.createDevice("/dev","random",r),se.createDevice("/dev","urandom",r),se.mkdir("/dev/shm"),se.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{se.mkdir("/proc");var e=se.mkdir("/proc/self");se.mkdir("/proc/self/fd"),se.mount({mount:()=>{var t=se.createNode(e,"fd",16895,73);return t.node_ops={lookup:(e,t)=>{var r=+t,n=se.getStreamChecked(r),o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>n.path}};return o.parent=o,o}},t}},{},"/proc/self/fd")},createStandardStreams:()=>{o.stdin?se.createDevice("/dev","stdin",o.stdin):se.symlink("/dev/tty","/dev/stdin"),o.stdout?se.createDevice("/dev","stdout",null,o.stdout):se.symlink("/dev/tty","/dev/stdout"),o.stderr?se.createDevice("/dev","stderr",null,o.stderr):se.symlink("/dev/tty1","/dev/stderr");se.open("/dev/stdin",0),se.open("/dev/stdout",1),se.open("/dev/stderr",1)},ensureErrnoError:()=>{se.ErrnoError||(se.ErrnoError=function(e,t){this.name="ErrnoError",this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},se.ErrnoError.prototype=new Error,se.ErrnoError.prototype.constructor=se.ErrnoError,[44].forEach((e=>{se.genericErrors[e]=new se.ErrnoError(e),se.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:()=>{se.ensureErrnoError(),se.nameTable=new Array(4096),se.mount(ne,{},"/"),se.createDefaultDirectories(),se.createDefaultDevices(),se.createSpecialDirectories(),se.filesystems={MEMFS:ne}},init:(e,t,r)=>{se.init.initialized=!0,se.ensureErrnoError(),o.stdin=e||o.stdin,o.stdout=t||o.stdout,o.stderr=r||o.stderr,se.createStandardStreams()},quit:()=>{se.init.initialized=!1;for(var e=0;e<se.streams.length;e++){var t=se.streams[e];t&&se.close(t)}},findObject:(e,t)=>{var r=se.analyzePath(e,t);return r.exists?r.object:null},analyzePath:(e,t)=>{try{e=(n=se.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=se.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=G.basename(e),n=se.lookupPath(e,{follow:!t}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,n)=>{e="string"==typeof e?e:se.getPath(e);for(var o=t.split("/").reverse();o.length;){var a=o.pop();if(a){var i=G.join2(e,a);try{se.mkdir(i)}catch(e){}e=i}}return i},createFile:(e,t,r,n,o)=>{var a=G.join2("string"==typeof e?e:se.getPath(e),t),i=ie(n,o);return se.create(a,i)},createDataFile:(e,t,r,n,o,a)=>{var i=t;e&&(e="string"==typeof e?e:se.getPath(e),i=t?G.join2(e,t):e);var s=ie(n,o),u=se.create(i,s);if(r){if("string"==typeof r){for(var c=new Array(r.length),l=0,d=r.length;l<d;++l)c[l]=r.charCodeAt(l);r=c}se.chmod(u,146|s);var p=se.open(u,577);se.write(p,r,0,r.length,0,a),se.close(p),se.chmod(u,s)}return u},createDevice:(e,t,r,n)=>{var o=G.join2("string"==typeof e?e:se.getPath(e),t),a=ie(!!r,!!n);se.createDevice.major||(se.createDevice.major=64);var i=se.makedev(se.createDevice.major++,0);return se.registerDevice(i,{open:e=>{e.seekable=!1},close:e=>{n&&n.buffer&&n.buffer.length&&n(10)},read:(e,t,n,o,a)=>{for(var i=0,s=0;s<o;s++){var u;try{u=r()}catch(e){throw new se.ErrnoError(29)}if(void 0===u&&0===i)throw new se.ErrnoError(6);if(null==u)break;i++,t[n+s]=u}return i&&(e.node.timestamp=Date.now()),i},write:(e,t,r,o,a)=>{for(var i=0;i<o;i++)try{n(t[r+i])}catch(e){throw new se.ErrnoError(29)}return o&&(e.node.timestamp=Date.now()),i}}),se.mkdev(o,a,i)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!a)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=ee(a(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new se.ErrnoError(29)}},createLazyFile:(e,t,r,n,o)=>{function a(){this.lengthKnown=!1,this.chunks=[]}if(a.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},a.prototype.setDataGetter=function(e){this.getter=e},a.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,n=Number(e.getResponseHeader("Content-length")),o=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,a=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,i=1048576;o||(i=n);var s=this;s.setDataGetter((e=>{var t=e*i,o=(e+1)*i-1;if(o=Math.min(o,n-1),void 0===s.chunks[e]&&(s.chunks[e]=((e,t)=>{if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",r,!1),n!==i&&o.setRequestHeader("Range","bytes="+e+"-"+t),o.responseType="arraybuffer",o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+r+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):ee(o.responseText||"",!0)})(t,o)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!a&&n||(i=n=1,n=this.getter(0).length,i=n,h("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=i,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!d)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var i=new a;Object.defineProperties(i,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var s={isDevice:!1,contents:i}}else s={isDevice:!1,url:r};var u=se.createFile(e,t,s,n,o);s.contents?u.contents=s.contents:s.url&&(u.contents=null,u.url=s.url),Object.defineProperties(u,{usedBytes:{get:function(){return this.contents.length}}});var c={};function l(e,t,r,n,o){var a=e.node.contents;if(o>=a.length)return 0;var i=Math.min(a.length-o,n);if(a.slice)for(var s=0;s<i;s++)t[r+s]=a[o+s];else for(s=0;s<i;s++)t[r+s]=a.get(o+s);return i}return Object.keys(u.stream_ops).forEach((e=>{var t=u.stream_ops[e];c[e]=function(){return se.forceLoadFile(u),t.apply(null,arguments)}})),c.read=(e,t,r,n,o)=>(se.forceLoadFile(u),l(e,t,r,n,o)),c.mmap=(e,t,r,n,o)=>{se.forceLoadFile(u);var a=re();if(!a)throw new se.ErrnoError(48);return l(e,g,a,t,r),{ptr:a,allocated:!0}},u.stream_ops=c,u}},ue=(e,t)=>e?Y(w,e,t):"",ce={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(G.isAbs(t))return t;var n;-100===e?n=se.cwd():n=ce.getStreamFromFD(e).path;if(0==t.length){if(!r)throw new se.ErrnoError(44);return n}return G.join2(n,t)},doStat:function(e,t,r){try{var n=e(t)}catch(e){if(e&&e.node&&G.normalize(t)!==G.normalize(se.getPath(e.node)))return-54;throw e}b[r>>2]=n.dev,b[r+4>>2]=n.mode,$[r+8>>2]=n.nlink,b[r+12>>2]=n.uid,b[r+16>>2]=n.gid,b[r+20>>2]=n.rdev,W=[n.size>>>0,(U=n.size,+Math.abs(U)>=1?U>0?+Math.floor(U/4294967296)>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],b[r+24>>2]=W[0],b[r+28>>2]=W[1],b[r+32>>2]=4096,b[r+36>>2]=n.blocks;var o=n.atime.getTime(),a=n.mtime.getTime(),i=n.ctime.getTime();return W=[Math.floor(o/1e3)>>>0,(U=Math.floor(o/1e3),+Math.abs(U)>=1?U>0?+Math.floor(U/4294967296)>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],b[r+40>>2]=W[0],b[r+44>>2]=W[1],$[r+48>>2]=o%1e3*1e3,W=[Math.floor(a/1e3)>>>0,(U=Math.floor(a/1e3),+Math.abs(U)>=1?U>0?+Math.floor(U/4294967296)>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],b[r+56>>2]=W[0],b[r+60>>2]=W[1],$[r+64>>2]=a%1e3*1e3,W=[Math.floor(i/1e3)>>>0,(U=Math.floor(i/1e3),+Math.abs(U)>=1?U>0?+Math.floor(U/4294967296)>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],b[r+72>>2]=W[0],b[r+76>>2]=W[1],$[r+80>>2]=i%1e3*1e3,W=[n.ino>>>0,(U=n.ino,+Math.abs(U)>=1?U>0?+Math.floor(U/4294967296)>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],b[r+88>>2]=W[0],b[r+92>>2]=W[1],0},doMsync:function(e,t,r,n,o){if(!se.isFile(t.node.mode))throw new se.ErrnoError(43);if(2&n)return 0;var a=w.slice(e,e+r);se.msync(t,a,o,r,n)},varargs:void 0,get:()=>(ce.varargs+=4,b[ce.varargs-4>>2]),getStr:e=>ue(e),getStreamFromFD:function(e){return se.getStreamChecked(e)}};function le(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${e}`)}}var de=void 0;function pe(e){for(var t="",r=e;w[r];)t+=de[w[r++]];return t}var fe={},he={},me={},ve=void 0;function ye(e){throw new ve(e)}var ge=void 0;function we(e){throw new ge(e)}function _e(e,t,r){function n(t){var n=r(t);n.length!==e.length&&we("Mismatched type converter count");for(var o=0;o<e.length;++o)Ee(e[o],n[o])}e.forEach((function(e){me[e]=t}));var o=new Array(t.length),a=[],i=0;t.forEach(((e,t)=>{he.hasOwnProperty(e)?o[t]=he[e]:(a.push(e),fe.hasOwnProperty(e)||(fe[e]=[]),fe[e].push((()=>{o[t]=he[e],++i===a.length&&n(o)})))})),0===a.length&&n(o)}function Ee(e,t,r={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(e,t,r={}){var n=t.name;if(e||ye(`type "${n}" must have a positive integer typeid pointer`),he.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;ye(`Cannot register type '${n}' twice`)}if(he[e]=t,delete me[e],fe.hasOwnProperty(e)){var o=fe[e];delete fe[e],o.forEach((e=>e()))}}(e,t,r)}function be(e){if(!(this instanceof ze))return!1;if(!(e instanceof ze))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return t===n&&r===o}function $e(e){ye(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Pe=!1;function Te(e){}function Ce(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function ke(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var n=ke(e,t,r.baseClass);return null===n?null:r.downcast(n)}var Se={};function Ae(){return Object.keys(je).length}function De(){var e=[];for(var t in je)je.hasOwnProperty(t)&&e.push(je[t]);return e}var Fe=[];function Oe(){for(;Fe.length;){var e=Fe.pop();e.$$.deleteScheduled=!1,e.delete()}}var Me=void 0;function Re(e){Me=e,Fe.length&&Me&&Me(Oe)}var je={};function xe(e,t){return t=function(e,t){for(void 0===t&&ye("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),je[t]}function Ie(e,t){return t.ptrType&&t.ptr||we("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!==!!t.smartPtr&&we("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Ue(Object.create(e,{$$:{value:t}}))}function Ne(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=xe(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var n=r.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?Ie(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):Ie(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var a,i=this.registeredClass.getActualType(t),s=Se[i];if(!s)return o.call(this);a=this.isConst?s.constPointerType:s.pointerType;var u=ke(t,this.registeredClass,a.registeredClass);return null===u?o.call(this):this.isSmartPointer?Ie(a.registeredClass.instancePrototype,{ptrType:a,ptr:u,smartPtrType:this,smartPtr:e}):Ie(a.registeredClass.instancePrototype,{ptrType:a,ptr:u})}var Ue=function(e){return"undefined"==typeof FinalizationRegistry?(Ue=e=>e,e):(Pe=new FinalizationRegistry((e=>{Ce(e.$$)})),Te=e=>Pe.unregister(e),(Ue=e=>{var t=e.$$;if(!!t.smartPtr){var r={$$:t};Pe.register(e,r,e)}return e})(e))};function We(){if(this.$$.ptr||$e(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=Ue(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function Be(){this.$$.ptr||$e(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ye("Object already scheduled for deletion"),Te(this),Ce(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Ve(){return!this.$$.ptr}function Le(){return this.$$.ptr||$e(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&ye("Object already scheduled for deletion"),Fe.push(this),1===Fe.length&&Me&&Me(Oe),this.$$.deleteScheduled=!0,this}function ze(){}function He(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=48&&t<=57?`_${e}`:e}function Ge(e,t){return e=He(e),{[e]:function(){return t.apply(this,arguments)}}[e]}function qe(e,t,r){if(void 0===e[t].overloadTable){var n=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||ye(`Function '${r}' called with an invalid number of arguments (${arguments.length}) - expects one of (${e[t].overloadTable})!`),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}}function Xe(e,t,r,n,o,a,i,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=o,this.getActualType=a,this.upcast=i,this.downcast=s,this.pureVirtualFunctions=[]}function Ke(e,t,r){for(;t!==r;)t.upcast||ye(`Expected null or instance of ${r.name}, got an instance of ${t.name}`),e=t.upcast(e),t=t.baseClass;return e}function Ye(e,t){if(null===t)return this.isReference&&ye(`null is not a valid ${this.name}`),0;t.$$||ye(`Cannot pass "${_t(t)}" as a ${this.name}`),t.$$.ptr||ye(`Cannot pass deleted object as a pointer of type ${this.name}`);var r=t.$$.ptrType.registeredClass;return Ke(t.$$.ptr,r,this.registeredClass)}function Je(e,t){var r;if(null===t)return this.isReference&&ye(`null is not a valid ${this.name}`),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||ye(`Cannot pass "${_t(t)}" as a ${this.name}`),t.$$.ptr||ye(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&t.$$.ptrType.isConst&&ye(`Cannot convert argument of type ${t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name} to parameter type ${this.name}`);var n=t.$$.ptrType.registeredClass;if(r=Ke(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&ye("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:ye(`Cannot convert argument of type ${t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var o=t.clone();r=this.rawShare(r,wt.toHandle((function(){o.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:ye("Unsupporting sharing policy")}return r}function Ze(e,t){if(null===t)return this.isReference&&ye(`null is not a valid ${this.name}`),0;t.$$||ye(`Cannot pass "${_t(t)}" as a ${this.name}`),t.$$.ptr||ye(`Cannot pass deleted object as a pointer of type ${this.name}`),t.$$.ptrType.isConst&&ye(`Cannot convert argument of type ${t.$$.ptrType.name} to parameter type ${this.name}`);var r=t.$$.ptrType.registeredClass;return Ke(t.$$.ptr,r,this.registeredClass)}function Qe(e){return this.fromWireType(b[e>>2])}function et(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function tt(e){this.rawDestructor&&this.rawDestructor(e)}function rt(e){null!==e&&e.delete()}function nt(e,t,r,n,o,a,i,s,u,c,l){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=o,this.pointeeType=a,this.sharingPolicy=i,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=l,o||void 0!==t.baseClass?this.toWireType=Je:n?(this.toWireType=Ye,this.destructorFunction=null):(this.toWireType=Ze,this.destructorFunction=null)}var ot=[],at=e=>{var t=ot[e];return t||(e>=ot.length&&(ot.length=e+1),ot[e]=t=C.get(e)),t},it=(e,t,r)=>e.includes("j")?((e,t,r)=>{var n=o["dynCall_"+e];return r&&r.length?n.apply(null,[t].concat(r)):n.call(null,t)})(e,t,r):at(t).apply(null,r);function st(e,t){var r,n,o,a=(e=pe(e)).includes("j")?(r=e,n=t,o=[],function(){return o.length=0,Object.assign(o,arguments),it(r,n,o)}):at(t);return"function"!=typeof a&&ye(`unknown function pointer with signature ${e}: ${t}`),a}var ut=void 0;function ct(e){var t=Gt(e),r=pe(t);return zt(t),r}function lt(e,t){var r=[],n={};throw t.forEach((function e(t){n[t]||he[t]||(me[t]?me[t].forEach(e):(r.push(t),n[t]=!0))})),new ut(`${e}: `+r.map(ct).join([", "]))}function dt(e,t){for(var r=[],n=0;n<e;n++)r.push($[t+4*n>>2]);return r}function pt(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function ft(e,t){if(!(e instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof e} which is not a function`);var r=Ge(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var n=new r,o=e.apply(n,t);return o instanceof Object?o:n}function ht(e,t,r,n,o,a){var i=t.length;i<2&&ye("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==t[1]&&null!==r,u=!1,c=1;c<t.length;++c)if(null!==t[c]&&void 0===t[c].destructorFunction){u=!0;break}var l="void"!==t[0].name,d="",p="";for(c=0;c<i-2;++c)d+=(0!==c?", ":"")+"arg"+c,p+=(0!==c?", ":"")+"arg"+c+"Wired";var f=`\n        return function ${He(e)}(${d}) {\n        if (arguments.length !== ${i-2}) {\n          throwBindingError('function ${e} called with ${arguments.length} arguments, expected ${i-2} args!');\n        }`;u&&(f+="var destructors = [];\n");var h=u?"destructors":"null",m=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[ye,n,o,pt,t[0],t[1]];s&&(f+="var thisWired = classParam.toWireType("+h+", this);\n");for(c=0;c<i-2;++c)f+="var arg"+c+"Wired = argType"+c+".toWireType("+h+", arg"+c+"); // "+t[c+2].name+"\n",m.push("argType"+c),v.push(t[c+2]);if(s&&(p="thisWired"+(p.length>0?", ":"")+p),f+=(l||a?"var rv = ":"")+"invoker(fn"+(p.length>0?", ":"")+p+");\n",u)f+="runDestructors(destructors);\n";else for(c=s?1:2;c<t.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==t[c].destructorFunction&&(f+=y+"_dtor("+y+"); // "+t[c].name+"\n",m.push(y+"_dtor"),v.push(t[c].destructorFunction))}return l&&(f+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),f+="}\n",m.push(f),ft(Function,m).apply(null,v)}function mt(){this.allocated=[void 0],this.freelist=[]}var vt=new mt;function yt(e){e>=vt.reserved&&0==--vt.get(e).refcount&&vt.free(e)}function gt(){for(var e=0,t=vt.reserved;t<vt.allocated.length;++t)void 0!==vt.allocated[t]&&++e;return e}var wt={toValue:e=>(e||ye("Cannot use deleted val. handle = "+e),vt.get(e).value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return vt.allocate({refcount:1,value:e})}}};function _t(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function Et(e,t){switch(t){case 2:return function(e){return this.fromWireType(P[e>>2])};case 3:return function(e){return this.fromWireType(T[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function bt(e,t,r){switch(t){case 0:return r?function(e){return g[e]}:function(e){return w[e]};case 1:return r?function(e){return _[e>>1]}:function(e){return E[e>>1]};case 2:return r?function(e){return b[e>>2]}:function(e){return $[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var $t="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,Pt=(e,t)=>{for(var r=e,n=r>>1,o=n+t/2;!(n>=o)&&E[n];)++n;if((r=n<<1)-e>32&&$t)return $t.decode(w.subarray(e,r));for(var a="",i=0;!(i>=t/2);++i){var s=_[e+2*i>>1];if(0==s)break;a+=String.fromCharCode(s)}return a},Tt=(e,t,r)=>{if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=t,o=(r-=2)<2*e.length?r/2:e.length,a=0;a<o;++a){var i=e.charCodeAt(a);_[t>>1]=i,t+=2}return _[t>>1]=0,t-n},Ct=e=>2*e.length,kt=(e,t)=>{for(var r=0,n="";!(r>=t/4);){var o=b[e+4*r>>2];if(0==o)break;if(++r,o>=65536){var a=o-65536;n+=String.fromCharCode(55296|a>>10,56320|1023&a)}else n+=String.fromCharCode(o)}return n},St=(e,t,r)=>{if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=t,o=n+r-4,a=0;a<e.length;++a){var i=e.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&e.charCodeAt(++a);if(b[t>>2]=i,(t+=4)+4>o)break}return b[t>>2]=0,t-n},At=e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,t+=4}return t};var Dt={};var Ft=[];var Ot=[];var Mt={},Rt=()=>{if(!Rt.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var t in Mt)void 0===Mt[t]?delete e[t]:e[t]=Mt[t];var r=[];for(var t in e)r.push(`${t}=${e[t]}`);Rt.strings=r}return Rt.strings};var jt,xt,It,Nt=function(e,t,r,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=se.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n},Ut=365,Wt=146;Object.defineProperties(Nt.prototype,{read:{get:function(){return(this.mode&Ut)===Ut},set:function(e){e?this.mode|=Ut:this.mode&=-366}},write:{get:function(){return(this.mode&Wt)===Wt},set:function(e){e?this.mode|=Wt:this.mode&=-147}},isFolder:{get:function(){return se.isDir(this.mode)}},isDevice:{get:function(){return se.isChrdev(this.mode)}}}),se.FSNode=Nt,se.createPreloadedFile=function(e,t,r,n,o,a,i,s,u,c){var l=t?X.resolve(G.join2(e,t)):e;function d(r){function d(r){c&&c(),s||se.createDataFile(e,t,r,n,o,u),a&&a(),j()}(function(e,t,r,n){"undefined"!=typeof Browser&&Browser.init();var o=!1;return ae.forEach((function(a){o||a.canHandle(t)&&(a.handle(e,t,r,n),o=!0)})),o})(r,l,d,(()=>{i&&i(),j()}))||d(r)}R(),"string"==typeof r?oe(r,(e=>d(e)),i):d(r)},se.staticInit(),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);de=e}(),ve=o.BindingError=class extends Error{constructor(e){super(e),this.name="BindingError"}},ge=o.InternalError=class extends Error{constructor(e){super(e),this.name="InternalError"}},ze.prototype.isAliasOf=be,ze.prototype.clone=We,ze.prototype.delete=Be,ze.prototype.isDeleted=Ve,ze.prototype.deleteLater=Le,o.getInheritedInstanceCount=Ae,o.getLiveInheritedInstances=De,o.flushPendingDeletes=Oe,o.setDelayFunction=Re,nt.prototype.getPointee=et,nt.prototype.destructor=tt,nt.prototype.argPackAdvance=8,nt.prototype.readValueFromPointer=Qe,nt.prototype.deleteObject=rt,nt.prototype.fromWireType=Ne,ut=o.UnboundTypeError=(jt=Error,(It=Ge(xt="UnboundTypeError",(function(e){this.name=xt,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(jt.prototype),It.prototype.constructor=It,It.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},It),Object.assign(mt.prototype,{get(e){return this.allocated[e]},has(e){return void 0!==this.allocated[e]},allocate(e){var t=this.freelist.pop()||this.allocated.length;return this.allocated[t]=e,t},free(e){this.allocated[e]=void 0,this.freelist.push(e)}}),vt.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),vt.reserved=vt.allocated.length,o.count_emval_handles=gt;var Bt,Vt={p:function(e,t,r){throw new H(e).init(t,r),e},C:function(e,t,r){ce.varargs=r;try{var n=ce.getStreamFromFD(e);switch(t){case 0:return(o=ce.get())<0?-28:se.createStream(n,o).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var o=ce.get();return n.flags|=o,0;case 5:o=ce.get();return _[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return a=28,b[Ht()>>2]=a,-1}}catch(e){if(void 0===se||"ErrnoError"!==e.name)throw e;return-e.errno}var a},w:function(e,t,r,n){ce.varargs=n;try{t=ce.getStr(t),t=ce.calculateAt(e,t);var o=n?ce.get():0;return se.open(t,r,o).fd}catch(e){if(void 0===se||"ErrnoError"!==e.name)throw e;return-e.errno}},t:function(e,t,r,n,o){},n:function(e,t,r,n,o){var a=le(r);Ee(e,{name:t=pe(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:o},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===r)n=g;else if(2===r)n=_;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);n=b}return this.fromWireType(n[e>>a])},destructorFunction:null})},r:function(e,t,r,n,a,i,s,u,c,l,d,p,f){d=pe(d),i=st(a,i),u&&(u=st(s,u)),l&&(l=st(c,l)),f=st(p,f);var h=He(d);!function(e,t,r){o.hasOwnProperty(e)?((void 0===r||void 0!==o[e].overloadTable&&void 0!==o[e].overloadTable[r])&&ye(`Cannot register public name '${e}' twice`),qe(o,e,e),o.hasOwnProperty(r)&&ye(`Cannot register multiple overloads of a function with the same number of arguments (${r})!`),o[e].overloadTable[r]=t):(o[e]=t,void 0!==r&&(o[e].numArguments=r))}(h,(function(){lt(`Cannot construct ${d} due to unbound types`,[n])})),_e([e,t,r],n?[n]:[],(function(t){var r,a;t=t[0],a=n?(r=t.registeredClass).instancePrototype:ze.prototype;var s=Ge(h,(function(){if(Object.getPrototypeOf(this)!==c)throw new ve("Use 'new' to construct "+d);if(void 0===p.constructor_body)throw new ve(d+" has no accessible constructor");var e=p.constructor_body[arguments.length];if(void 0===e)throw new ve(`Tried to invoke ctor of ${d} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(p.constructor_body).toString()}) parameters instead!`);return e.apply(this,arguments)})),c=Object.create(a,{constructor:{value:s}});s.prototype=c;var p=new Xe(d,s,c,f,r,i,u,l);p.baseClass&&(void 0===p.baseClass.__derivedClasses&&(p.baseClass.__derivedClasses=[]),p.baseClass.__derivedClasses.push(p));var m=new nt(d,p,!0,!1,!1),v=new nt(d+"*",p,!1,!1,!1),y=new nt(d+" const*",p,!1,!0,!1);return Se[e]={pointerType:v,constPointerType:y},function(e,t,r){o.hasOwnProperty(e)||we("Replacing nonexistant public symbol"),void 0!==o[e].overloadTable&&void 0!==r?o[e].overloadTable[r]=t:(o[e]=t,o[e].argCount=r)}(h,s),[m,v,y]}))},q:function(e,t,r,n,o,a){var i=dt(t,r);o=st(n,o),_e([],[e],(function(e){var r=`constructor ${(e=e[0]).name}`;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new ve(`Cannot register multiple constructors with identical number of parameters (${t-1}) for class '${e.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return e.registeredClass.constructor_body[t-1]=()=>{lt(`Cannot construct ${e.name} due to unbound types`,i)},_e([],i,(function(n){return n.splice(1,0,null),e.registeredClass.constructor_body[t-1]=ht(r,n,null,o,a),[]})),[]}))},d:function(e,t,r,n,o,a,i,s,u){var c=dt(r,n);t=pe(t),a=st(o,a),_e([],[e],(function(e){var n=`${(e=e[0]).name}.${t}`;function o(){lt(`Cannot call ${n} due to unbound types`,c)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(t);var l=e.registeredClass.instancePrototype,d=l[t];return void 0===d||void 0===d.overloadTable&&d.className!==e.name&&d.argCount===r-2?(o.argCount=r-2,o.className=e.name,l[t]=o):(qe(l,t,n),l[t].overloadTable[r-2]=o),_e([],c,(function(o){var s=ht(n,o,e,a,i,u);return void 0===l[t].overloadTable?(s.argCount=r-2,l[t]=s):l[t].overloadTable[r-2]=s,[]})),[]}))},D:function(e,t){Ee(e,{name:t=pe(t),fromWireType:function(e){var t=wt.toValue(e);return yt(e),t},toWireType:function(e,t){return wt.toHandle(t)},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:null})},k:function(e,t,r){var n=le(r);Ee(e,{name:t=pe(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:Et(t,n),destructorFunction:null})},c:function(e,t,r,n,o){t=pe(t),-1===o&&(o=4294967295);var a=le(r),i=e=>e;if(0===n){var s=32-8*r;i=e=>e<<s>>>s}var u=t.includes("unsigned");Ee(e,{name:t,fromWireType:i,toWireType:u?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:bt(t,a,0!==n),destructorFunction:null})},b:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function o(e){var t=$,r=t[e>>=2],o=t[e+1];return new n(t.buffer,o,r)}Ee(e,{name:r=pe(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},j:function(e,t){var r="std::string"===(t=pe(t));Ee(e,{name:t,fromWireType:function(e){var t,n=$[e>>2],o=e+4;if(r)for(var a=o,i=0;i<=n;++i){var s=o+i;if(i==n||0==w[s]){var u=ue(a,s-a);void 0===t?t=u:(t+=String.fromCharCode(0),t+=u),a=s+1}}else{var c=new Array(n);for(i=0;i<n;++i)c[i]=String.fromCharCode(w[o+i]);t=c.join("")}return zt(e),t},toWireType:function(e,t){var n;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var o="string"==typeof t;o||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||ye("Cannot pass non-string to std::string"),n=r&&o?Z(t):t.length;var a=Lt(4+n+1),i=a+4;if($[a>>2]=n,r&&o)Q(t,w,i,n+1);else if(o)for(var s=0;s<n;++s){var u=t.charCodeAt(s);u>255&&(zt(i),ye("String has UTF-16 code units that do not fit in 8 bits")),w[i+s]=u}else for(s=0;s<n;++s)w[i+s]=t[s];return null!==e&&e.push(zt,a),a},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:function(e){zt(e)}})},f:function(e,t,r){var n,o,a,i,s;r=pe(r),2===t?(n=Pt,o=Tt,i=Ct,a=()=>E,s=1):4===t&&(n=kt,o=St,i=At,a=()=>$,s=2),Ee(e,{name:r,fromWireType:function(e){for(var r,o=$[e>>2],i=a(),u=e+4,c=0;c<=o;++c){var l=e+4+c*t;if(c==o||0==i[l>>s]){var d=n(u,l-u);void 0===r?r=d:(r+=String.fromCharCode(0),r+=d),u=l+t}}return zt(e),r},toWireType:function(e,n){"string"!=typeof n&&ye(`Cannot pass non-string to C++ string type ${r}`);var a=i(n),u=Lt(4+a+t);return $[u>>2]=a>>s,o(n,u+4,a+t),null!==e&&e.push(zt,u),u},argPackAdvance:8,readValueFromPointer:Qe,destructorFunction:function(e){zt(e)}})},o:function(e,t){Ee(e,{isVoid:!0,name:t=pe(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},g:function(e,t,r,n){var o,a;(e=Ft[e])(t=wt.toValue(t),r=void 0===(a=Dt[o=r])?pe(o):a,null,n)},m:yt,l:function(e,t){var r=function(e,t){for(var r,n,o,a=new Array(e),i=0;i<e;++i)a[i]=(r=$[t+4*i>>2],n="parameter "+i,o=void 0,void 0===(o=he[r])&&ye(n+" has unknown type "+ct(r)),o);return a}(e,t),n=r[0],o=n.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",a=Ot[o];if(void 0!==a)return a;for(var i=["retType"],s=[n],u="",c=0;c<e-1;++c)u+=(0!==c?", ":"")+"arg"+c,i.push("argType"+c),s.push(r[1+c]);var l="return function "+He("methodCaller_"+o)+"(handle, name, destructors, args) {\n",d=0;for(c=0;c<e-1;++c)l+="    var arg"+c+" = argType"+c+".readValueFromPointer(args"+(d?"+"+d:"")+");\n",d+=r[c+1].argPackAdvance;for(l+="    var rv = handle[name]("+u+");\n",c=0;c<e-1;++c)r[c+1].deleteObject&&(l+="    argType"+c+".deleteObject(arg"+c+");\n");n.isVoid||(l+="    return retType.toWireType(destructors, rv);\n"),l+="};\n",i.push(l);var p,f,h=ft(Function,i).apply(null,s);return p=h,f=Ft.length,Ft.push(p),a=f,Ot[o]=a,a},a:()=>{x("")},e:function(){return Date.now()},v:()=>w.length,A:(e,t,r)=>w.copyWithin(e,t,t+r),u:e=>{w.length;x("OOM")},y:(e,t)=>{var r=0;return Rt().forEach((function(n,o){var a=t+r;$[e+4*o>>2]=a,((e,t)=>{for(var r=0;r<e.length;++r)g[0|t++]=e.charCodeAt(r);g[0|t]=0})(n,a),r+=n.length+1})),0},z:(e,t)=>{var r=Rt();$[e>>2]=r.length;var n=0;return r.forEach((function(e){n+=e.length+1})),$[t>>2]=n,0},i:function(e){try{var t=ce.getStreamFromFD(e);return se.close(t),0}catch(e){if(void 0===se||"ErrnoError"!==e.name)throw e;return e.errno}},x:function(e,t){try{var r=ce.getStreamFromFD(e),n=r.tty?2:se.isDir(r.mode)?3:se.isLink(r.mode)?7:4;return g[t|0]=n,_[t+2>>1]=0,W=[0,(U=0,+Math.abs(U)>=1?U>0?+Math.floor(U/4294967296)>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],b[t+8>>2]=W[0],b[t+12>>2]=W[1],W=[0,(U=0,+Math.abs(U)>=1?U>0?+Math.floor(U/4294967296)>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],b[t+16>>2]=W[0],b[t+20>>2]=W[1],0}catch(e){if(void 0===se||"ErrnoError"!==e.name)throw e;return e.errno}},B:function(e,t,r,n){try{var o=((e,t,r,n)=>{for(var o=0,a=0;a<r;a++){var i=$[t>>2],s=$[t+4>>2];t+=8;var u=se.read(e,g,i,s,n);if(u<0)return-1;if(o+=u,u<s)break;void 0!==n&&(n+=u)}return o})(ce.getStreamFromFD(e),t,r);return $[n>>2]=o,0}catch(e){if(void 0===se||"ErrnoError"!==e.name)throw e;return e.errno}},s:function(e,t,r,n,o){var a,i,s=(i=r)+2097152>>>0<4194305-!!(a=t)?(a>>>0)+4294967296*i:NaN;try{if(isNaN(s))return 61;var u=ce.getStreamFromFD(e);return se.llseek(u,s,n),W=[u.position>>>0,(U=u.position,+Math.abs(U)>=1?U>0?+Math.floor(U/4294967296)>>>0:~~+Math.ceil((U-+(~~U>>>0))/4294967296)>>>0:0)],b[o>>2]=W[0],b[o+4>>2]=W[1],u.getdents&&0===s&&0===n&&(u.getdents=null),0}catch(e){if(void 0===se||"ErrnoError"!==e.name)throw e;return e.errno}},h:function(e,t,r,n){try{var o=((e,t,r,n)=>{for(var o=0,a=0;a<r;a++){var i=$[t>>2],s=$[t+4>>2];t+=8;var u=se.write(e,g,i,s,n);if(u<0)return-1;o+=u,void 0!==n&&(n+=u)}return o})(ce.getStreamFromFD(e),t,r);return $[n>>2]=o,0}catch(e){if(void 0===se||"ErrnoError"!==e.name)throw e;return e.errno}}},Lt=(function(){var e,t,r,a,i={a:Vt};function s(e,t){var r,n,a=e.exports;return v=(y=a).E,r=v.buffer,o.HEAP8=g=new Int8Array(r),o.HEAP16=_=new Int16Array(r),o.HEAP32=b=new Int32Array(r),o.HEAPU8=w=new Uint8Array(r),o.HEAPU16=E=new Uint16Array(r),o.HEAPU32=$=new Uint32Array(r),o.HEAPF32=P=new Float32Array(r),o.HEAPF64=T=new Float64Array(r),C=y.H,n=y.F,A.unshift(n),j(),a}if(R(),o.instantiateWasm)try{return o.instantiateWasm(i,s)}catch(e){m("Module.instantiateWasm callback failed with error: "+e),n(e)}(e=f,t=I,r=i,a=function(e){s(e.instance)},e||"function"!=typeof WebAssembly.instantiateStreaming||B(t)||"function"!=typeof fetch?L(t,r,a):fetch(t,{credentials:"same-origin"}).then((e=>WebAssembly.instantiateStreaming(e,r).then(a,(function(e){return m("wasm streaming compile failed: "+e),m("falling back to ArrayBuffer instantiation"),L(t,r,a)}))))).catch(n)}(),e=>(Lt=y.G)(e)),zt=e=>(zt=y.I)(e),Ht=()=>(Ht=y.J)(),Gt=e=>(Gt=y.K)(e),qt=(o.__embind_initialize_bindings=()=>(o.__embind_initialize_bindings=y.L)(),e=>(qt=y.M)(e));o.dynCall_jiji=(e,t,r,n,a)=>(o.dynCall_jiji=y.N)(e,t,r,n,a),o._ff_h264_cabac_tables=67061;function Xt(){function e(){Bt||(Bt=!0,o.calledRun=!0,k||(o.noFSInit||se.init.initialized||se.init(),se.ignorePermissions=!1,te.init(),z(A),r(o),o.onRuntimeInitialized&&o.onRuntimeInitialized(),function(){if(o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)e=o.postRun.shift(),D.unshift(e);var e;z(D)}()))}F>0||(!function(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)e=o.preRun.shift(),S.unshift(e);var e;z(S)}(),F>0||(o.setStatus?(o.setStatus("Running..."),setTimeout((function(){setTimeout((function(){o.setStatus("")}),1),e()}),1)):e()))}if(M=function e(){Bt||Xt(),Bt||(M=e)},o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return Xt(),t.ready}})(),videodec_default=Module,VideoDecoderSoft=class extends VideoDecoderSoftBase{constructor(e){super(videodec_default,(null==e?void 0:e.wasmPath)?fetch(null==e?void 0:e.wasmPath).then((e=>e.arrayBuffer())):void 0,null==e?void 0:e.workerMode,null==e?void 0:e.canvas,null==e?void 0:e.yuvMode)}},Module2=(()=>{var e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(t={}){var r,n,o=t;o.ready=new Promise(((e,t)=>{r=e,n=t}));var a,i=Object.assign({},o),s="object"==typeof window,u="function"==typeof importScripts,c=("object"==typeof process&&"object"==typeof process.versions&&process.versions.node,"");(s||u)&&(u?c=self.location.href:"undefined"!=typeof document&&document.currentScript&&(c=document.currentScript.src),e&&(c=e),c=0!==c.indexOf("blob:")?c.substr(0,c.replace(/[?#].*/,"").lastIndexOf("/")+1):"",u&&(a=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}));var l,d=o.print||console.log.bind(console),p=o.printErr||console.error.bind(console);Object.assign(o,i),i=null,o.arguments&&o.arguments,o.thisProgram&&o.thisProgram,o.quit&&o.quit,o.wasmBinary&&(l=o.wasmBinary);var f,h;o.noExitRuntime;"object"!=typeof WebAssembly&&F("no native wasm support detected");var m,v,y,g,w,_,E,b,$,P=!1;var T=[],C=[],k=[];var S=0,A=null,D=null;function F(e){o.onAbort&&o.onAbort(e),p(e="Aborted("+e+")"),P=!0,e+=". Build with -sASSERTIONS for more info.";var t=new WebAssembly.RuntimeError(e);throw n(t),t}var O,M;function R(e){return e.startsWith("data:application/octet-stream;base64,")}function j(e){if(e==O&&l)return new Uint8Array(l);if(a)return a(e);throw"both async and sync fetching of the wasm failed"}function x(e,t,r){return function(e){return l||!s&&!u||"function"!=typeof fetch?Promise.resolve().then((()=>j(e))):fetch(e,{credentials:"same-origin"}).then((t=>{if(!t.ok)throw"failed to load wasm binary file at '"+e+"'";return t.arrayBuffer()})).catch((()=>j(e)))}(e).then((e=>WebAssembly.instantiate(e,t))).then((e=>e)).then(r,(e=>{p("failed to asynchronously prepare wasm: "+e),F(e)}))}R(O="videodec_simd.wasm")||(M=O,O=o.locateFile?o.locateFile(M,c):c+M);var I=e=>{for(;e.length>0;)e.shift()(o)};function N(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){_[this.ptr+4>>2]=e},this.get_type=function(){return _[this.ptr+4>>2]},this.set_destructor=function(e){_[this.ptr+8>>2]=e},this.get_destructor=function(){return _[this.ptr+8>>2]},this.set_caught=function(e){e=e?1:0,m[this.ptr+12|0]=e},this.get_caught=function(){return 0!=m[this.ptr+12|0]},this.set_rethrown=function(e){e=e?1:0,m[this.ptr+13|0]=e},this.get_rethrown=function(){return 0!=m[this.ptr+13|0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t)},this.set_adjusted_ptr=function(e){_[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return _[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Et(this.get_type()))return _[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}function U(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${e}`)}}var W=void 0;function B(e){for(var t="",r=e;v[r];)t+=W[v[r++]];return t}var V={},L={},z={},H=void 0;function G(e){throw new H(e)}var q=void 0;function X(e){throw new q(e)}function K(e,t,r){function n(t){var n=r(t);n.length!==e.length&&X("Mismatched type converter count");for(var o=0;o<e.length;++o)Y(e[o],n[o])}e.forEach((function(e){z[e]=t}));var o=new Array(t.length),a=[],i=0;t.forEach(((e,t)=>{L.hasOwnProperty(e)?o[t]=L[e]:(a.push(e),V.hasOwnProperty(e)||(V[e]=[]),V[e].push((()=>{o[t]=L[e],++i===a.length&&n(o)})))})),0===a.length&&n(o)}function Y(e,t,r={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(e,t,r={}){var n=t.name;if(e||G(`type "${n}" must have a positive integer typeid pointer`),L.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;G(`Cannot register type '${n}' twice`)}if(L[e]=t,delete z[e],V.hasOwnProperty(e)){var o=V[e];delete V[e],o.forEach((e=>e()))}}(e,t,r)}function J(e){if(!(this instanceof we))return!1;if(!(e instanceof we))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return t===n&&r===o}function Z(e){G(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Q=!1;function ee(e){}function te(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function re(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var n=re(e,t,r.baseClass);return null===n?null:r.downcast(n)}var ne={};function oe(){return Object.keys(le).length}function ae(){var e=[];for(var t in le)le.hasOwnProperty(t)&&e.push(le[t]);return e}var ie=[];function se(){for(;ie.length;){var e=ie.pop();e.$$.deleteScheduled=!1,e.delete()}}var ue=void 0;function ce(e){ue=e,ie.length&&ue&&ue(se)}var le={};function de(e,t){return t=function(e,t){for(void 0===t&&G("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),le[t]}function pe(e,t){return t.ptrType&&t.ptr||X("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!==!!t.smartPtr&&X("Both smartPtrType and smartPtr must be specified"),t.count={value:1},he(Object.create(e,{$$:{value:t}}))}function fe(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=de(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var n=r.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?pe(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):pe(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var a,i=this.registeredClass.getActualType(t),s=ne[i];if(!s)return o.call(this);a=this.isConst?s.constPointerType:s.pointerType;var u=re(t,this.registeredClass,a.registeredClass);return null===u?o.call(this):this.isSmartPointer?pe(a.registeredClass.instancePrototype,{ptrType:a,ptr:u,smartPtrType:this,smartPtr:e}):pe(a.registeredClass.instancePrototype,{ptrType:a,ptr:u})}var he=function(e){return"undefined"==typeof FinalizationRegistry?(he=e=>e,e):(Q=new FinalizationRegistry((e=>{te(e.$$)})),ee=e=>Q.unregister(e),(he=e=>{var t=e.$$;if(!!t.smartPtr){var r={$$:t};Q.register(e,r,e)}return e})(e))};function me(){if(this.$$.ptr||Z(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=he(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function ve(){this.$$.ptr||Z(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&G("Object already scheduled for deletion"),ee(this),te(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function ye(){return!this.$$.ptr}function ge(){return this.$$.ptr||Z(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&G("Object already scheduled for deletion"),ie.push(this),1===ie.length&&ue&&ue(se),this.$$.deleteScheduled=!0,this}function we(){}function _e(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=48&&t<=57?`_${e}`:e}function Ee(e,t){return e=_e(e),{[e]:function(){return t.apply(this,arguments)}}[e]}function be(e,t,r){if(void 0===e[t].overloadTable){var n=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||G(`Function '${r}' called with an invalid number of arguments (${arguments.length}) - expects one of (${e[t].overloadTable})!`),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}}function $e(e,t,r,n,o,a,i,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=o,this.getActualType=a,this.upcast=i,this.downcast=s,this.pureVirtualFunctions=[]}function Pe(e,t,r){for(;t!==r;)t.upcast||G(`Expected null or instance of ${r.name}, got an instance of ${t.name}`),e=t.upcast(e),t=t.baseClass;return e}function Te(e,t){if(null===t)return this.isReference&&G(`null is not a valid ${this.name}`),0;t.$$||G(`Cannot pass "${Ke(t)}" as a ${this.name}`),t.$$.ptr||G(`Cannot pass deleted object as a pointer of type ${this.name}`);var r=t.$$.ptrType.registeredClass;return Pe(t.$$.ptr,r,this.registeredClass)}function Ce(e,t){var r;if(null===t)return this.isReference&&G(`null is not a valid ${this.name}`),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||G(`Cannot pass "${Ke(t)}" as a ${this.name}`),t.$$.ptr||G(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&t.$$.ptrType.isConst&&G(`Cannot convert argument of type ${t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name} to parameter type ${this.name}`);var n=t.$$.ptrType.registeredClass;if(r=Pe(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&G("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:G(`Cannot convert argument of type ${t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var o=t.clone();r=this.rawShare(r,Xe.toHandle((function(){o.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:G("Unsupporting sharing policy")}return r}function ke(e,t){if(null===t)return this.isReference&&G(`null is not a valid ${this.name}`),0;t.$$||G(`Cannot pass "${Ke(t)}" as a ${this.name}`),t.$$.ptr||G(`Cannot pass deleted object as a pointer of type ${this.name}`),t.$$.ptrType.isConst&&G(`Cannot convert argument of type ${t.$$.ptrType.name} to parameter type ${this.name}`);var r=t.$$.ptrType.registeredClass;return Pe(t.$$.ptr,r,this.registeredClass)}function Se(e){return this.fromWireType(w[e>>2])}function Ae(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function De(e){this.rawDestructor&&this.rawDestructor(e)}function Fe(e){null!==e&&e.delete()}function Oe(e,t,r,n,o,a,i,s,u,c,l){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=o,this.pointeeType=a,this.sharingPolicy=i,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=l,o||void 0!==t.baseClass?this.toWireType=Ce:n?(this.toWireType=Te,this.destructorFunction=null):(this.toWireType=ke,this.destructorFunction=null)}var Me=[],Re=e=>{var t=Me[e];return t||(e>=Me.length&&(Me.length=e+1),Me[e]=t=$.get(e)),t},je=(e,t,r)=>e.includes("j")?((e,t,r)=>{var n=o["dynCall_"+e];return r&&r.length?n.apply(null,[t].concat(r)):n.call(null,t)})(e,t,r):Re(t).apply(null,r);function xe(e,t){var r,n,o,a=(e=B(e)).includes("j")?(r=e,n=t,o=[],function(){return o.length=0,Object.assign(o,arguments),je(r,n,o)}):Re(t);return"function"!=typeof a&&G(`unknown function pointer with signature ${e}: ${t}`),a}var Ie=void 0;function Ne(e){var t=_t(e),r=B(t);return gt(t),r}function Ue(e,t){var r=[],n={};throw t.forEach((function e(t){n[t]||L[t]||(z[t]?z[t].forEach(e):(r.push(t),n[t]=!0))})),new Ie(`${e}: `+r.map(Ne).join([", "]))}function We(e,t){for(var r=[],n=0;n<e;n++)r.push(_[t+4*n>>2]);return r}function Be(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function Ve(e,t){if(!(e instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof e} which is not a function`);var r=Ee(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var n=new r,o=e.apply(n,t);return o instanceof Object?o:n}function Le(e,t,r,n,o,a){var i=t.length;i<2&&G("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==t[1]&&null!==r,u=!1,c=1;c<t.length;++c)if(null!==t[c]&&void 0===t[c].destructorFunction){u=!0;break}var l="void"!==t[0].name,d="",p="";for(c=0;c<i-2;++c)d+=(0!==c?", ":"")+"arg"+c,p+=(0!==c?", ":"")+"arg"+c+"Wired";var f=`\n        return function ${_e(e)}(${d}) {\n        if (arguments.length !== ${i-2}) {\n          throwBindingError('function ${e} called with ${arguments.length} arguments, expected ${i-2} args!');\n        }`;u&&(f+="var destructors = [];\n");var h=u?"destructors":"null",m=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[G,n,o,Be,t[0],t[1]];s&&(f+="var thisWired = classParam.toWireType("+h+", this);\n");for(c=0;c<i-2;++c)f+="var arg"+c+"Wired = argType"+c+".toWireType("+h+", arg"+c+"); // "+t[c+2].name+"\n",m.push("argType"+c),v.push(t[c+2]);if(s&&(p="thisWired"+(p.length>0?", ":"")+p),f+=(l||a?"var rv = ":"")+"invoker(fn"+(p.length>0?", ":"")+p+");\n",u)f+="runDestructors(destructors);\n";else for(c=s?1:2;c<t.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==t[c].destructorFunction&&(f+=y+"_dtor("+y+"); // "+t[c].name+"\n",m.push(y+"_dtor"),v.push(t[c].destructorFunction))}return l&&(f+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),f+="}\n",m.push(f),Ve(Function,m).apply(null,v)}function ze(){this.allocated=[void 0],this.freelist=[]}var He=new ze;function Ge(e){e>=He.reserved&&0==--He.get(e).refcount&&He.free(e)}function qe(){for(var e=0,t=He.reserved;t<He.allocated.length;++t)void 0!==He.allocated[t]&&++e;return e}var Xe={toValue:e=>(e||G("Cannot use deleted val. handle = "+e),He.get(e).value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return He.allocate({refcount:1,value:e})}}};function Ke(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function Ye(e,t){switch(t){case 2:return function(e){return this.fromWireType(E[e>>2])};case 3:return function(e){return this.fromWireType(b[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Je(e,t,r){switch(t){case 0:return r?function(e){return m[e]}:function(e){return v[e]};case 1:return r?function(e){return y[e>>1]}:function(e){return g[e>>1]};case 2:return r?function(e){return w[e>>2]}:function(e){return _[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var Ze="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,Qe=(e,t,r)=>{for(var n=t+r,o=t;e[o]&&!(o>=n);)++o;if(o-t>16&&e.buffer&&Ze)return Ze.decode(e.subarray(t,o));for(var a="";t<o;){var i=e[t++];if(128&i){var s=63&e[t++];if(192!=(224&i)){var u=63&e[t++];if((i=224==(240&i)?(15&i)<<12|s<<6|u:(7&i)<<18|s<<12|u<<6|63&e[t++])<65536)a+=String.fromCharCode(i);else{var c=i-65536;a+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else a+=String.fromCharCode((31&i)<<6|s)}else a+=String.fromCharCode(i)}return a},et=(e,t)=>e?Qe(v,e,t):"";var tt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,rt=(e,t)=>{for(var r=e,n=r>>1,o=n+t/2;!(n>=o)&&g[n];)++n;if((r=n<<1)-e>32&&tt)return tt.decode(v.subarray(e,r));for(var a="",i=0;!(i>=t/2);++i){var s=y[e+2*i>>1];if(0==s)break;a+=String.fromCharCode(s)}return a},nt=(e,t,r)=>{if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=t,o=(r-=2)<2*e.length?r/2:e.length,a=0;a<o;++a){var i=e.charCodeAt(a);y[t>>1]=i,t+=2}return y[t>>1]=0,t-n},ot=e=>2*e.length,at=(e,t)=>{for(var r=0,n="";!(r>=t/4);){var o=w[e+4*r>>2];if(0==o)break;if(++r,o>=65536){var a=o-65536;n+=String.fromCharCode(55296|a>>10,56320|1023&a)}else n+=String.fromCharCode(o)}return n},it=(e,t,r)=>{if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=t,o=n+r-4,a=0;a<e.length;++a){var i=e.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&e.charCodeAt(++a);if(w[t>>2]=i,(t+=4)+4>o)break}return w[t>>2]=0,t-n},st=e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,t+=4}return t};var ut={};var ct=[];var lt=[];var dt;dt=()=>performance.now();var pt,ft,ht,mt=[null,[],[]];!function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);W=e}(),H=o.BindingError=class extends Error{constructor(e){super(e),this.name="BindingError"}},q=o.InternalError=class extends Error{constructor(e){super(e),this.name="InternalError"}},we.prototype.isAliasOf=J,we.prototype.clone=me,we.prototype.delete=ve,we.prototype.isDeleted=ye,we.prototype.deleteLater=ge,o.getInheritedInstanceCount=oe,o.getLiveInheritedInstances=ae,o.flushPendingDeletes=se,o.setDelayFunction=ce,Oe.prototype.getPointee=Ae,Oe.prototype.destructor=De,Oe.prototype.argPackAdvance=8,Oe.prototype.readValueFromPointer=Se,Oe.prototype.deleteObject=Fe,Oe.prototype.fromWireType=fe,Ie=o.UnboundTypeError=(pt=Error,(ht=Ee(ft="UnboundTypeError",(function(e){this.name=ft,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(pt.prototype),ht.prototype.constructor=ht,ht.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},ht),Object.assign(ze.prototype,{get(e){return this.allocated[e]},has(e){return void 0!==this.allocated[e]},allocate(e){var t=this.freelist.pop()||this.allocated.length;return this.allocated[t]=e,t},free(e){this.allocated[e]=void 0,this.freelist.push(e)}}),He.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),He.reserved=He.allocated.length,o.count_emval_handles=qe;var vt,yt={o:function(e,t,r){throw new N(e).init(t,r),e},r:function(e,t,r,n,o){},m:function(e,t,r,n,o){var a=U(r);Y(e,{name:t=B(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:o},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===r)n=m;else if(2===r)n=y;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);n=w}return this.fromWireType(n[e>>a])},destructorFunction:null})},q:function(e,t,r,n,a,i,s,u,c,l,d,p,f){d=B(d),i=xe(a,i),u&&(u=xe(s,u)),l&&(l=xe(c,l)),f=xe(p,f);var h=_e(d);!function(e,t,r){o.hasOwnProperty(e)?((void 0===r||void 0!==o[e].overloadTable&&void 0!==o[e].overloadTable[r])&&G(`Cannot register public name '${e}' twice`),be(o,e,e),o.hasOwnProperty(r)&&G(`Cannot register multiple overloads of a function with the same number of arguments (${r})!`),o[e].overloadTable[r]=t):(o[e]=t,void 0!==r&&(o[e].numArguments=r))}(h,(function(){Ue(`Cannot construct ${d} due to unbound types`,[n])})),K([e,t,r],n?[n]:[],(function(t){var r,a;t=t[0],a=n?(r=t.registeredClass).instancePrototype:we.prototype;var s=Ee(h,(function(){if(Object.getPrototypeOf(this)!==c)throw new H("Use 'new' to construct "+d);if(void 0===p.constructor_body)throw new H(d+" has no accessible constructor");var e=p.constructor_body[arguments.length];if(void 0===e)throw new H(`Tried to invoke ctor of ${d} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(p.constructor_body).toString()}) parameters instead!`);return e.apply(this,arguments)})),c=Object.create(a,{constructor:{value:s}});s.prototype=c;var p=new $e(d,s,c,f,r,i,u,l);p.baseClass&&(void 0===p.baseClass.__derivedClasses&&(p.baseClass.__derivedClasses=[]),p.baseClass.__derivedClasses.push(p));var m=new Oe(d,p,!0,!1,!1),v=new Oe(d+"*",p,!1,!1,!1),y=new Oe(d+" const*",p,!1,!0,!1);return ne[e]={pointerType:v,constPointerType:y},function(e,t,r){o.hasOwnProperty(e)||X("Replacing nonexistant public symbol"),void 0!==o[e].overloadTable&&void 0!==r?o[e].overloadTable[r]=t:(o[e]=t,o[e].argCount=r)}(h,s),[m,v,y]}))},p:function(e,t,r,n,o,a){var i=We(t,r);o=xe(n,o),K([],[e],(function(e){var r=`constructor ${(e=e[0]).name}`;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new H(`Cannot register multiple constructors with identical number of parameters (${t-1}) for class '${e.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return e.registeredClass.constructor_body[t-1]=()=>{Ue(`Cannot construct ${e.name} due to unbound types`,i)},K([],i,(function(n){return n.splice(1,0,null),e.registeredClass.constructor_body[t-1]=Le(r,n,null,o,a),[]})),[]}))},d:function(e,t,r,n,o,a,i,s,u){var c=We(r,n);t=B(t),a=xe(o,a),K([],[e],(function(e){var n=`${(e=e[0]).name}.${t}`;function o(){Ue(`Cannot call ${n} due to unbound types`,c)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(t);var l=e.registeredClass.instancePrototype,d=l[t];return void 0===d||void 0===d.overloadTable&&d.className!==e.name&&d.argCount===r-2?(o.argCount=r-2,o.className=e.name,l[t]=o):(be(l,t,n),l[t].overloadTable[r-2]=o),K([],c,(function(o){var s=Le(n,o,e,a,i,u);return void 0===l[t].overloadTable?(s.argCount=r-2,l[t]=s):l[t].overloadTable[r-2]=s,[]})),[]}))},u:function(e,t){Y(e,{name:t=B(t),fromWireType:function(e){var t=Xe.toValue(e);return Ge(e),t},toWireType:function(e,t){return Xe.toHandle(t)},argPackAdvance:8,readValueFromPointer:Se,destructorFunction:null})},k:function(e,t,r){var n=U(r);Y(e,{name:t=B(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:Ye(t,n),destructorFunction:null})},b:function(e,t,r,n,o){t=B(t),-1===o&&(o=4294967295);var a=U(r),i=e=>e;if(0===n){var s=32-8*r;i=e=>e<<s>>>s}var u=t.includes("unsigned");Y(e,{name:t,fromWireType:i,toWireType:u?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:Je(t,a,0!==n),destructorFunction:null})},a:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function o(e){var t=_,r=t[e>>=2],o=t[e+1];return new n(t.buffer,o,r)}Y(e,{name:r=B(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},j:function(e,t){var r="std::string"===(t=B(t));Y(e,{name:t,fromWireType:function(e){var t,n=_[e>>2],o=e+4;if(r)for(var a=o,i=0;i<=n;++i){var s=o+i;if(i==n||0==v[s]){var u=et(a,s-a);void 0===t?t=u:(t+=String.fromCharCode(0),t+=u),a=s+1}}else{var c=new Array(n);for(i=0;i<n;++i)c[i]=String.fromCharCode(v[o+i]);t=c.join("")}return gt(e),t},toWireType:function(e,t){var n;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var o="string"==typeof t;o||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||G("Cannot pass non-string to std::string"),n=r&&o?(e=>{for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n<=127?t++:n<=2047?t+=2:n>=55296&&n<=57343?(t+=4,++r):t+=3}return t})(t):t.length;var a=wt(4+n+1),i=a+4;if(_[a>>2]=n,r&&o)((e,t,r,n)=>{if(!(n>0))return 0;for(var o=r,a=r+n-1,i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++i)),s<=127){if(r>=a)break;t[r++]=s}else if(s<=2047){if(r+1>=a)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=a)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=a)break;t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}t[r]=0})(t,v,i,n+1);else if(o)for(var s=0;s<n;++s){var u=t.charCodeAt(s);u>255&&(gt(i),G("String has UTF-16 code units that do not fit in 8 bits")),v[i+s]=u}else for(s=0;s<n;++s)v[i+s]=t[s];return null!==e&&e.push(gt,a),a},argPackAdvance:8,readValueFromPointer:Se,destructorFunction:function(e){gt(e)}})},g:function(e,t,r){var n,o,a,i,s;r=B(r),2===t?(n=rt,o=nt,i=ot,a=()=>g,s=1):4===t&&(n=at,o=it,i=st,a=()=>_,s=2),Y(e,{name:r,fromWireType:function(e){for(var r,o=_[e>>2],i=a(),u=e+4,c=0;c<=o;++c){var l=e+4+c*t;if(c==o||0==i[l>>s]){var d=n(u,l-u);void 0===r?r=d:(r+=String.fromCharCode(0),r+=d),u=l+t}}return gt(e),r},toWireType:function(e,n){"string"!=typeof n&&G(`Cannot pass non-string to C++ string type ${r}`);var a=i(n),u=wt(4+a+t);return _[u>>2]=a>>s,o(n,u+4,a+t),null!==e&&e.push(gt,u),u},argPackAdvance:8,readValueFromPointer:Se,destructorFunction:function(e){gt(e)}})},n:function(e,t){Y(e,{isVoid:!0,name:t=B(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},e:function(e,t,r,n){var o,a;(e=ct[e])(t=Xe.toValue(t),r=void 0===(a=ut[o=r])?B(o):a,null,n)},l:Ge,h:function(e,t){var r=function(e,t){for(var r,n,o,a=new Array(e),i=0;i<e;++i)a[i]=(r=_[t+4*i>>2],n="parameter "+i,o=void 0,void 0===(o=L[r])&&G(n+" has unknown type "+Ne(r)),o);return a}(e,t),n=r[0],o=n.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",a=lt[o];if(void 0!==a)return a;for(var i=["retType"],s=[n],u="",c=0;c<e-1;++c)u+=(0!==c?", ":"")+"arg"+c,i.push("argType"+c),s.push(r[1+c]);var l="return function "+_e("methodCaller_"+o)+"(handle, name, destructors, args) {\n",d=0;for(c=0;c<e-1;++c)l+="    var arg"+c+" = argType"+c+".readValueFromPointer(args"+(d?"+"+d:"")+");\n",d+=r[c+1].argPackAdvance;for(l+="    var rv = handle[name]("+u+");\n",c=0;c<e-1;++c)r[c+1].deleteObject&&(l+="    argType"+c+".deleteObject(arg"+c+");\n");n.isVoid||(l+="    return retType.toWireType(destructors, rv);\n"),l+="};\n",i.push(l);var p,f,h=Ve(Function,i).apply(null,s);return p=h,f=ct.length,ct.push(p),a=f,lt[o]=a,a},f:()=>{F("")},c:dt,t:(e,t,r)=>v.copyWithin(e,t,t+r),s:e=>{v.length;F("OOM")},i:(e,t,r,n)=>{for(var o,a,i,s=0,u=0;u<r;u++){var c=_[t>>2],l=_[t+4>>2];t+=8;for(var f=0;f<l;f++)o=e,a=v[c+f],i=void 0,i=mt[o],0===a||10===a?((1===o?d:p)(Qe(i,0)),i.length=0):i.push(a);s+=l}return _[n>>2]=s,0}},gt=(function(){var e,t,r,a,i={a:yt};function s(e,t){var r,n,a=e.exports;return f=(h=a).v,r=f.buffer,o.HEAP8=m=new Int8Array(r),o.HEAP16=y=new Int16Array(r),o.HEAP32=w=new Int32Array(r),o.HEAPU8=v=new Uint8Array(r),o.HEAPU16=g=new Uint16Array(r),o.HEAPU32=_=new Uint32Array(r),o.HEAPF32=E=new Float32Array(r),o.HEAPF64=b=new Float64Array(r),$=h.z,n=h.w,C.unshift(n),function(){if(S--,o.monitorRunDependencies&&o.monitorRunDependencies(S),0==S&&(null!==A&&(clearInterval(A),A=null),D)){var e=D;D=null,e()}}(),a}if(S++,o.monitorRunDependencies&&o.monitorRunDependencies(S),o.instantiateWasm)try{return o.instantiateWasm(i,s)}catch(e){p("Module.instantiateWasm callback failed with error: "+e),n(e)}(e=l,t=O,r=i,a=function(e){s(e.instance)},e||"function"!=typeof WebAssembly.instantiateStreaming||R(t)||"function"!=typeof fetch?x(t,r,a):fetch(t,{credentials:"same-origin"}).then((e=>WebAssembly.instantiateStreaming(e,r).then(a,(function(e){return p("wasm streaming compile failed: "+e),p("falling back to ArrayBuffer instantiation"),x(t,r,a)}))))).catch(n)}(),e=>(gt=h.x)(e)),wt=e=>(wt=h.y)(e),_t=e=>(_t=h.A)(e),Et=(o.__embind_initialize_bindings=()=>(o.__embind_initialize_bindings=h.B)(),e=>(Et=h.C)(e));o.dynCall_jiji=(e,t,r,n,a)=>(o.dynCall_jiji=h.D)(e,t,r,n,a);function bt(){function e(){vt||(vt=!0,o.calledRun=!0,P||(I(C),r(o),o.onRuntimeInitialized&&o.onRuntimeInitialized(),function(){if(o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)e=o.postRun.shift(),k.unshift(e);var e;I(k)}()))}S>0||(!function(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)e=o.preRun.shift(),T.unshift(e);var e;I(T)}(),S>0||(o.setStatus?(o.setStatus("Running..."),setTimeout((function(){setTimeout((function(){o.setStatus("")}),1),e()}),1)):e()))}if(D=function e(){vt||bt(),vt||(D=e)},o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return bt(),t.ready}})(),videodec_simd_default=Module2,VideoDecoderSoftSIMD=class extends VideoDecoderSoftBase{constructor(e){super(videodec_simd_default,(null==e?void 0:e.wasmPath)?fetch(e.wasmPath).then((e=>e.arrayBuffer())):void 0,null==e?void 0:e.workerMode,null==e?void 0:e.canvas,null==e?void 0:e.yuvMode)}},BASE_HOST="web.sdk.qcloud.com",BASE_DOC_URL=`https://${BASE_HOST}/trtc/webrtc/v5/doc`,DOC_URL=`${BASE_DOC_URL}/zh-cn/`,LOG_LEVEL=(e=>(e[e.TRACE=0]="TRACE",e[e.DEBUG=1]="DEBUG",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.NONE=5]="NONE",e))(LOG_LEVEL||{}),STORAGE_EXPIRES_TIME=6048e5,NAME={CANVAS:"canvas",AUDIO:"audio",VIDEO:"video",SCREEN:"screen",SMALL:"small",BIG:"big",AUXILIARY:"auxiliary",SMALL_VIDEO:"smallVideo",FACING_MODE_USER:"user",FACING_MODE_ENVIRONMENT:"environment",MUTE:"mute",UNMUTE:"unmute",ENDED:"ended",PLAYING:"playing",PAUSE:"pause",ERROR:"error",LOADEDDATA:"loadeddata",LOADEDMETADATA:"loadedmetadata",AUDIO_INPUT:"audioinput",VIDEO_INPUT:"videoinput",DETAIL:"detail",TEXT:"text",MAIN:"main",BACKUP:"backup",BANNED:"banned",KICK:"kick",USER_TIME_OUT:"user_time_out",ROOM_DISBAND:"room_disband",SEI_MESSAGE:"sei-message",ADD:"add",REMOVE:"remove",REPLACE:"replace",TRACK:"track",SUBSCRIBE:"subscribe",UNSUBSCRIBE:"unsubscribe",TRANSCEIVER_DIRECTION_SENDONLY:"sendonly",TRANSCEIVER_DIRECTION_RECVONLY:"recvonly",ENTER_PICTURE_IN_PICTURE:"enterpictureinpicture",LEAVE_PICTURE_IN_PICTURE:"leavepictureinpicture"},REMOTE_STREAM_TYPE_MAIN=NAME.MAIN,REMOTE_STREAM_TYPE_AUX=NAME.AUXILIARY,STREAM_TYPE_BIG=NAME.BIG,STREAM_TYPE_SMALL=NAME.SMALL,PEERCONNECTION_CONNECTING_TIMEOUT=1e4,LOG_LEVEL_NAME=Object.keys(LOG_LEVEL),MAX_RTT=1e4,glog="undefined"!=typeof window&&"function"==typeof window.glog?window.glog:()=>{};function performanceNow(){return performance&&performance.now?Math.floor(performance.now()):Date.now()}var{toString:toString}=Object.prototype,_TRTCVideoDecoder=class e{constructor(e){this.core=e,__publicField(this,"contextMap",new Map),__publicField(this,"decodeProcessorMap",new WeakMap),this.addKVReportBeforeExitRoom=this.addKVReportBeforeExitRoom.bind(this),this.createDecoder=this.createDecoder.bind(this),this.core.innerEmitter.on("51",this.addKVReportBeforeExitRoom)}getAlias(){return"videoDecoder"}getGroup(e){return(null==e?void 0:e.track)?`${e.track.userId}${e.track.streamType}`:"*"}getName(){return e.Name}getValidateRule(e){return{type:this.core.enums.BASIC_TYPE.Object}}createDecoder(e,t){switch(e){case"wasm":return!1?new VideoDecoderSoftSIMD(__spreadProps(__spreadValues({},t),{wasmPath:`${this.core.assetsPath||"."}/videodec_simd.wasm`})):new VideoDecoderSoft(__spreadProps(__spreadValues({},t),{wasmPath:`${this.core.assetsPath||"."}/videodec.wasm`}));case"webCodecs":return new VideoDecoderHard;case"mse":throw new Error("mse decoder not supported yet");default:throw new Error("Unsupported decoder type")}}start(e){this.decodeProcessorMap.set(e.track,this.decode(e)),this.core.room.videoManager.addDecodeProcessor({processor:({frame:e,track:t})=>{var r;return(null==(r=this.decodeProcessorMap.get(t))?void 0:r({frame:e,track:t}))||e},type:3})}decode(e){const{pipe:t,take:r,subscribe:n}=this.core.rx;return({frame:o,track:a})=>{if(a!==e.track||"empty"===o.type)return o;if(this.contextMap.has(a))return this.contextMap.get(a).decode(o);const i=this.core.room.videoManager.createDecodeContext(__spreadProps(__spreadValues({},e),{createDecoder:this.createDecoder}));return t(i.trackDoneOB,r(1),n((()=>{this.core.clearStarted(this,this.getGroup(e)),this.stop({track:a})}))),this.contextMap.set(a,i),i.decode(o)}}stop(e){if(null==e?void 0:e.track){const t=this.contextMap.get(e.track);t&&(t.close("stop"),this.report(t,e.track),this.contextMap.delete(e.track))}else this.contextMap.forEach(((e,t)=>{e.close("stop"),this.contextMap.delete(t)}));0===this.contextMap.size&&this.core.room.videoManager.removeDecodeProcessor({type:3})}report(e,t){if(e.isReported)return;e.isReported=!0;const r=performanceNow()-e.startPerformanceTime,{renderFreezeTotal:n,dataFreezeTotal:o}=this.getRemoteTrackFreezeDuration(t,e);r&&this.core.kvStatManager.addNumber({key:514850,value:Math.floor(n/r*100),split:1}),r&&this.core.kvStatManager.addNumber({key:514851,value:Math.floor(o/r*100),split:1}),e.inputFrameCount&&this.core.kvStatManager.addNumber({key:514852,value:Math.floor(e.decodedFrameCount/e.inputFrameCount*100),split:1})}addKVReportBeforeExitRoom({room:e}){e===this.core.room&&this.contextMap.forEach(((e,t)=>{this.report(e,t)}))}update(e){const t=this.contextMap.get(e.track);t&&("mock"!==e.type?(t.close("update"),this.contextMap.set(e.track,this.core.room.videoManager.createDecodeContext(__spreadProps(__spreadValues({},e),{createDecoder:this.createDecoder})))):t.mock(this.core.enums.DECODE_FAILED_ERROR_CODE.TEST))}getRemoteTrackFreezeDuration(e,t){const r=this.core.room.badCaseDetector.getRenderFreezeMap().get(`${e.userId}_${e.streamType}`),n=this.core.room.badCaseDetector.getDataFreezeMap().get(`${e.userId}_${e.streamType}`);let o=0;r&&r.freezeTimeline.forEach((({startTime:e,endTime:r})=>{e>t.startPerformanceTime&&(o+=0===r?performanceNow()-e:r-e)}));let a=0;return n&&n.durationItemList.forEach((e=>{e.startTime>t.startPerformanceTime&&(a+=e.getDuration())})),{renderFreezeTotal:o,dataFreezeTotal:a}}destroy(){this.core.innerEmitter.off("51",this.addKVReportBeforeExitRoom)}};__publicField(_TRTCVideoDecoder,"Name","TRTCVideoDecoder");var TRTCVideoDecoder=_TRTCVideoDecoder,index_default=TRTCVideoDecoder;export{index_default as default};export{TRTCVideoDecoder};