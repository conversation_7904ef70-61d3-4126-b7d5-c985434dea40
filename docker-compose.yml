version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: live-streaming-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: live_streaming
      MYSQL_USER: live_user
      MYSQL_PASSWORD: live_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/migrations:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - live-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: live-streaming-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - live-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: live-streaming-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=live_user
      - DB_PASSWORD=live_password
      - DB_NAME=live_streaming
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - TENCENT_SECRET_ID=${TENCENT_SECRET_ID}
      - TENCENT_SECRET_KEY=${TENCENT_SECRET_KEY}
      - LIVE_PUSH_DOMAIN=${LIVE_PUSH_DOMAIN}
      - LIVE_PLAY_DOMAIN=${LIVE_PLAY_DOMAIN}
      - LIVE_PUSH_KEY=${LIVE_PUSH_KEY}
      - LIVE_PLAY_KEY=${LIVE_PLAY_KEY}
    depends_on:
      - mysql
      - redis
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    networks:
      - live-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: live-streaming-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - live-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: live-streaming-nginx
    restart: unless-stopped
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - live-network

volumes:
  mysql_data:
  redis_data:

networks:
  live-network:
    driver: bridge
