# 服务器配置
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
GIN_MODE=debug

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=live_streaming
DB_CHARSET=utf8mb4
# 是否自动执行数据库迁移（开发环境可设为true，生产环境建议设为false）
AUTO_MIGRATE=false

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE_HOURS=24

# 腾讯云配置
TENCENT_SECRET_ID=AKIDYw4vVpsfCSSBdojNkrjMiofFYbOu9CyU
TENCENT_SECRET_KEY=HH0TfaGhsnUv5yby4tRihQsjKfkY7QSE
TENCENT_REGION=ap-beijing

# 腾讯云直播配置
LIVE_PUSH_DOMAIN=your-push-domain.com
LIVE_PLAY_DOMAIN=your-play-domain.com
LIVE_PUSH_KEY=your-push-auth-key
LIVE_PLAY_KEY=your-play-auth-key
LIVE_EXPIRE_TIME=3600

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CORS配置
CORS_ALLOW_ORIGINS=http://localhost:3000,http://localhost:5173
CORS_ALLOW_CREDENTIALS=true

# WebSocket配置
WS_READ_BUFFER_SIZE=1024
WS_WRITE_BUFFER_SIZE=1024
WS_CHECK_ORIGIN=false

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_PATH=uploads/
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,mp4,mov,avi

# 安全配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_DURATION=60
BCRYPT_COST=12

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
