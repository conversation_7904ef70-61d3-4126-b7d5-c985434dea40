// 直播相关类型定义

import type { UserProfile } from './user'

export interface Room {
  id: number
  user_id: number
  title: string
  description: string
  cover: string
  stream_key: string
  push_url: string
  webrtc_push_url: string
  play_url: string
  hls_play_url: string
  flv_play_url: string
  webrtc_play_url: string
  status: number
  is_private: boolean
  password?: string
  category_id: number
  tags: string
  viewer_count: number
  max_viewer_count: number
  like_count: number
  share_count: number
  message_count: number
  gift_count: number
  started_at?: string
  ended_at?: string
  duration: number
  allow_chat: boolean
  allow_gift: boolean
  chat_mode: number
  quality_level: number
  created_at: string
  updated_at: string
  user?: UserProfile
  category?: Category
}

export interface Category {
  id: number
  name: string
  description: string
  icon: string
  sort: number
  is_active: boolean
  room_count: number
  created_at: string
  updated_at: string
}

export interface Message {
  id: number
  room_id: number
  user_id: number
  content: string
  type: number
  is_deleted: boolean
  deleted_by: number
  client_ip: string
  created_at: string
  user?: UserProfile
}

export interface Viewer {
  id: number
  room_id: number
  user_id: number
  joined_at: string
  left_at?: string
  duration: number
  client_ip: string
  user?: UserProfile
}

export interface Record {
  id: number
  room_id: number
  filename: string
  file_url: string
  file_size: number
  duration: number
  format: string
  quality: string
  status: number
  started_at: string
  finished_at?: string
}

export interface StreamInfo {
  stream_name: string
  push_url: string
  webrtc_push_url: string
  play_url: string
  hls_play_url: string
  flv_play_url: string
  webrtc_play_url: string
  expire_time: number
}

export interface StreamStatus {
  stream_name: string
  status: string
  start_time: string
  viewer_count: number
  bandwidth: number
  video_codec: string
  audio_codec: string
  resolution: string
  frame_rate: number
  bitrate: number
}

export interface CreateRoomRequest {
  title: string
  description?: string
  cover?: string
  category_id: number
  tags?: string
  is_private?: boolean
  password?: string
  allow_chat?: boolean
  allow_gift?: boolean
  chat_mode?: number
  quality_level?: number
}

export interface UpdateRoomRequest {
  title?: string
  description?: string
  cover?: string
  category_id?: number
  tags?: string
  is_private?: boolean
  password?: string
  allow_chat?: boolean
  allow_gift?: boolean
  chat_mode?: number
  quality_level?: number
}

export interface RoomListResponse {
  rooms: Room[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface MessageListResponse {
  messages: Message[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 直播间状态枚举
export enum RoomStatus {
  OFFLINE = 0,
  LIVE = 1,
  PAUSED = 2,
  ENDED = 3
}

// 消息类型枚举
export enum MessageType {
  NORMAL = 1,
  SYSTEM = 2,
  GIFT = 3
}

// 聊天模式枚举
export enum ChatMode {
  ALL = 0,
  FOLLOWERS = 1,
  MUTED = 2
}

// 画质等级枚举
export enum QualityLevel {
  SMOOTH = 1,
  SD = 2,
  HD = 3,
  FHD = 4
}

// 录制状态枚举
export enum RecordStatus {
  RECORDING = 0,
  COMPLETED = 1,
  FAILED = 2
}
