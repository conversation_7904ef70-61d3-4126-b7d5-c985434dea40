package repository

import (
	"fmt"
	"log"
	"time"

	"live-streaming-platform/config"
	"live-streaming-platform/internal/model"

	"github.com/go-redis/redis/v8"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database 数据库连接管理
type Database struct {
	DB    *gorm.DB
	Redis *redis.Client
}

// NewDatabase 创建数据库连接
func NewDatabase(cfg *config.Config) (*Database, error) {
	// 连接MySQL
	db, err := connectMySQL(cfg)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL失败: %w", err)
	}

	// 连接Redis
	rdb, err := connectRedis(cfg)
	if err != nil {
		return nil, fmt.Errorf("连接Redis失败: %w", err)
	}

	database := &Database{
		DB:    db,
		Redis: rdb,
	}

	// 根据环境变量决定是否执行数据库迁移
	if cfg.Database.AutoMigrate {
		log.Println("开始执行数据库迁移...")
		if err := database.AutoMigrate(); err != nil {
			return nil, fmt.Errorf("数据库迁移失败: %w", err)
		}
	} else {
		log.Println("跳过数据库迁移（AUTO_MIGRATE=false）")
	}

	return database, nil
}

// connectMySQL 连接MySQL数据库
func connectMySQL(cfg *config.Config) (*gorm.DB, error) {
	dsn := cfg.GetDSN()

	// 配置GORM日志
	var logLevel logger.LogLevel
	switch cfg.Log.Level {
	case "debug":
		logLevel = logger.Info
	case "info":
		logLevel = logger.Warn
	case "warn":
		logLevel = logger.Error
	default:
		logLevel = logger.Silent
	}

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
	})
	if err != nil {
		return nil, err
	}

	// 获取底层sql.DB对象进行连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)           // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)          // 最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Hour) // 连接最大生存时间

	return db, nil
}

// connectRedis 连接Redis
func connectRedis(cfg *config.Config) (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.GetRedisAddr(),
		Username: cfg.Redis.Username,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
		PoolSize: 10,
	})

	// 测试连接
	ctx := rdb.Context()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return nil, err
	}

	return rdb, nil
}

// AutoMigrate 自动迁移数据库表
func (d *Database) AutoMigrate() error {
	// 首先清理可能存在的问题数据
	if err := d.cleanupExistingData(); err != nil {
		log.Printf("清理现有数据时出现警告: %v", err)
	}

	// 定义需要迁移的模型
	models := []interface{}{
		&model.User{},
		&model.Follow{},
		&model.Room{},
		&model.Category{},
		&model.Message{},
		&model.Viewer{},
		&model.Record{},
	}

	// 执行迁移
	for _, model := range models {
		if err := d.DB.AutoMigrate(model); err != nil {
			return fmt.Errorf("迁移模型 %T 失败: %w", model, err)
		}
	}

	// 创建默认数据
	if err := d.createDefaultData(); err != nil {
		return fmt.Errorf("创建默认数据失败: %w", err)
	}

	log.Println("数据库迁移完成")
	return nil
}

// createDefaultData 创建默认数据
func (d *Database) createDefaultData() error {
	// 创建默认分类
	categories := []model.Category{
		{Name: "游戏", Description: "游戏直播", Icon: "game", Sort: 1},
		{Name: "娱乐", Description: "娱乐直播", Icon: "entertainment", Sort: 2},
		{Name: "教育", Description: "教育直播", Icon: "education", Sort: 3},
		{Name: "科技", Description: "科技直播", Icon: "tech", Sort: 4},
		{Name: "生活", Description: "生活直播", Icon: "life", Sort: 5},
		{Name: "其他", Description: "其他分类", Icon: "other", Sort: 99},
	}

	for _, category := range categories {
		var count int64
		d.DB.Model(&model.Category{}).Where("name = ?", category.Name).Count(&count)
		if count == 0 {
			if err := d.DB.Create(&category).Error; err != nil {
				return fmt.Errorf("创建默认分类失败: %w", err)
			}
		}
	}

	return nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	// 关闭Redis连接
	if err := d.Redis.Close(); err != nil {
		log.Printf("关闭Redis连接失败: %v", err)
	}

	// 关闭MySQL连接
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}

	if err := sqlDB.Close(); err != nil {
		return fmt.Errorf("关闭MySQL连接失败: %w", err)
	}

	return nil
}

// Transaction 执行事务
func (d *Database) Transaction(fn func(*gorm.DB) error) error {
	return d.DB.Transaction(fn)
}

// GetDB 获取数据库连接
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}

// GetRedis 获取Redis连接
func (d *Database) GetRedis() *redis.Client {
	return d.Redis
}

// Health 健康检查
func (d *Database) Health() error {
	// 检查MySQL连接
	sqlDB, err := d.DB.DB()
	if err != nil {
		return fmt.Errorf("获取MySQL连接失败: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("MySQL连接异常: %w", err)
	}

	// 检查Redis连接
	ctx := d.Redis.Context()
	if _, err := d.Redis.Ping(ctx).Result(); err != nil {
		return fmt.Errorf("Redis连接异常: %w", err)
	}

	return nil
}

// cleanupExistingData 清理可能存在的问题数据
func (d *Database) cleanupExistingData() error {
	// 检查是否存在users表
	var tableExists bool
	err := d.DB.Raw("SELECT COUNT(*) > 0 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'users'").Scan(&tableExists).Error
	if err != nil {
		return err
	}

	if !tableExists {
		return nil // 表不存在，无需清理
	}

	// 清理空用户名的记录
	result := d.DB.Exec("DELETE FROM users WHERE username = '' OR username IS NULL")
	if result.Error != nil {
		log.Printf("清理空用户名记录失败: %v", result.Error)
	} else if result.RowsAffected > 0 {
		log.Printf("清理了 %d 条空用户名记录", result.RowsAffected)
	}

	// 清理重复的用户名记录（保留ID最小的）
	result = d.DB.Exec(`
		DELETE u1 FROM users u1
		INNER JOIN users u2
		WHERE u1.id > u2.id AND u1.username = u2.username AND u1.username != ''
	`)
	if result.Error != nil {
		log.Printf("清理重复用户名记录失败: %v", result.Error)
	} else if result.RowsAffected > 0 {
		log.Printf("清理了 %d 条重复用户名记录", result.RowsAffected)
	}

	return nil
}
