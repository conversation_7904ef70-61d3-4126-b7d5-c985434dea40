// 用户相关类型定义

export interface User {
  id: number
  username: string
  email: string
  nickname: string
  avatar: string
  gender: number
  birthday?: string
  bio: string
  status: number
  is_verified: boolean
  role: string
  level: number
  follow_count: number
  follower_count: number
  stream_count: number
  total_watch_time: number
  created_at: string
  last_login_at?: string
}

export interface UserProfile {
  id: number
  username: string
  nickname: string
  avatar: string
  gender: number
  bio: string
  is_verified: boolean
  role: string
  level: number
  follow_count: number
  follower_count: number
  stream_count: number
  created_at: string
  last_login_at?: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  nickname?: string
}

export interface UpdateProfileRequest {
  nickname?: string
  avatar?: string
  gender?: number
  birthday?: string
  bio?: string
}

export interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

export interface AuthResponse {
  token: string
  expires_at: string
  user: UserProfile
}

export interface FollowListResponse {
  users: UserProfile[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface UserListResponse {
  users: UserProfile[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 用户状态枚举
export enum UserStatus {
  DISABLED = 0,
  ACTIVE = 1,
  PENDING = 2
}

// 用户角色枚举
export enum UserRole {
  USER = 'user',
  STREAMER = 'streamer',
  ADMIN = 'admin'
}

// 性别枚举
export enum Gender {
  UNKNOWN = 0,
  MALE = 1,
  FEMALE = 2
}
