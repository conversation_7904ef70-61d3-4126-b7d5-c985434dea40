!function(A,g){"object"==typeof exports&&"undefined"!=typeof module?module.exports=g():"function"==typeof define&&define.amd?define(g):(A="undefined"!=typeof globalThis?globalThis:A||self).BasicBeauty=g()}(this,(function(){"use strict";function A(A,g){(null==g||g>A.length)&&(g=A.length);for(var I=0,C=Array(g);I<g;I++)C[I]=A[I];return C}function g(A,g,I,C,B,Q,t){try{var E=A[Q](t),e=E.value}catch(A){return void I(A)}E.done?g(e):Promise.resolve(e).then(C,B)}function I(A){return function(){var I=this,C=arguments;return new Promise((function(B,Q){var t=A.apply(I,C);function E(A){g(t,B,Q,E,e,"next",A)}function e(A){g(t,B,Q,E,e,"throw",A)}E(void 0)}))}}function C(A,g,I){return g=t(g),function(A,g){if(g&&("object"==typeof g||"function"==typeof g))return g;if(void 0!==g)throw new TypeError("Derived constructors may only return object or undefined");return function(A){if(void 0===A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A}(A)}(A,e()?Reflect.construct(g,I||[],t(A).constructor):g.apply(A,I))}function B(A,g){if(!(A instanceof g))throw new TypeError("Cannot call a class as a function")}function Q(A,g,I){return g&&function(A,g){for(var I=0;I<g.length;I++){var C=g[I];C.enumerable=C.enumerable||!1,C.configurable=!0,"value"in C&&(C.writable=!0),Object.defineProperty(A,n(C.key),C)}}(A.prototype,g),Object.defineProperty(A,"prototype",{writable:!1}),A}function t(A){return t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(A){return A.__proto__||Object.getPrototypeOf(A)},t(A)}function E(A,g){if("function"!=typeof g&&null!==g)throw new TypeError("Super expression must either be null or a function");A.prototype=Object.create(g&&g.prototype,{constructor:{value:A,writable:!0,configurable:!0}}),Object.defineProperty(A,"prototype",{writable:!1}),g&&i(A,g)}function e(){try{var A=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(A){}return(e=function(){return!!A})()}function r(){r=function(){return g};var A,g={},I=Object.prototype,C=I.hasOwnProperty,B=Object.defineProperty||function(A,g,I){A[g]=I.value},Q="function"==typeof Symbol?Symbol:{},t=Q.iterator||"@@iterator",E=Q.asyncIterator||"@@asyncIterator",e=Q.toStringTag||"@@toStringTag";function i(A,g,I){return Object.defineProperty(A,g,{value:I,enumerable:!0,configurable:!0,writable:!0}),A[g]}try{i({},"")}catch(A){i=function(A,g,I){return A[g]=I}}function o(A,g,I,C){var Q=g&&g.prototype instanceof D?g:D,t=Object.create(Q.prototype),E=new R(C||[]);return B(t,"_invoke",{value:F(A,I,E)}),t}function n(A,g,I){try{return{type:"normal",arg:A.call(g,I)}}catch(A){return{type:"throw",arg:A}}}g.wrap=o;var a="suspendedStart",c="suspendedYield",s="executing",u="completed",y={};function D(){}function h(){}function w(){}var f={};i(f,t,(function(){return this}));var G=Object.getPrototypeOf,l=G&&G(G(S([])));l&&l!==I&&C.call(l,t)&&(f=l);var N=w.prototype=D.prototype=Object.create(f);function d(A){["next","throw","return"].forEach((function(g){i(A,g,(function(A){return this._invoke(g,A)}))}))}function p(A,g){function I(B,Q,t,E){var e=n(A[B],A,Q);if("throw"!==e.type){var r=e.arg,i=r.value;return i&&"object"==typeof i&&C.call(i,"__await")?g.resolve(i.__await).then((function(A){I("next",A,t,E)}),(function(A){I("throw",A,t,E)})):g.resolve(i).then((function(A){r.value=A,t(r)}),(function(A){return I("throw",A,t,E)}))}E(e.arg)}var Q;B(this,"_invoke",{value:function(A,C){function B(){return new g((function(g,B){I(A,C,g,B)}))}return Q=Q?Q.then(B,B):B()}})}function F(g,I,C){var B=a;return function(Q,t){if(B===s)throw Error("Generator is already running");if(B===u){if("throw"===Q)throw t;return{value:A,done:!0}}for(C.method=Q,C.arg=t;;){var E=C.delegate;if(E){var e=Y(E,C);if(e){if(e===y)continue;return e}}if("next"===C.method)C.sent=C._sent=C.arg;else if("throw"===C.method){if(B===a)throw B=u,C.arg;C.dispatchException(C.arg)}else"return"===C.method&&C.abrupt("return",C.arg);B=s;var r=n(g,I,C);if("normal"===r.type){if(B=C.done?u:c,r.arg===y)continue;return{value:r.arg,done:C.done}}"throw"===r.type&&(B=u,C.method="throw",C.arg=r.arg)}}}function Y(g,I){var C=I.method,B=g.iterator[C];if(B===A)return I.delegate=null,"throw"===C&&g.iterator.return&&(I.method="return",I.arg=A,Y(g,I),"throw"===I.method)||"return"!==C&&(I.method="throw",I.arg=new TypeError("The iterator does not provide a '"+C+"' method")),y;var Q=n(B,g.iterator,I.arg);if("throw"===Q.type)return I.method="throw",I.arg=Q.arg,I.delegate=null,y;var t=Q.arg;return t?t.done?(I[g.resultName]=t.value,I.next=g.nextLoc,"return"!==I.method&&(I.method="next",I.arg=A),I.delegate=null,y):t:(I.method="throw",I.arg=new TypeError("iterator result is not an object"),I.delegate=null,y)}function k(A){var g={tryLoc:A[0]};1 in A&&(g.catchLoc=A[1]),2 in A&&(g.finallyLoc=A[2],g.afterLoc=A[3]),this.tryEntries.push(g)}function M(A){var g=A.completion||{};g.type="normal",delete g.arg,A.completion=g}function R(A){this.tryEntries=[{tryLoc:"root"}],A.forEach(k,this),this.reset(!0)}function S(g){if(g||""===g){var I=g[t];if(I)return I.call(g);if("function"==typeof g.next)return g;if(!isNaN(g.length)){var B=-1,Q=function I(){for(;++B<g.length;)if(C.call(g,B))return I.value=g[B],I.done=!1,I;return I.value=A,I.done=!0,I};return Q.next=Q}}throw new TypeError(typeof g+" is not iterable")}return h.prototype=w,B(N,"constructor",{value:w,configurable:!0}),B(w,"constructor",{value:h,configurable:!0}),h.displayName=i(w,e,"GeneratorFunction"),g.isGeneratorFunction=function(A){var g="function"==typeof A&&A.constructor;return!!g&&(g===h||"GeneratorFunction"===(g.displayName||g.name))},g.mark=function(A){return Object.setPrototypeOf?Object.setPrototypeOf(A,w):(A.__proto__=w,i(A,e,"GeneratorFunction")),A.prototype=Object.create(N),A},g.awrap=function(A){return{__await:A}},d(p.prototype),i(p.prototype,E,(function(){return this})),g.AsyncIterator=p,g.async=function(A,I,C,B,Q){void 0===Q&&(Q=Promise);var t=new p(o(A,I,C,B),Q);return g.isGeneratorFunction(I)?t:t.next().then((function(A){return A.done?A.value:t.next()}))},d(N),i(N,e,"Generator"),i(N,t,(function(){return this})),i(N,"toString",(function(){return"[object Generator]"})),g.keys=function(A){var g=Object(A),I=[];for(var C in g)I.push(C);return I.reverse(),function A(){for(;I.length;){var C=I.pop();if(C in g)return A.value=C,A.done=!1,A}return A.done=!0,A}},g.values=S,R.prototype={constructor:R,reset:function(g){if(this.prev=0,this.next=0,this.sent=this._sent=A,this.done=!1,this.delegate=null,this.method="next",this.arg=A,this.tryEntries.forEach(M),!g)for(var I in this)"t"===I.charAt(0)&&C.call(this,I)&&!isNaN(+I.slice(1))&&(this[I]=A)},stop:function(){this.done=!0;var A=this.tryEntries[0].completion;if("throw"===A.type)throw A.arg;return this.rval},dispatchException:function(g){if(this.done)throw g;var I=this;function B(C,B){return E.type="throw",E.arg=g,I.next=C,B&&(I.method="next",I.arg=A),!!B}for(var Q=this.tryEntries.length-1;Q>=0;--Q){var t=this.tryEntries[Q],E=t.completion;if("root"===t.tryLoc)return B("end");if(t.tryLoc<=this.prev){var e=C.call(t,"catchLoc"),r=C.call(t,"finallyLoc");if(e&&r){if(this.prev<t.catchLoc)return B(t.catchLoc,!0);if(this.prev<t.finallyLoc)return B(t.finallyLoc)}else if(e){if(this.prev<t.catchLoc)return B(t.catchLoc,!0)}else{if(!r)throw Error("try statement without catch or finally");if(this.prev<t.finallyLoc)return B(t.finallyLoc)}}}},abrupt:function(A,g){for(var I=this.tryEntries.length-1;I>=0;--I){var B=this.tryEntries[I];if(B.tryLoc<=this.prev&&C.call(B,"finallyLoc")&&this.prev<B.finallyLoc){var Q=B;break}}Q&&("break"===A||"continue"===A)&&Q.tryLoc<=g&&g<=Q.finallyLoc&&(Q=null);var t=Q?Q.completion:{};return t.type=A,t.arg=g,Q?(this.method="next",this.next=Q.finallyLoc,y):this.complete(t)},complete:function(A,g){if("throw"===A.type)throw A.arg;return"break"===A.type||"continue"===A.type?this.next=A.arg:"return"===A.type?(this.rval=this.arg=A.arg,this.method="return",this.next="end"):"normal"===A.type&&g&&(this.next=g),y},finish:function(A){for(var g=this.tryEntries.length-1;g>=0;--g){var I=this.tryEntries[g];if(I.finallyLoc===A)return this.complete(I.completion,I.afterLoc),M(I),y}},catch:function(A){for(var g=this.tryEntries.length-1;g>=0;--g){var I=this.tryEntries[g];if(I.tryLoc===A){var C=I.completion;if("throw"===C.type){var B=C.arg;M(I)}return B}}throw Error("illegal catch attempt")},delegateYield:function(g,I,C){return this.delegate={iterator:S(g),resultName:I,nextLoc:C},"next"===this.method&&(this.arg=A),y}},g}function i(A,g){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(A,g){return A.__proto__=g,A},i(A,g)}function o(g){return function(g){if(Array.isArray(g))return A(g)}(g)||function(A){if("undefined"!=typeof Symbol&&null!=A[Symbol.iterator]||null!=A["@@iterator"])return Array.from(A)}(g)||function(g,I){if(g){if("string"==typeof g)return A(g,I);var C={}.toString.call(g).slice(8,-1);return"Object"===C&&g.constructor&&(C=g.constructor.name),"Map"===C||"Set"===C?Array.from(g):"Arguments"===C||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(C)?A(g,I):void 0}}(g)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(A){var g=function(A,g){if("object"!=typeof A||!A)return A;var I=A[Symbol.toPrimitive];if(void 0!==I){var C=I.call(A,g);if("object"!=typeof C)return C;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(A)}(A,"string");return"symbol"==typeof g?g:g+""}function a(A){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},a(A)}function c(A){var g="function"==typeof Map?new Map:void 0;return c=function(A){if(null===A||!function(A){try{return-1!==Function.toString.call(A).indexOf("[native code]")}catch(g){return"function"==typeof A}}(A))return A;if("function"!=typeof A)throw new TypeError("Super expression must either be null or a function");if(void 0!==g){if(g.has(A))return g.get(A);g.set(A,I)}function I(){return function(A,g,I){if(e())return Reflect.construct.apply(null,arguments);var C=[null];C.push.apply(C,g);var B=new(A.bind.apply(A,C));return I&&i(B,I.prototype),B}(A,arguments,t(this).constructor)}return I.prototype=Object.create(A.prototype,{constructor:{value:I,enumerable:!1,writable:!0,configurable:!0}}),i(I,A)},c(A)}var s=Object.defineProperty,u=function(A,g,I){return function(A,g,I){return g in A?s(A,g,{enumerable:!0,configurable:!0,writable:!0,value:I}):A[g]=I}(A,"symbol"!==a(g)?g+"":g,I)};function y(A){return{name:"BasicBeautyOptions",type:"object",required:!0,allowEmpty:!1,properties:{beauty:{required:!1,type:"number"},brightness:{required:!1,type:"number"},ruddy:{required:!1,type:"number"}},validate:function(g,I,C,B){var Q=A.errorModule,t=Q.RtcError,E=Q.ErrorCode,e=Q.ErrorCodeDictionary;if(A.utils.isOverseaSdkAppId(g.sdkAppId))throw new t({code:E.INVALID_OPERATION,extraCode:e.INVALID_OPERATION,message:"This feature is not yet available in your country or region. If you have any questions, you can go to the community for consultation: https://zhiliao.qq.com/s/cWSPGIIM62CC/c3TPGIIM62CQ"})}}}var D,h,w=(h="undefined"!=typeof document?null==(D=document.currentScript)?void 0:D.src:void 0,function(){var A,g,I=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Object.assign({},I),e=new Promise((function(I,C){A=I,g=C})),r=Object.assign({},t),i="";"undefined"!=typeof document&&document.currentScript&&(i=document.currentScript.src),h&&(i=h),i=i.startsWith("blob:")?"":i.substr(0,i.replace(/[?#].*/,"").lastIndexOf("/")+1);var n,s,u=t.print||console.log.bind(console),y=t.printErr||console.error.bind(console);function D(A){if(J(A))return function(A){for(var g=atob(A),I=new Uint8Array(g.length),C=0;C<g.length;++C)I[C]=g.charCodeAt(C);return I}(A.slice(U.length))}Object.assign(t,r),r=null,t.arguments&&t.arguments,t.thisProgram&&t.thisProgram,t.quit&&t.quit,t.wasmBinary&&(n=t.wasmBinary);var w,f,G,l,N,d,p,F,Y=!1,k=[],M=[],R=[],S=0,L=null;function b(A){var I;null==(I=t.onAbort)||I.call(t,A),y(A="Aborted("+A+")"),Y=!0,A+=". Build with -sASSERTIONS for more info.";var C=new WebAssembly.RuntimeError(A);throw g(C),C}var v,U="data:application/octet-stream;base64,",J=function(A){return A.startsWith(U)};function m(A){return Promise.resolve().then((function(){return function(A){if(A==v&&n)return new Uint8Array(n);var g=D(A);if(g)return g;throw"both async and sync fetching of the wasm failed"}(A)}))}function H(A,g,I,C){return function(A,g,I){return m(A).then((function(A){return WebAssembly.instantiate(A,g)})).then(I,(function(A){y("failed to asynchronously prepare wasm: ".concat(A)),b(A)}))}(g,I,C)}var K=function(A){for(;A.length>0;)A.shift()(t)};t.noExitRuntime;var Z,j,x,T=function(){return Q((function A(g){B(this,A),this.excPtr=g,this.ptr=g-24}),[{key:"set_type",value:function(A){d[this.ptr+4>>2]=A}},{key:"get_type",value:function(){return d[this.ptr+4>>2]}},{key:"set_destructor",value:function(A){d[this.ptr+8>>2]=A}},{key:"get_destructor",value:function(){return d[this.ptr+8>>2]}},{key:"set_caught",value:function(A){A=A?1:0,w[this.ptr+12]=A}},{key:"get_caught",value:function(){return 0!=w[this.ptr+12]}},{key:"set_rethrown",value:function(A){A=A?1:0,w[this.ptr+13]=A}},{key:"get_rethrown",value:function(){return 0!=w[this.ptr+13]}},{key:"init",value:function(A,g){this.set_adjusted_ptr(0),this.set_type(A),this.set_destructor(g)}},{key:"set_adjusted_ptr",value:function(A){d[this.ptr+16>>2]=A}},{key:"get_adjusted_ptr",value:function(){return d[this.ptr+16>>2]}},{key:"get_exception_ptr",value:function(){if(yg(this.get_type()))return d[this.excPtr>>2];var A=this.get_adjusted_ptr();return 0!==A?A:this.excPtr}}])}(),P=function(A){for(var g="",I=A;f[I];)g+=Z[f[I++]];return g},W={},O={},z={},q=function(A){throw new j(A)},V=function(A){throw new x(A)},X=function(A,g,I){function C(g){var C=I(g);C.length!==A.length&&V("Mismatched type converter count");for(var B=0;B<A.length;++B)$(A[B],C[B])}A.forEach((function(A){z[A]=g}));var B=new Array(g.length),Q=[],t=0;g.forEach((function(A,g){O.hasOwnProperty(A)?B[g]=O[A]:(Q.push(A),W.hasOwnProperty(A)||(W[A]=[]),W[A].push((function(){B[g]=O[A],++t===Q.length&&C(B)})))})),0===Q.length&&C(B)};function $(A,g){var I=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in g))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(A,g){var I=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},C=g.name;if(A||q('type "'.concat(C,'" must have a positive integer typeid pointer')),O.hasOwnProperty(A)){if(I.ignoreDuplicateRegistrations)return;q("Cannot register type '".concat(C,"' twice"))}if(O[A]=g,delete z[A],W.hasOwnProperty(A)){var B=W[A];delete W[A],B.forEach((function(A){return A()}))}}(A,g,I)}var _,AA=function(A){q(A.$$.ptrType.registeredClass.name+" instance already deleted")},gA=!1,IA=function(A){},CA=function(A){A.count.value-=1,0===A.count.value&&function(A){A.smartPtr?A.smartPtrType.rawDestructor(A.smartPtr):A.ptrType.registeredClass.rawDestructor(A.ptr)}(A)},BA=function(A,g,I){if(g===I)return A;if(void 0===I.baseClass)return null;var C=BA(A,g,I.baseClass);return null===C?null:I.downcast(C)},QA={},tA=function(){return Object.keys(oA).length},EA=function(){var A=[];for(var g in oA)oA.hasOwnProperty(g)&&A.push(oA[g]);return A},eA=[],rA=function(){for(;eA.length;){var A=eA.pop();A.$$.deleteScheduled=!1,A.delete()}},iA=function(A){_=A,eA.length&&_&&_(rA)},oA={},nA=function(A,g){return g=function(A,g){for(void 0===g&&q("ptr should not be undefined");A.baseClass;)g=A.upcast(g),A=A.baseClass;return g}(A,g),oA[g]},aA=function(A,g){return g.ptrType&&g.ptr||V("makeClassHandle requires ptr and ptrType"),!!g.smartPtrType!=!!g.smartPtr&&V("Both smartPtrType and smartPtr must be specified"),g.count={value:1},sA(Object.create(A,{$$:{value:g,writable:!0}}))};function cA(A){var g=this.getPointee(A);if(!g)return this.destructor(A),null;var I=nA(this.registeredClass,g);if(void 0!==I){if(0===I.$$.count.value)return I.$$.ptr=g,I.$$.smartPtr=A,I.clone();var C=I.clone();return this.destructor(A),C}function B(){return this.isSmartPointer?aA(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:g,smartPtrType:this,smartPtr:A}):aA(this.registeredClass.instancePrototype,{ptrType:this,ptr:A})}var Q,t=this.registeredClass.getActualType(g),E=QA[t];if(!E)return B.call(this);Q=this.isConst?E.constPointerType:E.pointerType;var e=BA(g,this.registeredClass,Q.registeredClass);return null===e?B.call(this):this.isSmartPointer?aA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:e,smartPtrType:this,smartPtr:A}):aA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:e})}var sA=function(A){return"undefined"==typeof FinalizationRegistry?(sA=function(A){return A},A):(gA=new FinalizationRegistry((function(A){CA(A.$$)})),IA=function(A){return gA.unregister(A)},(sA=function(A){var g=A.$$;if(g.smartPtr){var I={$$:g};gA.register(A,I,A)}return A})(A))};function uA(){}var yA=function(A,g){return Object.defineProperty(g,"name",{value:A})},DA=function(A,g,I){if(void 0===A[g].overloadTable){var C=A[g];A[g]=function(){for(var C=arguments.length,B=new Array(C),Q=0;Q<C;Q++)B[Q]=arguments[Q];return A[g].overloadTable.hasOwnProperty(B.length)||q("Function '".concat(I,"' called with an invalid number of arguments (").concat(B.length,") - expects one of (").concat(A[g].overloadTable,")!")),A[g].overloadTable[B.length].apply(this,B)},A[g].overloadTable=[],A[g].overloadTable[C.argCount]=C}};function hA(A,g,I,C,B,Q,t,E){this.name=A,this.constructor=g,this.instancePrototype=I,this.rawDestructor=C,this.baseClass=B,this.getActualType=Q,this.upcast=t,this.downcast=E,this.pureVirtualFunctions=[]}var wA=function(A,g,I){for(;g!==I;)g.upcast||q("Expected null or instance of ".concat(I.name,", got an instance of ").concat(g.name)),A=g.upcast(A),g=g.baseClass;return A};function fA(A,g){if(null===g)return this.isReference&&q("null is not a valid ".concat(this.name)),0;g.$$||q('Cannot pass "'.concat(OA(g),'" as a ').concat(this.name)),g.$$.ptr||q("Cannot pass deleted object as a pointer of type ".concat(this.name));var I=g.$$.ptrType.registeredClass;return wA(g.$$.ptr,I,this.registeredClass)}function GA(A,g){var I;if(null===g)return this.isReference&&q("null is not a valid ".concat(this.name)),this.isSmartPointer?(I=this.rawConstructor(),null!==A&&A.push(this.rawDestructor,I),I):0;g&&g.$$||q('Cannot pass "'.concat(OA(g),'" as a ').concat(this.name)),g.$$.ptr||q("Cannot pass deleted object as a pointer of type ".concat(this.name)),!this.isConst&&g.$$.ptrType.isConst&&q("Cannot convert argument of type ".concat(g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name," to parameter type ").concat(this.name));var C=g.$$.ptrType.registeredClass;if(I=wA(g.$$.ptr,C,this.registeredClass),this.isSmartPointer)switch(void 0===g.$$.smartPtr&&q("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:g.$$.smartPtrType===this?I=g.$$.smartPtr:q("Cannot convert argument of type ".concat(g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name," to parameter type ").concat(this.name));break;case 1:I=g.$$.smartPtr;break;case 2:if(g.$$.smartPtrType===this)I=g.$$.smartPtr;else{var B=g.clone();I=this.rawShare(I,PA.toHandle((function(){return B.delete()}))),null!==A&&A.push(this.rawDestructor,I)}break;default:q("Unsupporting sharing policy")}return I}function lA(A,g){if(null===g)return this.isReference&&q("null is not a valid ".concat(this.name)),0;g.$$||q('Cannot pass "'.concat(OA(g),'" as a ').concat(this.name)),g.$$.ptr||q("Cannot pass deleted object as a pointer of type ".concat(this.name)),g.$$.ptrType.isConst&&q("Cannot convert argument of type ".concat(g.$$.ptrType.name," to parameter type ").concat(this.name));var I=g.$$.ptrType.registeredClass;return wA(g.$$.ptr,I,this.registeredClass)}function NA(A){return this.fromWireType(d[A>>2])}function dA(A,g,I,C,B,Q,t,E,e,r,i){this.name=A,this.registeredClass=g,this.isReference=I,this.isConst=C,this.isSmartPointer=B,this.pointeeType=Q,this.sharingPolicy=t,this.rawGetPointee=E,this.rawConstructor=e,this.rawShare=r,this.rawDestructor=i,B||void 0!==g.baseClass?this.toWireType=GA:C?(this.toWireType=fA,this.destructorFunction=null):(this.toWireType=lA,this.destructorFunction=null)}var pA,FA,YA=[],kA=function(A){var g=YA[A];return g||(A>=YA.length&&(YA.length=A+1),YA[A]=g=pA.get(A)),g},MA=function(A,g){var I=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return A.includes("j")?function(A,g,I){return A=A.replace(/p/g,"i"),t["dynCall_"+A].apply(void 0,[g].concat(o(I)))}(A,g,I):kA(g).apply(void 0,o(I))},RA=function(A,g){var I,C,B=(A=P(A)).includes("j")?(I=A,C=g,function(){for(var A=arguments.length,g=new Array(A),B=0;B<A;B++)g[B]=arguments[B];return MA(I,C,g)}):kA(g);return"function"!=typeof B&&q("unknown function pointer with signature ".concat(A,": ").concat(g)),B},SA=function(A){var g=sg(A),I=P(g);return ug(g),I},LA=function(A,g){var I=[],C={};throw g.forEach((function A(g){C[g]||O[g]||(z[g]?z[g].forEach(A):(I.push(g),C[g]=!0))})),new FA("".concat(A,": ")+I.map(SA).join([", "]))},bA=function(A,g){for(var I=[],C=0;C<A;C++)I.push(d[g+4*C>>2]);return I},vA=function(A){for(;A.length;){var g=A.pop();A.pop()(g)}};function UA(A,g,I,C,B,Q){var t=g.length;t<2&&q("argTypes array size mismatch! Must at least get return value and 'this' types!");var E=null!==g[1]&&null!==I,e=function(A){for(var g=1;g<A.length;++g)if(null!==A[g]&&void 0===A[g].destructorFunction)return!0;return!1}(g),r="void"!==g[0].name,i=t-2,o=new Array(i),n=[],a=[];return yA(A,(function(){var I;arguments.length!==i&&q("function ".concat(A," called with ").concat(arguments.length," arguments, expected ").concat(i)),a.length=0,n.length=E?2:1,n[0]=B,E&&(I=g[1].toWireType(a,this),n[1]=I);for(var Q=0;Q<i;++Q)o[Q]=g[Q+2].toWireType(a,Q<0||arguments.length<=Q?void 0:arguments[Q]),n.push(o[Q]);return function(A){if(e)vA(a);else for(var C=E?1:2;C<g.length;C++){var B=1===C?I:o[C-2];null!==g[C].destructorFunction&&g[C].destructorFunction(B)}if(r)return g[0].fromWireType(A)}(C.apply(void 0,n))}))}var JA,mA,HA,KA=function(A,g,I){return A instanceof Object||q("".concat(I,' with invalid "this": ').concat(A)),A instanceof g.registeredClass.constructor||q("".concat(I,' incompatible with "this" of type ').concat(A.constructor.name)),A.$$.ptr||q("cannot call emscripten binding method ".concat(I," on deleted object")),wA(A.$$.ptr,A.$$.ptrType.registeredClass,g.registeredClass)},ZA=[],jA=[],xA=function(A){A>9&&0==--jA[A+1]&&(jA[A]=void 0,ZA.push(A))},TA=function(){return jA.length/2-5-ZA.length},PA={toValue:function(A){return A||q("Cannot use deleted val. handle = "+A),jA[A]},toHandle:function(A){switch(A){case void 0:return 2;case null:return 4;case!0:return 6;case!1:return 8;default:var g=ZA.pop()||jA.length;return jA[g]=A,jA[g+1]=1,g}}},WA={name:"emscripten::val",fromWireType:function(A){var g=PA.toValue(A);return xA(A),g},toWireType:function(A,g){return PA.toHandle(g)},argPackAdvance:8,readValueFromPointer:NA,destructorFunction:null},OA=function(A){if(null===A)return"null";var g=a(A);return"object"===g||"array"===g||"function"===g?A.toString():""+A},zA=function(A,g){switch(g){case 4:return function(A){return this.fromWireType(p[A>>2])};case 8:return function(A){return this.fromWireType(F[A>>3])};default:throw new TypeError("invalid float width (".concat(g,"): ").concat(A))}},qA=function(A,g,I){switch(g){case 1:return I?function(A){return w[A]}:function(A){return f[A]};case 2:return I?function(A){return G[A>>1]}:function(A){return l[A>>1]};case 4:return I?function(A){return N[A>>2]}:function(A){return d[A>>2]};default:throw new TypeError("invalid integer width (".concat(g,"): ").concat(A))}},VA="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,XA=function(A,g,I){for(var C=g+I,B=g;A[B]&&!(B>=C);)++B;if(B-g>16&&A.buffer&&VA)return VA.decode(A.subarray(g,B));for(var Q="";g<B;){var t=A[g++];if(128&t){var E=63&A[g++];if(192!=(224&t)){var e=63&A[g++];if((t=224==(240&t)?(15&t)<<12|E<<6|e:(7&t)<<18|E<<12|e<<6|63&A[g++])<65536)Q+=String.fromCharCode(t);else{var r=t-65536;Q+=String.fromCharCode(55296|r>>10,56320|1023&r)}}else Q+=String.fromCharCode((31&t)<<6|E)}else Q+=String.fromCharCode(t)}return Q},$A="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,_A=function(A,g){for(var I=A,C=I>>1,B=C+g/2;!(C>=B)&&l[C];)++C;if((I=C<<1)-A>32&&$A)return $A.decode(f.subarray(A,I));for(var Q="",t=0;!(t>=g/2);++t){var E=G[A+2*t>>1];if(0==E)break;Q+=String.fromCharCode(E)}return Q},Ag=function(A,g,I){if(null!=I||(I=2147483647),I<2)return 0;for(var C=g,B=(I-=2)<2*A.length?I/2:A.length,Q=0;Q<B;++Q){var t=A.charCodeAt(Q);G[g>>1]=t,g+=2}return G[g>>1]=0,g-C},gg=function(A){return 2*A.length},Ig=function(A,g){for(var I=0,C="";!(I>=g/4);){var B=N[A+4*I>>2];if(0==B)break;if(++I,B>=65536){var Q=B-65536;C+=String.fromCharCode(55296|Q>>10,56320|1023&Q)}else C+=String.fromCharCode(B)}return C},Cg=function(A,g,I){if(null!=I||(I=2147483647),I<4)return 0;for(var C=g,B=C+I-4,Q=0;Q<A.length;++Q){var t=A.charCodeAt(Q);if(t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&A.charCodeAt(++Q)),N[g>>2]=t,(g+=4)+4>B)break}return N[g>>2]=0,g-C},Bg=function(A){for(var g=0,I=0;I<A.length;++I){var C=A.charCodeAt(I);C>=55296&&C<=57343&&++I,g+=4}return g},Qg=function(A,g){var I=O[A];return void 0===I&&q("".concat(g," has unknown type ").concat(SA(A))),I},tg=function(A,g,I){var C=[],B=A.toWireType(C,I);return C.length&&(d[g>>2]=PA.toHandle(C)),B},Eg={},eg=[],rg=Reflect.construct,ig=[null,[],[]];!function(){for(var A=new Array(256),g=0;g<256;++g)A[g]=String.fromCharCode(g);Z=A}(),j=t.BindingError=function(A){function g(A){var I;return B(this,g),(I=C(this,g,[A])).name="BindingError",I}return E(g,A),Q(g)}(c(Error)),x=t.InternalError=function(A){function g(A){var I;return B(this,g),(I=C(this,g,[A])).name="InternalError",I}return E(g,A),Q(g)}(c(Error)),Object.assign(uA.prototype,{isAliasOf:function(A){if(!(this instanceof uA))return!1;if(!(A instanceof uA))return!1;var g=this.$$.ptrType.registeredClass,I=this.$$.ptr;A.$$=A.$$;for(var C=A.$$.ptrType.registeredClass,B=A.$$.ptr;g.baseClass;)I=g.upcast(I),g=g.baseClass;for(;C.baseClass;)B=C.upcast(B),C=C.baseClass;return g===C&&I===B},clone:function(){if(this.$$.ptr||AA(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var A,g=sA(Object.create(Object.getPrototypeOf(this),{$$:{value:(A=this.$$,{count:A.count,deleteScheduled:A.deleteScheduled,preservePointerOnDelete:A.preservePointerOnDelete,ptr:A.ptr,ptrType:A.ptrType,smartPtr:A.smartPtr,smartPtrType:A.smartPtrType})}}));return g.$$.count.value+=1,g.$$.deleteScheduled=!1,g},delete:function(){this.$$.ptr||AA(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&q("Object already scheduled for deletion"),IA(this),CA(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted:function(){return!this.$$.ptr},deleteLater:function(){return this.$$.ptr||AA(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&q("Object already scheduled for deletion"),eA.push(this),1===eA.length&&_&&_(rA),this.$$.deleteScheduled=!0,this}}),t.getInheritedInstanceCount=tA,t.getLiveInheritedInstances=EA,t.flushPendingDeletes=rA,t.setDelayFunction=iA,Object.assign(dA.prototype,{getPointee:function(A){return this.rawGetPointee&&(A=this.rawGetPointee(A)),A},destructor:function(A){var g;null==(g=this.rawDestructor)||g.call(this,A)},argPackAdvance:8,readValueFromPointer:NA,fromWireType:cA}),FA=t.UnboundTypeError=(JA=Error,(HA=yA(mA="UnboundTypeError",(function(A){this.name=mA,this.message=A;var g=new Error(A).stack;void 0!==g&&(this.stack=this.toString()+"\n"+g.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(JA.prototype),HA.prototype.constructor=HA,HA.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},HA),jA.push(0,1,void 0,1,null,1,!0,1,!1,1),t.count_emval_handles=TA;var og,ng={w:function(A,g,I){throw new T(A).init(g,I),A},q:function(){b("")},p:function(A,g,I,C,B){},u:function(A,g,I,C){$(A,{name:g=P(g),fromWireType:function(A){return!!A},toWireType:function(A,g){return g?I:C},argPackAdvance:8,readValueFromPointer:function(A){return this.fromWireType(f[A])},destructorFunction:null})},y:function(A,g,I,C,B,Q,E,e,r,i,o,n,a){o=P(o),Q=RA(B,Q),e&&(e=RA(E,e)),i&&(i=RA(r,i)),a=RA(n,a);var c=function(A){if(void 0===A)return"_unknown";var g=(A=A.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return g>=48&&g<=57?"_".concat(A):A}(o);!function(A,g,I){t.hasOwnProperty(A)?(q("Cannot register public name '".concat(A,"' twice")),DA(t,A,A),t.hasOwnProperty(I)&&q("Cannot register multiple overloads of a function with the same number of arguments (".concat(I,")!")),t[A].overloadTable[I]=g):t[A]=g}(c,(function(){LA("Cannot construct ".concat(o," due to unbound types"),[C])})),X([A,g,I],C?[C]:[],(function(g){var I,B,E;g=g[0],E=C?(B=g.registeredClass).instancePrototype:uA.prototype;var r=yA(o,(function(){if(Object.getPrototypeOf(this)!==n)throw new j("Use 'new' to construct "+o);if(void 0===s.constructor_body)throw new j(o+" has no accessible constructor");for(var A=arguments.length,g=new Array(A),I=0;I<A;I++)g[I]=arguments[I];var C=s.constructor_body[g.length];if(void 0===C)throw new j("Tried to invoke ctor of ".concat(o," with invalid number of parameters (").concat(g.length,") - expected (").concat(Object.keys(s.constructor_body).toString(),") parameters instead!"));return C.apply(this,g)})),n=Object.create(E,{constructor:{value:r}});r.prototype=n;var s=new hA(o,r,n,a,B,Q,e,i);s.baseClass&&(null!=(I=s.baseClass).__derivedClasses||(I.__derivedClasses=[]),s.baseClass.__derivedClasses.push(s));var u=new dA(o,s,!0,!1,!1),y=new dA(o+"*",s,!1,!1,!1),D=new dA(o+" const*",s,!1,!0,!1);return QA[A]={pointerType:y,constPointerType:D},function(A,g,I){t.hasOwnProperty(A)||V("Replacing nonexistent public symbol"),void 0!==t[A].overloadTable&&void 0!==I||(t[A]=g,t[A].argCount=I)}(c,r),[u,y,D]}))},x:function(A,g,I,C,B,Q){var t=bA(g,I);B=RA(C,B),X([],[A],(function(A){A=A[0];var I="constructor ".concat(A.name);if(void 0===A.registeredClass.constructor_body&&(A.registeredClass.constructor_body=[]),void 0!==A.registeredClass.constructor_body[g-1])throw new j("Cannot register multiple constructors with identical number of parameters (".concat(g-1,") for class '").concat(A.name,"'! Overload resolution is currently only performed using the parameter count, not actual type info!"));return A.registeredClass.constructor_body[g-1]=function(){LA("Cannot construct ".concat(A.name," due to unbound types"),t)},X([],t,(function(C){return C.splice(1,0,null),A.registeredClass.constructor_body[g-1]=UA(I,C,null,B,Q),[]})),[]}))},h:function(A,g,I,C,B,Q,t,E,e){var r,i,o=bA(I,C);g=P(g),i=(r=(r=g).trim()).indexOf("("),g=-1!==i?r.substr(0,i):r,Q=RA(B,Q),X([],[A],(function(A){A=A[0];var C="".concat(A.name,".").concat(g);function B(){LA("Cannot call ".concat(C," due to unbound types"),o)}g.startsWith("@@")&&(g=Symbol[g.substring(2)]),E&&A.registeredClass.pureVirtualFunctions.push(g);var e=A.registeredClass.instancePrototype,r=e[g];return void 0===r||void 0===r.overloadTable&&r.className!==A.name&&r.argCount===I-2?(B.argCount=I-2,B.className=A.name,e[g]=B):(DA(e,g,C),e[g].overloadTable[I-2]=B),X([],o,(function(B){var E=UA(C,B,A,Q,t);return void 0===e[g].overloadTable?(E.argCount=I-2,e[g]=E):e[g].overloadTable[I-2]=E,[]})),[]}))},k:function(A,g,I,C,B,Q,t,E,e,r){g=P(g),B=RA(C,B),X([],[A],(function(A){A=A[0];var C="".concat(A.name,".").concat(g),i={get:function(){LA("Cannot access ".concat(C," due to unbound types"),[I,t])},enumerable:!0,configurable:!0};return i.set=e?function(){return LA("Cannot access ".concat(C," due to unbound types"),[I,t])}:function(A){return q(C+" is a read-only property")},Object.defineProperty(A.registeredClass.instancePrototype,g,i),X([],e?[I,t]:[I],(function(I){var t=I[0],i={get:function(){var g=KA(this,A,C+" getter");return t.fromWireType(B(Q,g))},enumerable:!0};if(e){e=RA(E,e);var o=I[1];i.set=function(g){var I=KA(this,A,C+" setter"),B=[];e(r,I,o.toWireType(B,g)),vA(B)}}return Object.defineProperty(A.registeredClass.instancePrototype,g,i),[]})),[]}))},t:function(A){return $(A,WA)},o:function(A,g,I){$(A,{name:g=P(g),fromWireType:function(A){return A},toWireType:function(A,g){return g},argPackAdvance:8,readValueFromPointer:zA(g,I),destructorFunction:null})},g:function(A,g,I,C,B){g=P(g);var Q=function(A){return A};if(0===C){var t=32-8*I;Q=function(A){return A<<t>>>t}}var E=g.includes("unsigned");$(A,{name:g,fromWireType:Q,toWireType:E?function(A,g){return this.name,g>>>0}:function(A,g){return this.name,g},argPackAdvance:8,readValueFromPointer:qA(g,I,0!==C),destructorFunction:null})},a:function(A,g,I){var C=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][g];function B(A){var g=d[A>>2],I=d[A+4>>2];return new C(w.buffer,I,g)}$(A,{name:I=P(I),fromWireType:B,argPackAdvance:8,readValueFromPointer:B},{ignoreDuplicateRegistrations:!0})},n:function(A,g){var I="std::string"===(g=P(g));$(A,{name:g,fromWireType:function(A){var g,C,B,Q=d[A>>2],t=A+4;if(I)for(var E=t,e=0;e<=Q;++e){var r=t+e;if(e==Q||0==f[r]){var i=(B=r-E,(C=E)?XA(f,C,B):"");void 0===g?g=i:(g+=String.fromCharCode(0),g+=i),E=r+1}}else{var o=new Array(Q);for(e=0;e<Q;++e)o[e]=String.fromCharCode(f[t+e]);g=o.join("")}return ug(A),g},toWireType:function(A,g){var C;g instanceof ArrayBuffer&&(g=new Uint8Array(g));var B="string"==typeof g;B||g instanceof Uint8Array||g instanceof Uint8ClampedArray||g instanceof Int8Array||q("Cannot pass non-string to std::string"),C=I&&B?function(A){for(var g=0,I=0;I<A.length;++I){var C=A.charCodeAt(I);C<=127?g++:C<=2047?g+=2:C>=55296&&C<=57343?(g+=4,++I):g+=3}return g}(g):g.length;var Q=cg(4+C+1),t=Q+4;if(d[Q>>2]=C,I&&B)!function(A,g,I,C){if(!(C>0))return 0;for(var B=I+C-1,Q=0;Q<A.length;++Q){var t=A.charCodeAt(Q);if(t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&A.charCodeAt(++Q)),t<=127){if(I>=B)break;g[I++]=t}else if(t<=2047){if(I+1>=B)break;g[I++]=192|t>>6,g[I++]=128|63&t}else if(t<=65535){if(I+2>=B)break;g[I++]=224|t>>12,g[I++]=128|t>>6&63,g[I++]=128|63&t}else{if(I+3>=B)break;g[I++]=240|t>>18,g[I++]=128|t>>12&63,g[I++]=128|t>>6&63,g[I++]=128|63&t}}g[I]=0}(g,f,t,C+1);else if(B)for(var E=0;E<C;++E){var e=g.charCodeAt(E);e>255&&(ug(t),q("String has UTF-16 code units that do not fit in 8 bits")),f[t+E]=e}else for(E=0;E<C;++E)f[t+E]=g[E];return null!==A&&A.push(ug,Q),Q},argPackAdvance:8,readValueFromPointer:NA,destructorFunction:function(A){ug(A)}})},l:function(A,g,I){var C,B,Q,t;I=P(I),2===g?(C=_A,B=Ag,t=gg,Q=function(A){return l[A>>1]}):4===g&&(C=Ig,B=Cg,t=Bg,Q=function(A){return d[A>>2]}),$(A,{name:I,fromWireType:function(A){for(var I,B=d[A>>2],t=A+4,E=0;E<=B;++E){var e=A+4+E*g;if(E==B||0==Q(e)){var r=C(t,e-t);void 0===I?I=r:(I+=String.fromCharCode(0),I+=r),t=e+g}}return ug(A),I},toWireType:function(A,C){"string"!=typeof C&&q("Cannot pass non-string to C++ string type ".concat(I));var Q=t(C),E=cg(4+Q+g);return d[E>>2]=Q/g,B(C,E+4,Q+g),null!==A&&A.push(ug,E),E},argPackAdvance:8,readValueFromPointer:NA,destructorFunction:function(A){ug(A)}})},v:function(A,g){$(A,{isVoid:!0,name:g=P(g),argPackAdvance:0,fromWireType:function(){},toWireType:function(A,g){}})},s:function(A,g,I){return f.copyWithin(A,g,g+I)},j:function(A,g,I){return A=PA.toValue(A),g=Qg(g,"emval::as"),tg(g,I,A)},e:function(A,g,I,C,B){var Q,t;return(A=eg[A])(g=PA.toValue(g),g[I=void 0===(t=Eg[Q=I])?P(Q):t],C,B)},c:xA,f:function(A,g,I){var C=function(A,g){for(var I=new Array(A),C=0;C<A;++C)I[C]=Qg(d[g+4*C>>2],"parameter "+C);return I}(A,g),B=C.shift();A--;var Q,t,E=new Array(A),e="methodCaller<(".concat(C.map((function(A){return A.name})).join(", "),") => ").concat(B.name,">");return Q=yA(e,(function(g,Q,t,e){for(var r=0,i=0;i<A;++i)E[i]=C[i].readValueFromPointer(e+r),r+=C[i].argPackAdvance;var o=1===I?rg(Q,E):Q.apply(g,E);return tg(B,t,o)})),t=eg.length,eg.push(Q),t},d:function(A){A>9&&(jA[A+1]+=1)},b:function(A){var g=PA.toValue(A);vA(g),xA(A)},i:function(A,g){var I=(A=Qg(A,"_emval_take_value")).readValueFromPointer(g);return PA.toHandle(I)},r:function(A){f.length,b("OOM")},m:function(A,g,I,C){for(var B,Q,t,E=0,e=0;e<I;e++){var r=d[g>>2],i=d[g+4>>2];g+=8;for(var o=0;o<i;o++)B=A,Q=f[r+o],t=void 0,t=ig[B],0===Q||10===Q?((1===B?u:y)(XA(t,0)),t.length=0):t.push(Q);E+=i}return d[C>>2]=E,0}},ag=function(){var A,I={a:ng};function C(A,g){var I,C;return ag=A.exports,s=ag.z,I=s.buffer,t.HEAP8=w=new Int8Array(I),t.HEAP16=G=new Int16Array(I),t.HEAPU8=f=new Uint8Array(I),t.HEAPU16=l=new Uint16Array(I),t.HEAP32=N=new Int32Array(I),t.HEAPU32=d=new Uint32Array(I),t.HEAPF32=p=new Float32Array(I),t.HEAPF64=F=new Float64Array(I),pA=ag.C,C=ag.A,M.unshift(C),function(){var A;if(S--,null==(A=t.monitorRunDependencies)||A.call(t,S),0==S&&L){var g=L;L=null,g()}}(),ag}if(S++,null==(A=t.monitorRunDependencies)||A.call(t,S),t.instantiateWasm)try{return t.instantiateWasm(I,C)}catch(A){y("Module.instantiateWasm callback failed with error: ".concat(A)),g(A)}return v||(v="data:application/octet-stream;base64,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"),H(0,v,I,(function(A){C(A.instance)})).catch(g),{}}(),cg=function(A){return(cg=ag.B)(A)},sg=function(A){return(sg=ag.D)(A)},ug=function(A){return(ug=ag.E)(A)},yg=function(A){return(yg=ag.F)(A)};function Dg(){function g(){og||(og=!0,t.calledRun=!0,Y||(K(M),A(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)A=t.postRun.shift(),R.unshift(A);var A;K(R)}()))}S>0||(function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)A=t.preRun.shift(),k.unshift(A);var A;K(k)}(),S>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),g()}),1)):g()))}if(t.dynCall_jiji=function(A,g,I,C,B){return(t.dynCall_jiji=ag.G)(A,g,I,C,B)},t._vertexShaderSource=9872,L=function A(){og||Dg(),og||(L=A)},t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();return Dg(),e}),f=w,G=0,l=function(){function A(g){B(this,A),this.core=g,u(this,"seq"),u(this,"_core"),u(this,"log"),u(this,"beautyParams"),G+=1,this.seq=G,this._core=g,this.log=g.log.createChild({id:"".concat(this.getAlias()).concat(G)}),this.log.info("created")}return Q(A,[{key:"getName",value:function(){return A.Name}},{key:"getAlias",value:function(){return"bb"}},{key:"getValidateRule",value:function(A){switch(A){case"start":case"update":return y(this._core);case"stop":return this._core,{name:"StopBasicBeautyOptions",required:!1}}}},{key:"getGroup",value:function(){return"w"}},{key:"start",value:(t=I(r().mark((function A(g){var I,C,B;return r().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:if(this._core.room.videoManager.Wasm){A.next=4;break}return A.next=3,f();case 3:this._core.room.videoManager.Wasm=A.sent;case 4:return this._core.room.videoManager.renderMode="webgl",I=this._core.utils.isUndefined(g.beauty)?.5:g.beauty,C=this._core.utils.isUndefined(g.brightness)?.5:g.brightness,B=this._core.utils.isUndefined(g.ruddy)?.5:g.ruddy,A.abrupt("return",this._core.room.videoManager.setBeautyParams({beauty:I,brightness:C,ruddy:B}));case 9:case"end":return A.stop()}}),A,this)}))),function(A){return t.apply(this,arguments)})},{key:"update",value:(C=I(r().mark((function A(g){var I,C,B;return r().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return I=this._core.utils.isUndefined(g.beauty)?.5:g.beauty,C=this._core.utils.isUndefined(g.brightness)?.5:g.brightness,B=this._core.utils.isUndefined(g.ruddy)?.5:g.ruddy,A.abrupt("return",this._core.room.videoManager.setBeautyParams({beauty:I,brightness:C,ruddy:B}));case 4:case"end":return A.stop()}}),A,this)}))),function(A){return C.apply(this,arguments)})},{key:"stop",value:(g=I(r().mark((function A(){return r().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return this._core.room.videoManager.renderMode="auto",A.abrupt("return",this._core.room.videoManager.stopBeauty());case 2:case"end":return A.stop()}}),A,this)}))),function(){return g.apply(this,arguments)})},{key:"destroy",value:function(){this._core.room.videoManager.renderMode="auto"}}]);var g,C,t}();return u(l,"Name","BasicBeauty"),l}));
