package repository

import (
	"fmt"

	"live-streaming-platform/internal/model"
	"gorm.io/gorm"
)

// RoomRepository 直播间仓库接口
type RoomRepository interface {
	Create(room *model.Room) error
	Update(room *model.Room) error
	Delete(id uint) error
	GetByID(id uint) (*model.Room, error)
	GetByUserID(userID uint) (*model.Room, error)
	GetByStreamKey(streamKey string) (*model.Room, error)
	GetList(page, pageSize int, categoryID uint, keyword string, status int) ([]*model.Room, int64, error)
	GetLiveRooms(page, pageSize int) ([]*model.Room, int64, error)
	UpdateViewerCount(roomID uint, count int) error
	IncrementLikeCount(roomID uint) error
	IncrementShareCount(roomID uint) error
	IncrementMessageCount(roomID uint) error
	IncrementGiftCount(roomID uint) error
}

// roomRepository 直播间仓库实现
type roomRepository struct {
	db *gorm.DB
}

// NewRoomRepository 创建直播间仓库
func NewRoomRepository(db *gorm.DB) RoomRepository {
	return &roomRepository{db: db}
}

// Create 创建直播间
func (r *roomRepository) Create(room *model.Room) error {
	if err := r.db.Create(room).Error; err != nil {
		return fmt.Errorf("创建直播间失败: %w", err)
	}
	return nil
}

// Update 更新直播间
func (r *roomRepository) Update(room *model.Room) error {
	if err := r.db.Save(room).Error; err != nil {
		return fmt.Errorf("更新直播间失败: %w", err)
	}
	return nil
}

// Delete 删除直播间
func (r *roomRepository) Delete(id uint) error {
	if err := r.db.Delete(&model.Room{}, id).Error; err != nil {
		return fmt.Errorf("删除直播间失败: %w", err)
	}
	return nil
}

// GetByID 根据ID获取直播间
func (r *roomRepository) GetByID(id uint) (*model.Room, error) {
	var room model.Room
	if err := r.db.Preload("User").Preload("Category").First(&room, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("直播间不存在")
		}
		return nil, fmt.Errorf("获取直播间失败: %w", err)
	}
	return &room, nil
}

// GetByUserID 根据用户ID获取直播间
func (r *roomRepository) GetByUserID(userID uint) (*model.Room, error) {
	var room model.Room
	if err := r.db.Preload("User").Preload("Category").Where("user_id = ?", userID).First(&room).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户没有直播间")
		}
		return nil, fmt.Errorf("获取用户直播间失败: %w", err)
	}
	return &room, nil
}

// GetByStreamKey 根据流密钥获取直播间
func (r *roomRepository) GetByStreamKey(streamKey string) (*model.Room, error) {
	var room model.Room
	if err := r.db.Preload("User").Preload("Category").Where("stream_key = ?", streamKey).First(&room).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("直播间不存在")
		}
		return nil, fmt.Errorf("获取直播间失败: %w", err)
	}
	return &room, nil
}

// GetList 获取直播间列表
func (r *roomRepository) GetList(page, pageSize int, categoryID uint, keyword string, status int) ([]*model.Room, int64, error) {
	var rooms []*model.Room
	var total int64

	query := r.db.Model(&model.Room{}).Preload("User").Preload("Category")

	// 分类筛选
	if categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	// 关键词搜索
	if keyword != "" {
		query = query.Where("title LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 状态筛选
	if status >= 0 {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取直播间总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&rooms).Error; err != nil {
		return nil, 0, fmt.Errorf("获取直播间列表失败: %w", err)
	}

	return rooms, total, nil
}

// GetLiveRooms 获取正在直播的房间列表
func (r *roomRepository) GetLiveRooms(page, pageSize int) ([]*model.Room, int64, error) {
	var rooms []*model.Room
	var total int64

	query := r.db.Model(&model.Room{}).Preload("User").Preload("Category").Where("status = ?", 1)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取直播间总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("viewer_count DESC, created_at DESC").Find(&rooms).Error; err != nil {
		return nil, 0, fmt.Errorf("获取直播间列表失败: %w", err)
	}

	return rooms, total, nil
}

// UpdateViewerCount 更新观众数量
func (r *roomRepository) UpdateViewerCount(roomID uint, count int) error {
	if err := r.db.Model(&model.Room{}).Where("id = ?", roomID).Update("viewer_count", count).Error; err != nil {
		return fmt.Errorf("更新观众数量失败: %w", err)
	}
	return nil
}

// IncrementLikeCount 增加点赞数
func (r *roomRepository) IncrementLikeCount(roomID uint) error {
	if err := r.db.Model(&model.Room{}).Where("id = ?", roomID).UpdateColumn("like_count", gorm.Expr("like_count + ?", 1)).Error; err != nil {
		return fmt.Errorf("增加点赞数失败: %w", err)
	}
	return nil
}

// IncrementShareCount 增加分享数
func (r *roomRepository) IncrementShareCount(roomID uint) error {
	if err := r.db.Model(&model.Room{}).Where("id = ?", roomID).UpdateColumn("share_count", gorm.Expr("share_count + ?", 1)).Error; err != nil {
		return fmt.Errorf("增加分享数失败: %w", err)
	}
	return nil
}

// IncrementMessageCount 增加消息数
func (r *roomRepository) IncrementMessageCount(roomID uint) error {
	if err := r.db.Model(&model.Room{}).Where("id = ?", roomID).UpdateColumn("message_count", gorm.Expr("message_count + ?", 1)).Error; err != nil {
		return fmt.Errorf("增加消息数失败: %w", err)
	}
	return nil
}

// IncrementGiftCount 增加礼物数
func (r *roomRepository) IncrementGiftCount(roomID uint) error {
	if err := r.db.Model(&model.Room{}).Where("id = ?", roomID).UpdateColumn("gift_count", gorm.Expr("gift_count + ?", 1)).Error; err != nil {
		return fmt.Errorf("增加礼物数失败: %w", err)
	}
	return nil
}
