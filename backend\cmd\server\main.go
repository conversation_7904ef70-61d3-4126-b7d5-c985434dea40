package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"live-streaming-platform/config"
	"live-streaming-platform/internal/controller"
	"live-streaming-platform/internal/middleware"
	"live-streaming-platform/internal/repository"
	"live-streaming-platform/internal/service"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	if err := config.Load(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	cfg := config.AppConfig

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 初始化数据库
	db, err := repository.NewDatabase(cfg)
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer db.Close()

	// 初始化仓储层
	userRepo := repository.NewUserRepository(db)
	roomRepo := repository.NewRoomRepository(db.GetDB())
	categoryRepo := repository.NewCategoryRepository(db.GetDB())

	// 初始化服务层
	userService := service.NewUserService(
		userRepo,
		cfg.JWT.Secret,
		cfg.JWT.ExpireHours,
		cfg.Security.BcryptCost,
	)

	liveService, err := service.NewLiveService(roomRepo)
	if err != nil {
		log.Fatalf("初始化直播服务失败: %v", err)
	}

	categoryService := service.NewCategoryService(categoryRepo)

	// 初始化默认分类
	if err := categoryService.InitDefaultCategories(); err != nil {
		log.Printf("初始化默认分类失败: %v", err)
	}

	// 初始化控制器
	userController := controller.NewUserController(userService)
	liveController := controller.NewSimpleLiveController(liveService)
	categoryController := controller.NewSimpleCategoryController(categoryService)

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.RequestIDMiddleware())
	r.Use(middleware.LoggerMiddleware(cfg.Log.File))
	r.Use(middleware.RecoveryMiddleware())
	r.Use(middleware.CORSMiddleware(cfg.CORS.AllowOrigins, cfg.CORS.AllowCredentials))
	r.Use(middleware.SecurityHeadersMiddleware())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		if err := db.Health(); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "unhealthy",
				"error":  err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
			"time":   time.Now(),
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 认证路由
		auth := api.Group("/auth")
		{
			auth.POST("/register", userController.Register)
			auth.POST("/login", userController.Login)
			auth.POST("/refresh", middleware.AuthMiddleware(userService), userController.RefreshToken)
		}

		// 用户路由
		users := api.Group("/users")
		{
			// 公开路由
			users.GET("/search", userController.SearchUsers)
			users.GET("/:id/profile", userController.GetProfile)
			users.GET("/:id/followers", userController.GetFollowers)
			users.GET("/:id/following", userController.GetFollowing)

			// 需要认证的路由
			authenticated := users.Group("")
			authenticated.Use(middleware.AuthMiddleware(userService))
			{
				authenticated.GET("/profile", userController.GetProfile)
				authenticated.PUT("/profile", userController.UpdateProfile)
				authenticated.PUT("/password", userController.ChangePassword)
				authenticated.GET("/followers", userController.GetFollowers)
				authenticated.GET("/following", userController.GetFollowing)
				authenticated.POST("/:id/follow", userController.Follow)
				authenticated.POST("/:id/unfollow", userController.Unfollow)
			}
		}

		// 分类路由
		categories := api.Group("/categories")
		{
			categories.GET("", categoryController.GetCategories)
			categories.GET("/:id", categoryController.GetCategoryDetail)

			// 管理员路由
			admin := categories.Group("")
			admin.Use(middleware.AuthMiddleware(userService))
			{
				admin.POST("", categoryController.CreateCategory)
			}
		}

		// 直播间路由
		rooms := api.Group("/rooms")
		{
			// 公开路由
			rooms.GET("", liveController.GetRoomList)
			rooms.GET("/:id", liveController.GetRoomDetail)

			// 需要认证的路由
			authenticated := rooms.Group("")
			authenticated.Use(middleware.AuthMiddleware(userService))
			{
				authenticated.POST("", liveController.CreateRoom)
				authenticated.GET("/my", liveController.GetMyRoom)
				authenticated.POST("/:id/start", liveController.StartLive)
				authenticated.POST("/:id/stop", liveController.StopLive)
			}
		}
	}

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    cfg.GetServerAddr(),
		Handler: r,
	}

	// 启动服务器
	go func() {
		log.Printf("服务器启动在 %s", cfg.GetServerAddr())
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("启动服务器失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭服务器...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("服务器强制关闭: %v", err)
	}

	log.Println("服务器已关闭")
}
