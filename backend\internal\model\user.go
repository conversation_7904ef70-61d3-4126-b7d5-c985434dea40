package model

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 基本信息
	Username string `json:"username" gorm:"uniqueIndex;size:50;not null" validate:"required,min=3,max=50"`
	Email    string `json:"email" gorm:"uniqueIndex;size:100;not null" validate:"required,email"`
	Password string `json:"-" gorm:"size:255;not null" validate:"required,min=6"`
	
	// 个人信息
	Nickname string `json:"nickname" gorm:"size:50"`
	Avatar   string `json:"avatar" gorm:"size:255"`
	Gender   int    `json:"gender" gorm:"default:0"` // 0:未知 1:男 2:女
	Birthday *time.Time `json:"birthday"`
	Bio      string `json:"bio" gorm:"size:500"`
	
	// 状态信息
	Status    int  `json:"status" gorm:"default:1"`    // 0:禁用 1:正常 2:待审核
	IsVerified bool `json:"is_verified" gorm:"default:false"` // 是否认证
	
	// 角色权限
	Role     string `json:"role" gorm:"size:20;default:'user'"` // user, streamer, admin
	Level    int    `json:"level" gorm:"default:1"`             // 用户等级
	
	// 统计信息
	FollowCount    int `json:"follow_count" gorm:"default:0"`    // 关注数
	FollowerCount  int `json:"follower_count" gorm:"default:0"`  // 粉丝数
	StreamCount    int `json:"stream_count" gorm:"default:0"`    // 直播次数
	TotalWatchTime int `json:"total_watch_time" gorm:"default:0"` // 总观看时长(秒)
	
	// 最后活跃时间
	LastLoginAt *time.Time `json:"last_login_at"`
	LastLoginIP string     `json:"last_login_ip" gorm:"size:45"`
	
	// 关联关系
	Rooms     []Room     `json:"rooms,omitempty" gorm:"foreignKey:UserID"`
	Messages  []Message  `json:"messages,omitempty" gorm:"foreignKey:UserID"`
	Follows   []Follow   `json:"follows,omitempty" gorm:"foreignKey:FollowerID"`
	Followers []Follow   `json:"followers,omitempty" gorm:"foreignKey:FollowedID"`
}

// UserProfile 用户公开信息
type UserProfile struct {
	ID           uint       `json:"id"`
	Username     string     `json:"username"`
	Nickname     string     `json:"nickname"`
	Avatar       string     `json:"avatar"`
	Gender       int        `json:"gender"`
	Bio          string     `json:"bio"`
	IsVerified   bool       `json:"is_verified"`
	Role         string     `json:"role"`
	Level        int        `json:"level"`
	FollowCount  int        `json:"follow_count"`
	FollowerCount int       `json:"follower_count"`
	StreamCount  int        `json:"stream_count"`
	CreatedAt    time.Time  `json:"created_at"`
	LastLoginAt  *time.Time `json:"last_login_at"`
}

// UserStats 用户统计信息
type UserStats struct {
	UserID         uint `json:"user_id"`
	TotalStreams   int  `json:"total_streams"`
	TotalWatchTime int  `json:"total_watch_time"`
	TotalViewers   int  `json:"total_viewers"`
	AvgViewers     int  `json:"avg_viewers"`
	MaxViewers     int  `json:"max_viewers"`
	TotalGifts     int  `json:"total_gifts"`
	TotalRevenue   int  `json:"total_revenue"`
}

// Follow 关注关系模型
type Follow struct {
	ID         uint           `json:"id" gorm:"primarykey"`
	CreatedAt  time.Time      `json:"created_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
	
	FollowerID uint `json:"follower_id" gorm:"not null;index"`
	FollowedID uint `json:"followed_id" gorm:"not null;index"`
	
	// 关联关系
	Follower *User `json:"follower,omitempty" gorm:"foreignKey:FollowerID"`
	Followed *User `json:"followed,omitempty" gorm:"foreignKey:FollowedID"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// TableName 指定表名
func (Follow) TableName() string {
	return "follows"
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// 设置默认昵称
	if u.Nickname == "" {
		u.Nickname = u.Username
	}
	return nil
}

// ToProfile 转换为公开信息
func (u *User) ToProfile() *UserProfile {
	return &UserProfile{
		ID:            u.ID,
		Username:      u.Username,
		Nickname:      u.Nickname,
		Avatar:        u.Avatar,
		Gender:        u.Gender,
		Bio:           u.Bio,
		IsVerified:    u.IsVerified,
		Role:          u.Role,
		Level:         u.Level,
		FollowCount:   u.FollowCount,
		FollowerCount: u.FollowerCount,
		StreamCount:   u.StreamCount,
		CreatedAt:     u.CreatedAt,
		LastLoginAt:   u.LastLoginAt,
	}
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == "admin"
}

// IsStreamer 检查是否为主播
func (u *User) IsStreamer() bool {
	return u.Role == "streamer" || u.Role == "admin"
}

// CanStream 检查是否可以直播
func (u *User) CanStream() bool {
	return u.Status == 1 && (u.Role == "streamer" || u.Role == "admin")
}

// IsActive 检查用户是否活跃
func (u *User) IsActive() bool {
	return u.Status == 1
}

// UpdateLastLogin 更新最后登录信息
func (u *User) UpdateLastLogin(ip string) {
	now := time.Now()
	u.LastLoginAt = &now
	u.LastLoginIP = ip
}
