package controller

import (
	"net/http"
	"strconv"

	"live-streaming-platform/internal/middleware"
	"live-streaming-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// UserController 用户控制器
type UserController struct {
	userService service.UserService
	validator   *validator.Validate
}

// NewUserController 创建用户控制器
func NewUserController(userService service.UserService) *UserController {
	return &UserController{
		userService: userService,
		validator:   validator.New(),
	}
}

// Register 用户注册
// @Summary 用户注册
// @Description 用户注册接口
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param request body service.RegisterRequest true "注册信息"
// @Success 200 {object} service.AuthResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/auth/register [post]
func (ctrl *UserController) Register(c *gin.Context) {
	var req service.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "请求参数错误",
			Message: err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := ctrl.validator.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "参数验证失败",
			Message: err.Error(),
		})
		return
	}

	// 调用服务层
	resp, err := ctrl.userService.Register(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "注册失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "注册成功",
		Data:    resp,
	})
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录接口
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param request body service.LoginRequest true "登录信息"
// @Success 200 {object} service.AuthResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/auth/login [post]
func (ctrl *UserController) Login(c *gin.Context) {
	var req service.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "请求参数错误",
			Message: err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := ctrl.validator.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "参数验证失败",
			Message: err.Error(),
		})
		return
	}

	// 调用服务层
	resp, err := ctrl.userService.Login(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "登录失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "登录成功",
		Data:    resp,
	})
}

// GetProfile 获取用户资料
// @Summary 获取用户资料
// @Description 获取用户资料接口
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int false "用户ID，不传则获取当前用户资料"
// @Success 200 {object} model.UserProfile
// @Failure 400 {object} ErrorResponse
// @Router /api/users/profile [get]
// @Router /api/users/{id}/profile [get]
func (ctrl *UserController) GetProfile(c *gin.Context) {
	var userID uint

	// 检查是否有路径参数
	if idParam := c.Param("id"); idParam != "" {
		id, err := strconv.ParseUint(idParam, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "用户ID格式错误",
				Message: err.Error(),
			})
			return
		}
		userID = uint(id)
	} else {
		// 获取当前用户ID
		currentUserID, exists := middleware.GetUserID(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error: "未认证用户",
			})
			return
		}
		userID = currentUserID
	}

	// 调用服务层
	profile, err := ctrl.userService.GetProfile(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "获取用户资料失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "获取成功",
		Data:    profile,
	})
}

// UpdateProfile 更新用户资料
// @Summary 更新用户资料
// @Description 更新用户资料接口
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body service.UpdateProfileRequest true "更新信息"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/users/profile [put]
func (ctrl *UserController) UpdateProfile(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: "未认证用户",
		})
		return
	}

	var req service.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "请求参数错误",
			Message: err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := ctrl.validator.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "参数验证失败",
			Message: err.Error(),
		})
		return
	}

	// 调用服务层
	if err := ctrl.userService.UpdateProfile(c.Request.Context(), userID, &req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "更新资料失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "更新成功",
	})
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改密码接口
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body service.ChangePasswordRequest true "密码信息"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/users/password [put]
func (ctrl *UserController) ChangePassword(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: "未认证用户",
		})
		return
	}

	var req service.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "请求参数错误",
			Message: err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := ctrl.validator.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "参数验证失败",
			Message: err.Error(),
		})
		return
	}

	// 调用服务层
	if err := ctrl.userService.ChangePassword(c.Request.Context(), userID, &req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "修改密码失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "密码修改成功",
	})
}

// Follow 关注用户
// @Summary 关注用户
// @Description 关注用户接口
// @Tags 用户关系
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "被关注用户ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/users/{id}/follow [post]
func (ctrl *UserController) Follow(c *gin.Context) {
	followerID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: "未认证用户",
		})
		return
	}

	followedID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "用户ID格式错误",
			Message: err.Error(),
		})
		return
	}

	// 调用服务层
	if err := ctrl.userService.Follow(c.Request.Context(), followerID, uint(followedID)); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "关注失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "关注成功",
	})
}

// Unfollow 取消关注
// @Summary 取消关注
// @Description 取消关注用户接口
// @Tags 用户关系
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "被取消关注用户ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/users/{id}/unfollow [post]
func (ctrl *UserController) Unfollow(c *gin.Context) {
	followerID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: "未认证用户",
		})
		return
	}

	followedID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "用户ID格式错误",
			Message: err.Error(),
		})
		return
	}

	// 调用服务层
	if err := ctrl.userService.Unfollow(c.Request.Context(), followerID, uint(followedID)); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "取消关注失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "取消关注成功",
	})
}

// GetFollowers 获取粉丝列表
// @Summary 获取粉丝列表
// @Description 获取用户粉丝列表
// @Tags 用户关系
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int false "用户ID，不传则获取当前用户粉丝"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} service.FollowListResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/users/followers [get]
// @Router /api/users/{id}/followers [get]
func (ctrl *UserController) GetFollowers(c *gin.Context) {
	var userID uint

	// 检查是否有路径参数
	if idParam := c.Param("id"); idParam != "" {
		id, err := strconv.ParseUint(idParam, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "用户ID格式错误",
				Message: err.Error(),
			})
			return
		}
		userID = uint(id)
	} else {
		// 获取当前用户ID
		currentUserID, exists := middleware.GetUserID(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error: "未认证用户",
			})
			return
		}
		userID = currentUserID
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 调用服务层
	resp, err := ctrl.userService.GetFollowers(c.Request.Context(), userID, page, pageSize)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "获取粉丝列表失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "获取成功",
		Data:    resp,
	})
}

// GetFollowing 获取关注列表
// @Summary 获取关注列表
// @Description 获取用户关注列表
// @Tags 用户关系
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int false "用户ID，不传则获取当前用户关注"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} service.FollowListResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/users/following [get]
// @Router /api/users/{id}/following [get]
func (ctrl *UserController) GetFollowing(c *gin.Context) {
	var userID uint

	// 检查是否有路径参数
	if idParam := c.Param("id"); idParam != "" {
		id, err := strconv.ParseUint(idParam, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "用户ID格式错误",
				Message: err.Error(),
			})
			return
		}
		userID = uint(id)
	} else {
		// 获取当前用户ID
		currentUserID, exists := middleware.GetUserID(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error: "未认证用户",
			})
			return
		}
		userID = currentUserID
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 调用服务层
	resp, err := ctrl.userService.GetFollowing(c.Request.Context(), userID, page, pageSize)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "获取关注列表失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "获取成功",
		Data:    resp,
	})
}

// SearchUsers 搜索用户
// @Summary 搜索用户
// @Description 搜索用户接口
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} service.UserListResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/users/search [get]
func (ctrl *UserController) SearchUsers(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: "搜索关键词不能为空",
		})
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 调用服务层
	resp, err := ctrl.userService.SearchUsers(c.Request.Context(), keyword, page, pageSize)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "搜索用户失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "搜索成功",
		Data:    resp,
	})
}

// RefreshToken 刷新令牌
// @Summary 刷新令牌
// @Description 刷新JWT令牌
// @Tags 用户认证
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} service.AuthResponse
// @Failure 400 {object} ErrorResponse
// @Router /api/auth/refresh [post]
func (ctrl *UserController) RefreshToken(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: "缺少认证令牌",
		})
		return
	}

	// 提取token
	tokenString := authHeader[7:] // 去掉 "Bearer " 前缀

	// 调用服务层
	resp, err := ctrl.userService.RefreshToken(c.Request.Context(), tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "刷新令牌失败",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "刷新成功",
		Data:    resp,
	})
}
