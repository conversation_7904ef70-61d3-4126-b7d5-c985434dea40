{"hash": "09386c83", "configHash": "7c3e7e6c", "lockfileHash": "b0bb5ddb", "browserHash": "25c99ec8", "optimized": {"@element-plus/icons-vue": {"src": "../../@element-plus/icons-vue/dist/index.js", "file": "@element-plus_icons-vue.js", "fileHash": "a9587561", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "8318ad75", "needsInterop": false}, "element-plus": {"src": "../../element-plus/es/index.mjs", "file": "element-plus.js", "fileHash": "a34d0a57", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "5d16a626", "needsInterop": false}, "pinia-plugin-persistedstate": {"src": "../../pinia-plugin-persistedstate/dist/index.js", "file": "pinia-plugin-persistedstate.js", "fileHash": "e44e4097", "needsInterop": false}, "trtc-sdk-v5": {"src": "../../trtc-sdk-v5/trtc.js", "file": "trtc-sdk-v5.js", "fileHash": "56f36ff9", "needsInterop": true}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "1644a5b0", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "6e68cc76", "needsInterop": false}}, "chunks": {"chunk-M7SMPNQH": {"file": "chunk-M7SMPNQH.js"}, "chunk-HX5C5AN6": {"file": "chunk-HX5C5AN6.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}