@echo off
chcp 65001 >nul

echo ========================================
echo 测试腾讯云直播平台 API
echo ========================================
echo.

echo [INFO] 测试后端健康检查...
curl -s http://localhost:8080/health
echo.
echo.

echo [INFO] 测试获取分类列表...
curl -s http://localhost:8080/api/categories
echo.
echo.

echo [INFO] 测试用户注册...
curl -s -X POST http://localhost:8080/api/auth/register ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"testuser\",\"email\":\"<EMAIL>\",\"password\":\"123456\"}"
echo.
echo.

echo [INFO] 测试用户登录...
curl -s -X POST http://localhost:8080/api/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"testuser\",\"password\":\"123456\"}"
echo.
echo.

echo [INFO] 测试获取我的直播间（应该返回404）...
curl -s http://localhost:8080/api/rooms/my ^
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
echo.
echo.

echo ========================================
echo 测试完成
echo ========================================

pause
