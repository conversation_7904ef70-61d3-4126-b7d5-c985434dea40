// 通用类型定义

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  timestamp: string
}

export interface ErrorResponse {
  success: boolean
  error: string
  message?: string
  code?: string
  timestamp: string
}

export interface PaginationParams {
  page?: number
  page_size?: number
}

export interface PaginationResponse<T = any> {
  data: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface UploadResponse {
  url: string
  filename: string
  size: number
  type: string
}

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
}

export interface NotificationItem {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message?: string
  duration?: number
  timestamp: number
}

// 加载状态
export interface LoadingState {
  loading: boolean
  error: string | null
}

// 表单验证规则
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}

// 表格列定义
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | string
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
}

// 菜单项
export interface MenuItem {
  id: string
  title: string
  icon?: string
  path?: string
  children?: MenuItem[]
  meta?: {
    requireAuth?: boolean
    roles?: string[]
    hidden?: boolean
  }
}

// 面包屑项
export interface BreadcrumbItem {
  title: string
  path?: string
}

// 统计数据
export interface StatItem {
  title: string
  value: number | string
  icon?: string
  color?: string
  trend?: {
    value: number
    type: 'up' | 'down'
  }
}

// 图表数据
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }[]
}

// 设备信息
export interface DeviceInfo {
  deviceId: string
  label: string
  kind: 'audioinput' | 'videoinput' | 'audiooutput'
}

// 媒体约束
export interface MediaConstraints {
  video?: boolean | MediaTrackConstraints
  audio?: boolean | MediaTrackConstraints
}

// 直播配置
export interface LiveConfig {
  video: {
    width: number
    height: number
    frameRate: number
    bitrate: number
  }
  audio: {
    sampleRate: number
    bitrate: number
    channels: number
  }
}

// 环境变量类型
export interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
  readonly VITE_APP_DESCRIPTION: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_WS_BASE_URL: string
  readonly VITE_TENCENT_LIVE_PUSH_DOMAIN: string
  readonly VITE_TENCENT_LIVE_PLAY_DOMAIN: string
  readonly VITE_TENCENT_WEB_RTC_APP_ID: string
  readonly VITE_TENCENT_WEB_RTC_SECRET_KEY: string
  readonly VITE_DEV_SERVER_PORT: string
  readonly VITE_DEV_SERVER_HOST: string
  readonly VITE_DEBUG: string
  readonly VITE_LOG_LEVEL: string
  readonly VITE_UPLOAD_MAX_SIZE: string
  readonly VITE_UPLOAD_ALLOWED_TYPES: string
  readonly VITE_ENABLE_CHAT: string
  readonly VITE_ENABLE_GIFT: string
  readonly VITE_ENABLE_RECORD: string
  readonly VITE_ENABLE_SCREENSHOT: string
}

export interface ImportMeta {
  readonly env: ImportMetaEnv
}
