# WebRTC 推流功能集成指南

## 🎯 功能概述

基于腾讯云 Web 推流 SDK (TRTC) 实现的浏览器内直接推流功能，支持：

- ✅ 浏览器内直接推流，无需安装 OBS 等软件
- ✅ 摄像头和麦克风权限管理
- ✅ 多种画质选择（480p/720p/1080p/4K）
- ✅ 屏幕共享推流
- ✅ 实时推流质量监控
- ✅ 音视频控制（静音/关闭视频）
- ✅ 设备切换（摄像头/麦克风）
- ✅ 浏览器兼容性检查
- ✅ 与传统 RTMP 推流并存

## 🏗️ 架构设计

### 前端组件架构
```
StudioView.vue (直播控制台)
├── 推流方式选择 (WebRTC/RTMP)
├── WebRTCPusher.vue (WebRTC 推流组件)
│   ├── DeviceManager (设备管理)
│   ├── WebRTCPusher (推流核心)
│   └── 质量监控和控制
└── RTMP 推流配置 (传统方式)
```

### 后端服务架构
```
LiveService
├── 生成 WebRTC 推流地址
├── 生成 WebRTC 播放地址
├── 支持多协议地址生成
└── 兼容现有 RTMP 流程
```

## 📋 已实现的功能

### 1. WebRTC 推流服务 (`webrtc-pusher.ts`)
- **初始化配置**: 支持腾讯云 TRTC SDK 配置
- **媒体流管理**: 摄像头、麦克风、屏幕共享
- **推流控制**: 开始/停止推流，音视频控制
- **质量监控**: 实时码率、帧率、延迟统计
- **设备切换**: 动态切换摄像头和麦克风
- **错误处理**: 完善的错误回调机制

### 2. 设备管理服务 (`device-manager.ts`)
- **权限管理**: 摄像头和麦克风权限请求
- **设备枚举**: 获取可用的音视频设备
- **兼容性检查**: 浏览器 WebRTC 支持检测
- **设备测试**: 摄像头和麦克风功能测试
- **网络质量**: 简单的网络质量评估

### 3. WebRTC 推流组件 (`WebRTCPusher.vue`)
- **设备配置界面**: 摄像头、麦克风、画质选择
- **实时预览**: 推流前设备预览
- **推流控制**: 开始/停止推流按钮
- **质量监控**: 实时统计数据显示
- **错误提示**: 用户友好的错误信息

### 4. 后端 WebRTC 支持
- **地址生成**: WebRTC 推流和播放地址
- **数据库字段**: 新增 WebRTC 相关字段
- **API 兼容**: 保持现有 API 结构不变

## 🔧 配置要求

### 腾讯云配置
需要在腾讯云控制台配置以下服务：

1. **实时音视频 TRTC**
   - 创建应用获取 SDKAppID
   - 配置推流域名
   - 生成 UserSig

2. **云直播 CSS**
   - 推流域名: `215131.push.tlivecloud.com`
   - 播放域名: `215131.liveplay.myqcloud.com`
   - WebRTC 推流支持

### 配置步骤

#### 1. 更新前端配置
编辑 `frontend/src/config/webrtc.ts`：

```typescript
export const WEBRTC_CONFIG = {
  SDK_APP_ID: 1400000000, // 替换为您的 SDKAppID
  PUSH_DOMAIN: '215131.push.tlivecloud.com',
  PLAY_DOMAIN: '215131.liveplay.myqcloud.com',
  // ... 其他配置
}
```

#### 2. 实现 UserSig 生成
在后端实现 UserSig 生成接口：

```go
// 添加到 backend/internal/handler/auth_handler.go
func (h *AuthHandler) GenerateUserSig(c *gin.Context) {
    userID := c.Param("userId")

    // 使用腾讯云 SDK 生成 UserSig
    userSig, err := h.trtcService.GenerateUserSig(userID)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }

    c.JSON(200, gin.H{"userSig": userSig})
}
```

#### 3. 更新前端 UserSig 获取
编辑 `frontend/src/config/webrtc.ts`：

```typescript
export async function generateUserSig(userId: string): Promise<string> {
  const response = await fetch(`/api/auth/user-sig/${userId}`)
  const data = await response.json()
  return data.userSig
}
```

## 🚀 使用流程

### 用户使用流程
1. **选择推流方式**: Web 推流 或 RTMP 推流
2. **设备权限**: 授权摄像头和麦克风访问
3. **设备配置**: 选择摄像头、麦克风、画质
4. **开始推流**: 点击开始推流按钮
5. **推流控制**: 静音、关闭视频、切换设备
6. **质量监控**: 查看实时推流统计

### 开发者集成流程
1. **安装依赖**: `npm install trtc-js-sdk`
2. **导入组件**: 在直播控制台中使用 WebRTCPusher
3. **配置参数**: 设置腾讯云 TRTC 参数
4. **测试功能**: 验证推流和播放功能

## 📱 浏览器兼容性

### 支持的浏览器
- ✅ **Chrome 90+**: 完全支持
- ✅ **Firefox 88+**: 完全支持  
- ✅ **Safari 14+**: 支持（部分限制）
- ✅ **Edge 90+**: 完全支持

### 功能支持矩阵
| 功能 | Chrome | Firefox | Safari | Edge |
|------|--------|---------|--------|------|
| 摄像头推流 | ✅ | ✅ | ✅ | ✅ |
| 麦克风推流 | ✅ | ✅ | ✅ | ✅ |
| 屏幕共享 | ✅ | ✅ | ⚠️ | ✅ |
| 设备切换 | ✅ | ✅ | ⚠️ | ✅ |
| 质量监控 | ✅ | ✅ | ✅ | ✅ |

## 🔍 故障排除

### 常见问题

1. **权限被拒绝**
   - 检查浏览器设置中的摄像头/麦克风权限
   - 确保使用 HTTPS 协议访问

2. **推流失败**
   - 检查腾讯云 TRTC 配置
   - 验证 UserSig 是否正确
   - 检查网络连接

3. **设备无法访问**
   - 确认设备未被其他应用占用
   - 检查设备驱动是否正常

4. **画质问题**
   - 调整画质设置
   - 检查网络带宽
   - 优化推流参数

### 调试方法

1. **开启控制台日志**
   ```javascript
   // 在浏览器控制台中查看详细日志
   localStorage.setItem('debug', 'true')
   ```

2. **检查推流统计**
   - 查看推流组件中的实时统计
   - 监控码率、帧率、丢包率

3. **网络质量测试**
   - 使用内置的网络质量检测
   - 检查延迟和带宽

## 📈 性能优化

### 推流优化
- **自适应码率**: 根据网络状况调整码率
- **分辨率适配**: 根据设备性能选择合适分辨率
- **帧率控制**: 平衡画质和性能

### 用户体验优化
- **快速启动**: 预加载设备列表
- **错误恢复**: 自动重连机制
- **状态提示**: 清晰的状态反馈

## 🔮 后续扩展

### 计划功能
- [ ] 美颜滤镜支持
- [ ] 多路推流
- [ ] 云端录制集成
- [ ] AI 降噪
- [ ] 虚拟背景
- [ ] 互动连麦

### 技术升级
- [ ] WebCodecs API 支持
- [ ] WebAssembly 优化
- [ ] PWA 支持
- [ ] 移动端适配

## 📞 技术支持

### 相关文档
- [腾讯云 Web 推流文档](https://cloud.tencent.com/document/product/267/97826)
- [TRTC Web SDK 文档](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/)
- [WebRTC 标准文档](https://webrtc.org/)

### 联系方式
- 技术问题: 查看浏览器控制台日志
- 配置问题: 检查腾讯云控制台设置
- 功能建议: 提交 Issue 或 PR

## 🧪 测试功能

### WebRTC 功能测试页面
访问 `/test/webrtc` 路径可以进行 WebRTC 功能测试：

1. **浏览器兼容性检查**
2. **设备列表获取**
3. **摄像头测试**
4. **麦克风测试**
5. **实时日志查看**

### 测试步骤
1. 启动前端服务：`npm run dev`
2. 访问：http://localhost:5173/test/webrtc
3. 按照页面提示进行各项测试
4. 查看测试结果和日志

## 📝 当前状态

### ✅ 已完成
- [x] TRTC SDK v5 集成
- [x] WebRTC 推流服务封装
- [x] 设备管理服务
- [x] WebRTC 推流组件
- [x] 直播控制台集成
- [x] 浏览器兼容性检查
- [x] 设备权限管理
- [x] 推流质量监控
- [x] 错误处理机制
- [x] 测试页面

### ⏳ 待完成
- [ ] UserSig 生成后端接口
- [ ] 腾讯云 TRTC 应用配置
- [ ] 推流地址生成优化
- [ ] 实际推流测试
- [ ] 播放端集成

### 🔧 需要配置
1. **腾讯云 TRTC 应用ID**：更新 `WEBRTC_CONFIG.SDK_APP_ID`
2. **UserSig 生成**：实现后端 UserSig 生成接口
3. **推流域名**：确认 WebRTC 推流域名配置

## 🚀 快速开始

### 1. 测试 WebRTC 功能
```bash
# 启动前端
cd frontend
npm run dev

# 访问测试页面
http://localhost:5173/test/webrtc
```

### 2. 体验 Web 推流
```bash
# 启动后端
cd backend
go run cmd/server/main.go

# 启动前端
cd frontend
npm run dev

# 访问直播控制台
http://localhost:5173/studio
```

### 3. 配置腾讯云参数
1. 编辑 `frontend/src/config/webrtc.ts`
2. 更新 `SDK_APP_ID` 为您的应用ID
3. 实现 UserSig 生成接口
4. 测试推流功能

---

**更新时间**: 2024年12月
**版本**: v1.0.0
**状态**: ✅ 开发完成，等待腾讯云配置
