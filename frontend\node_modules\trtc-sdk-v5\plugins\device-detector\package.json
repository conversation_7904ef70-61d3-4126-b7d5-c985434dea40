{"name": "@rtc-plugin/device-detector", "version": "5.10.1", "description": "TRTC Web SDK 5.x device detector plugin", "main": "./device-detector.esm.js", "module": "./device-detector.esm.js", "repository": {"type": "git", "url": "**************:LiteAVSDK/TRTC_Web.git"}, "homepage": "https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/TRTC.html#startPlugin", "keywords": ["detect", "device-detect", "webrtc", "TRTC", "rtc", "call", "video call", "audio call", "javascript", "video", "audio", "camera", "microphone"], "types": "./device-detector.esm.d.ts"}