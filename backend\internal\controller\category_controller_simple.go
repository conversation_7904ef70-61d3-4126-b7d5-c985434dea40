package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"live-streaming-platform/internal/model"
	"live-streaming-platform/internal/service"
)

// SimpleCategoryController 简化的分类控制器
type SimpleCategoryController struct {
	categoryService *service.CategoryService
}

// NewSimpleCategoryController 创建简化的分类控制器
func NewSimpleCategoryController(categoryService *service.CategoryService) *SimpleCategoryController {
	return &SimpleCategoryController{
		categoryService: categoryService,
	}
}

// GetCategories 获取分类列表
func (c *SimpleCategoryController) GetCategories(ctx *gin.Context) {
	categories, err := c.categoryService.GetCategories()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": categories})
}

// GetCategoryDetail 获取分类详情
func (c *SimpleCategoryController) GetCategoryDetail(ctx *gin.Context) {
	categoryIDStr := ctx.Param("id")
	categoryID, err := strconv.ParseUint(categoryIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "分类ID格式错误"})
		return
	}

	category, err := c.categoryService.GetCategoryByID(uint(categoryID))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": category})
}

// CreateCategory 创建分类（管理员功能）
func (c *SimpleCategoryController) CreateCategory(ctx *gin.Context) {
	var req struct {
		Name        string `json:"name" validate:"required,max=50"`
		Description string `json:"description" validate:"max=200"`
		Icon        string `json:"icon" validate:"max=255"`
		Sort        int    `json:"sort"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "请求参数错误: " + err.Error()})
		return
	}

	category := &model.Category{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		Sort:        req.Sort,
		IsActive:    true,
	}

	if err := c.categoryService.CreateCategory(category); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": category})
}
