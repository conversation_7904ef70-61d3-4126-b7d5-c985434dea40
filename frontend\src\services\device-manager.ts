// 设备管理服务
import TRTC from 'trtc-sdk-v5'

export interface DeviceInfo {
  deviceId: string
  label: string
  kind: string
}

export interface MediaPermissions {
  camera: boolean
  microphone: boolean
}

export class DeviceManager {
  private static instance: DeviceManager
  private devices: {
    cameras: DeviceInfo[]
    microphones: DeviceInfo[]
    speakers: DeviceInfo[]
  } = {
    cameras: [],
    microphones: [],
    speakers: []
  }

  private permissions: MediaPermissions = {
    camera: false,
    microphone: false
  }

  static getInstance(): DeviceManager {
    if (!DeviceManager.instance) {
      DeviceManager.instance = new DeviceManager()
    }
    return DeviceManager.instance
  }

  // 检查浏览器支持
  checkBrowserSupport(): {
    webrtc: boolean
    getUserMedia: boolean
    mediaDevices: boolean
  } {
    return {
      webrtc: TRTC.isSupported(),
      getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      mediaDevices: !!navigator.mediaDevices
    }
  }

  // 请求媒体权限
  async requestPermissions(constraints: {
    video?: boolean
    audio?: boolean
  } = { video: true, audio: true }): Promise<MediaPermissions> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      
      // 检查获得的权限
      const videoTracks = stream.getVideoTracks()
      const audioTracks = stream.getAudioTracks()

      this.permissions.camera = videoTracks.length > 0
      this.permissions.microphone = audioTracks.length > 0

      // 立即停止流，我们只是为了获取权限
      stream.getTracks().forEach(track => track.stop())

      console.log('媒体权限获取成功:', this.permissions)
      return this.permissions
    } catch (error) {
      console.error('媒体权限获取失败:', error)
      throw error
    }
  }

  // 获取设备列表
  async getDevices(): Promise<typeof this.devices> {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        throw new Error('浏览器不支持设备枚举')
      }

      const devices = await navigator.mediaDevices.enumerateDevices()
      
      this.devices = {
        cameras: [],
        microphones: [],
        speakers: []
      }

      devices.forEach(device => {
        const deviceInfo: DeviceInfo = {
          deviceId: device.deviceId,
          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
          kind: device.kind
        }

        switch (device.kind) {
          case 'videoinput':
            this.devices.cameras.push(deviceInfo)
            break
          case 'audioinput':
            this.devices.microphones.push(deviceInfo)
            break
          case 'audiooutput':
            this.devices.speakers.push(deviceInfo)
            break
        }
      })

      console.log('设备列表获取成功:', this.devices)
      return this.devices
    } catch (error) {
      console.error('获取设备列表失败:', error)
      throw error
    }
  }

  // 测试摄像头
  async testCamera(deviceId?: string): Promise<MediaStream> {
    try {
      const constraints: MediaStreamConstraints = {
        video: deviceId ? { deviceId: { exact: deviceId } } : true,
        audio: false
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      console.log('摄像头测试成功')
      return stream
    } catch (error) {
      console.error('摄像头测试失败:', error)
      throw error
    }
  }

  // 测试麦克风
  async testMicrophone(deviceId?: string): Promise<MediaStream> {
    try {
      const constraints: MediaStreamConstraints = {
        video: false,
        audio: deviceId ? { deviceId: { exact: deviceId } } : true
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      console.log('麦克风测试成功')
      return stream
    } catch (error) {
      console.error('麦克风测试失败:', error)
      throw error
    }
  }

  // 获取屏幕共享流
  async getScreenShare(): Promise<MediaStream> {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        throw new Error('浏览器不支持屏幕共享')
      }

      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          frameRate: { ideal: 30 }
        },
        audio: true
      })

      console.log('屏幕共享获取成功')
      return stream
    } catch (error) {
      console.error('屏幕共享获取失败:', error)
      throw error
    }
  }

  // 检查权限状态
  async checkPermissions(): Promise<MediaPermissions> {
    try {
      if (!navigator.permissions) {
        // 如果浏览器不支持 permissions API，尝试通过 getUserMedia 检查
        return await this.requestPermissions({ video: true, audio: true })
      }

      const cameraPermission = await navigator.permissions.query({ name: 'camera' as PermissionName })
      const microphonePermission = await navigator.permissions.query({ name: 'microphone' as PermissionName })

      this.permissions = {
        camera: cameraPermission.state === 'granted',
        microphone: microphonePermission.state === 'granted'
      }

      return this.permissions
    } catch (error) {
      console.warn('权限检查失败，尝试直接请求权限:', error)
      return await this.requestPermissions({ video: true, audio: true })
    }
  }

  // 获取当前权限状态
  getPermissions(): MediaPermissions {
    return { ...this.permissions }
  }

  // 获取推荐的视频质量配置
  getVideoQualityProfiles(): {
    [key: string]: {
      width: number
      height: number
      frameRate: number
      bitrate: number
      label: string
    }
  } {
    return {
      '480p': {
        width: 640,
        height: 480,
        frameRate: 15,
        bitrate: 500,
        label: '流畅 (480p)'
      },
      '720p': {
        width: 1280,
        height: 720,
        frameRate: 25,
        bitrate: 1000,
        label: '标清 (720p)'
      },
      '1080p': {
        width: 1920,
        height: 1080,
        frameRate: 30,
        bitrate: 2000,
        label: '高清 (1080p)'
      },
      '4k': {
        width: 3840,
        height: 2160,
        frameRate: 30,
        bitrate: 8000,
        label: '超清 (4K)'
      }
    }
  }

  // 检测网络质量
  async checkNetworkQuality(): Promise<{
    bandwidth: number
    latency: number
    quality: 'excellent' | 'good' | 'fair' | 'poor'
  }> {
    try {
      // 简单的网络质量检测
      const startTime = performance.now()
      
      // 发送一个小的请求来测试延迟
      await fetch('/api/health', { method: 'HEAD' })
      
      const latency = performance.now() - startTime

      // 根据延迟判断网络质量
      let quality: 'excellent' | 'good' | 'fair' | 'poor'
      if (latency < 50) {
        quality = 'excellent'
      } else if (latency < 100) {
        quality = 'good'
      } else if (latency < 200) {
        quality = 'fair'
      } else {
        quality = 'poor'
      }

      return {
        bandwidth: 0, // 实际项目中可以通过下载测试文件来估算带宽
        latency,
        quality
      }
    } catch (error) {
      console.error('网络质量检测失败:', error)
      return {
        bandwidth: 0,
        latency: 999,
        quality: 'poor'
      }
    }
  }

  // 获取浏览器信息
  getBrowserInfo(): {
    name: string
    version: string
    platform: string
    supported: boolean
  } {
    const userAgent = navigator.userAgent
    let name = 'Unknown'
    let version = 'Unknown'

    if (userAgent.includes('Chrome')) {
      name = 'Chrome'
      const match = userAgent.match(/Chrome\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (userAgent.includes('Firefox')) {
      name = 'Firefox'
      const match = userAgent.match(/Firefox\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      name = 'Safari'
      const match = userAgent.match(/Version\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (userAgent.includes('Edge')) {
      name = 'Edge'
      const match = userAgent.match(/Edge\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    }

    const support = this.checkBrowserSupport()
    const supported = support.webrtc && support.getUserMedia && support.mediaDevices

    return {
      name,
      version,
      platform: navigator.platform,
      supported
    }
  }
}
