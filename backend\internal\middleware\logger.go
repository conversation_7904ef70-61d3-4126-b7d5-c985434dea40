package middleware

import (
	"fmt"
	"io"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// LoggerMiddleware 日志中间件
func LoggerMiddleware(logFile string) gin.HandlerFunc {
	// 创建日志目录
	if err := os.MkdirAll("logs", 0755); err != nil {
		fmt.Printf("创建日志目录失败: %v\n", err)
	}

	// 打开日志文件
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		fmt.Printf("打开日志文件失败: %v\n", err)
		return gin.Logger()
	}

	// 创建多写入器，同时写入文件和控制台
	multiWriter := io.MultiWriter(file, os.Stdout)

	// 配置logrus
	logger := logrus.New()
	logger.SetOutput(multiWriter)
	logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: time.RFC3339,
	})

	return gin.LoggerWithConfig(gin.LoggerConfig{
		Formatter: func(param gin.LogFormatterParams) string {
			// 记录请求日志
			logger.WithFields(logrus.Fields{
				"timestamp":  param.TimeStamp.Format(time.RFC3339),
				"status":     param.StatusCode,
				"latency":    param.Latency,
				"client_ip":  param.ClientIP,
				"method":     param.Method,
				"path":       param.Path,
				"error":      param.ErrorMessage,
				"user_agent": param.Request.UserAgent(),
				"request_id": param.Request.Header.Get("X-Request-ID"),
			}).Info("HTTP Request")

			return ""
		},
	})
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}

		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// RecoveryMiddleware 恢复中间件
func RecoveryMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		// 记录panic日志
		logger := logrus.New()
		logger.WithFields(logrus.Fields{
			"timestamp":  time.Now().Format(time.RFC3339),
			"panic":      recovered,
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"client_ip":  c.ClientIP(),
			"user_agent": c.Request.UserAgent(),
			"request_id": c.GetString("request_id"),
		}).Error("Panic recovered")

		c.JSON(500, gin.H{
			"error": "服务器内部错误",
		})
	})
}
