import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { setPageTitle } from '@/utils'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      title: '首页',
      requireAuth: false
    }
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: {
      title: '登录',
      requireAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('@/views/auth/RegisterView.vue'),
    meta: {
      title: '注册',
      requireAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/live',
    name: 'live',
    component: () => import('@/views/live/LiveListView.vue'),
    meta: {
      title: '直播列表',
      requireAuth: false
    }
  },
  {
    path: '/live/:id',
    name: 'live-room',
    component: () => import('@/views/live/LiveRoomView.vue'),
    meta: {
      title: '直播间',
      requireAuth: false
    }
  },
  {
    path: '/studio',
    name: 'studio',
    component: () => import('@/views/studio/StudioView.vue'),
    meta: {
      title: '直播控制台',
      requireAuth: true,
      roles: ['streamer', 'admin']
    }
  },
  {
    path: '/profile',
    name: 'profile',
    component: () => import('@/views/user/ProfileView.vue'),
    meta: {
      title: '个人资料',
      requireAuth: true
    }
  },
  {
    path: '/user/:id',
    name: 'user-profile',
    component: () => import('@/views/user/UserProfileView.vue'),
    meta: {
      title: '用户资料',
      requireAuth: false
    }
  },
  {
    path: '/settings',
    name: 'settings',
    component: () => import('@/views/user/SettingsView.vue'),
    meta: {
      title: '设置',
      requireAuth: true
    }
  },
  {
    path: '/test/webrtc',
    name: 'webrtc-test',
    component: () => import('@/views/test/WebRTCTest.vue'),
    meta: {
      title: 'WebRTC 测试',
      requireAuth: false
    }
  },
  {
    path: '/admin',
    name: 'admin',
    component: () => import('@/views/admin/AdminView.vue'),
    meta: {
      title: '管理后台',
      requireAuth: true,
      roles: ['admin']
    },
    children: [
      {
        path: 'users',
        name: 'admin-users',
        component: () => import('@/views/admin/UsersView.vue'),
        meta: {
          title: '用户管理',
          requireAuth: true,
          roles: ['admin']
        }
      },
      {
        path: 'rooms',
        name: 'admin-rooms',
        component: () => import('@/views/admin/RoomsView.vue'),
        meta: {
          title: '直播间管理',
          requireAuth: true,
          roles: ['admin']
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'not-found',
    component: () => import('@/views/error/NotFoundView.vue'),
    meta: {
      title: '页面不存在'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    setPageTitle(to.meta.title as string)
  }

  // 检查是否需要认证
  if (to.meta.requireAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next({
        name: 'login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查token是否过期
    if (!userStore.checkTokenExpiry()) {
      next({
        name: 'login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查角色权限
    if (to.meta.roles && Array.isArray(to.meta.roles)) {
      const userRole = userStore.user?.role
      if (!userRole || !to.meta.roles.includes(userRole)) {
        // 权限不足，跳转到首页
        next({ name: 'home' })
        return
      }
    }
  }

  // 已登录用户访问登录/注册页面，跳转到首页
  if (to.meta.hideForAuth && userStore.isLoggedIn) {
    next({ name: 'home' })
    return
  }

  next()
})

export default router
