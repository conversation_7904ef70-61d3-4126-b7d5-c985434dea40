<template>
  <div class="live-room-container">
    <div class="live-room-layout" v-if="room">
      <!-- 视频播放区域 -->
      <div class="video-section">
        <div class="video-container">
          <video
            ref="videoPlayer"
            class="live-video"
            controls
            autoplay
            muted
            :poster="room.cover"
          >
            您的浏览器不支持视频播放
          </video>

          <!-- 直播状态覆盖层 -->
          <div class="video-overlay" v-if="room.status !== 1">
            <div class="offline-message">
              <el-icon size="48"><VideoCamera /></el-icon>
              <h3>{{ room.status === 0 ? '主播暂未开播' : '直播已结束' }}</h3>
              <p>{{ room.status === 0 ? '请稍后再来' : '感谢观看' }}</p>
            </div>
          </div>
        </div>

        <!-- 直播间信息 -->
        <div class="room-info">
          <div class="room-header">
            <h1>{{ room.title }}</h1>
            <div class="room-actions">
              <el-button @click="toggleLike" :type="isLiked ? 'danger' : 'default'">
                <el-icon><Star /></el-icon>
                {{ room.like_count }}
              </el-button>
              <el-button @click="shareRoom">
                <el-icon><Share /></el-icon>
                分享
              </el-button>
              <el-dropdown @command="handleMoreAction">
                <el-button>
                  <el-icon><More /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="report">举报</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="room-meta">
            <div class="streamer-info">
              <el-avatar :src="room.user?.avatar" :size="40">
                {{ room.user?.nickname?.charAt(0) }}
              </el-avatar>
              <div class="streamer-details">
                <h4>{{ room.user?.nickname }}</h4>
                <p>{{ room.user?.follower_count }} 粉丝</p>
              </div>
              <el-button
                v-if="!isOwnRoom"
                type="primary"
                size="small"
                @click="toggleFollow"
              >
                {{ isFollowing ? '已关注' : '关注' }}
              </el-button>
            </div>

            <div class="room-stats">
              <div class="stat-item">
                <span class="label">观看</span>
                <span class="value">{{ formatNumber(room.viewer_count) }}</span>
              </div>
              <div class="stat-item">
                <span class="label">点赞</span>
                <span class="value">{{ formatNumber(room.like_count) }}</span>
              </div>
            </div>
          </div>

          <div class="room-description" v-if="room.description">
            <p>{{ room.description }}</p>
          </div>

          <div class="room-tags" v-if="room.tags">
            <el-tag
              v-for="tag in room.tags.split(',')"
              :key="tag"
              size="small"
              class="tag-item"
            >
              {{ tag.trim() }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 聊天区域 -->
      <div class="chat-section">
        <div class="chat-container">
          <div class="chat-header">
            <h3>聊天室</h3>
            <span class="online-count">{{ room.viewer_count }} 人在线</span>
          </div>

          <div class="chat-messages" ref="chatMessages">
            <div
              v-for="message in messages"
              :key="message.id"
              class="message-item"
              :class="{ 'system-message': message.type === 2 }"
            >
              <div class="message-user" v-if="message.type === 1">
                <el-avatar :src="message.user?.avatar" :size="24">
                  {{ message.user?.nickname?.charAt(0) }}
                </el-avatar>
                <span class="username">{{ message.user?.nickname }}</span>
              </div>
              <div class="message-content">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.created_at) }}</div>
            </div>
          </div>

          <div class="chat-input" v-if="userStore.isLoggedIn && room.allow_chat">
            <el-input
              v-model="messageInput"
              placeholder="说点什么..."
              @keyup.enter="sendMessage"
              :disabled="sending"
            >
              <template #append>
                <el-button @click="sendMessage" :loading="sending">发送</el-button>
              </template>
            </el-input>
          </div>

          <div class="chat-login-tip" v-else-if="!userStore.isLoggedIn">
            <p>
              <el-link @click="$router.push('/login')">登录</el-link>
              后可参与聊天
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-container" v-else-if="loading">
      <el-loading-spinner />
      <p>加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div class="error-container" v-else>
      <el-icon size="48"><Warning /></el-icon>
      <h3>直播间不存在</h3>
      <p>该直播间可能已被删除或不存在</p>
      <el-button @click="$router.push('/live')">返回直播列表</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { VideoCamera, Star, Share, More, Warning } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { formatNumber, formatRelativeTime } from '@/utils'
import { roomApi, chatApi, interactionApi } from '@/api/live'
import type { Room, Message } from '@/types/live'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const room = ref<Room | null>(null)
const messages = ref<Message[]>([])
const messageInput = ref('')
const loading = ref(true)
const sending = ref(false)
const isLiked = ref(false)
const isFollowing = ref(false)

// DOM引用
const videoPlayer = ref<HTMLVideoElement>()
const chatMessages = ref<HTMLDivElement>()

// 计算属性
const roomId = computed(() => parseInt(route.params.id as string))
const isOwnRoom = computed(() => room.value?.user_id === userStore.user?.id)

// 方法
const loadRoomData = async () => {
  try {
    loading.value = true
    const response = await roomApi.getRoomDetail(roomId.value)
    room.value = response.data

    // 如果是直播中，初始化视频播放
    if (room.value?.status === 1) {
      await nextTick()
      initVideoPlayer()
    }

    // 加载聊天消息
    await loadMessages()

  } catch (error) {
    console.error('加载直播间失败:', error)
    ElMessage.error('加载直播间失败')
  } finally {
    loading.value = false
  }
}

const loadMessages = async () => {
  try {
    const response = await chatApi.getMessages(roomId.value, {
      page: 1,
      page_size: 50
    })
    messages.value = response.data.messages

    // 滚动到底部
    await nextTick()
    scrollToBottom()

  } catch (error) {
    console.error('加载消息失败:', error)
  }
}

const initVideoPlayer = () => {
  if (!videoPlayer.value || !room.value) return

  try {
    // 设置视频源
    videoPlayer.value.src = room.value.hls_play_url
    videoPlayer.value.load()
  } catch (error) {
    console.error('初始化视频播放器失败:', error)
  }
}

const sendMessage = async () => {
  if (!messageInput.value.trim() || !userStore.isLoggedIn) return

  try {
    sending.value = true
    const response = await chatApi.sendMessage(roomId.value, {
      content: messageInput.value.trim()
    })

    // 添加到消息列表
    messages.value.push(response.data)
    messageInput.value = ''

    // 滚动到底部
    await nextTick()
    scrollToBottom()

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  } finally {
    sending.value = false
  }
}

const toggleLike = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    if (isLiked.value) {
      await interactionApi.unlikeRoom(roomId.value)
      isLiked.value = false
      if (room.value) room.value.like_count--
    } else {
      await interactionApi.likeRoom(roomId.value)
      isLiked.value = true
      if (room.value) room.value.like_count++
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const toggleFollow = async () => {
  if (!userStore.isLoggedIn || !room.value?.user) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    // 这里应该调用关注/取消关注API
    isFollowing.value = !isFollowing.value
    ElMessage.success(isFollowing.value ? '关注成功' : '取消关注成功')
  } catch (error) {
    console.error('关注操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const shareRoom = async () => {
  try {
    const url = window.location.href
    await navigator.clipboard.writeText(url)
    ElMessage.success('链接已复制到剪贴板')

    // 记录分享
    await interactionApi.shareRoom(roomId.value)
  } catch (error) {
    console.error('分享失败:', error)
    ElMessage.error('分享失败')
  }
}

const handleMoreAction = async (command: string) => {
  switch (command) {
    case 'report':
      await reportRoom()
      break
  }
}

const reportRoom = async () => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入举报原因',
      '举报直播间',
      {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputPlaceholder: '请详细描述举报原因'
      }
    )

    await interactionApi.reportRoom(roomId.value, {
      reason: reason || '其他',
      description: reason
    })

    ElMessage.success('举报已提交')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('举报失败:', error)
      ElMessage.error('举报失败')
    }
  }
}

const scrollToBottom = () => {
  if (chatMessages.value) {
    chatMessages.value.scrollTop = chatMessages.value.scrollHeight
  }
}

const formatTime = (time: string) => {
  return formatRelativeTime(time)
}

// 生命周期
onMounted(() => {
  loadRoomData()
})

onUnmounted(() => {
  // 清理资源
  if (videoPlayer.value) {
    videoPlayer.value.pause()
    videoPlayer.value.src = ''
  }
})
</script>

<style scoped>
.live-room-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.live-room-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

/* 视频区域样式 */
.video-section {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-container {
  position: relative;
  background: #000;
  aspect-ratio: 16/9;
}

.live-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.offline-message {
  text-align: center;
}

.offline-message h3 {
  margin: 16px 0 8px 0;
  font-size: 20px;
}

.offline-message p {
  margin: 0;
  color: #ccc;
}

/* 直播间信息样式 */
.room-info {
  padding: 20px;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.room-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  flex: 1;
  margin-right: 16px;
}

.room-actions {
  display: flex;
  gap: 8px;
}

.room-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.streamer-details h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.streamer-details p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.room-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-item .label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-item .value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.room-description {
  margin-bottom: 16px;
}

.room-description p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.room-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin: 0;
}

/* 聊天区域样式 */
.chat-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: fit-content;
  max-height: calc(100vh - 40px);
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.online-count {
  font-size: 12px;
  color: #909399;
}

.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-item.system-message {
  align-items: center;
  color: #909399;
  font-size: 12px;
}

.message-user {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  font-size: 12px;
  font-weight: 500;
  color: #409eff;
}

.message-content {
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
  word-wrap: break-word;
}

.message-time {
  font-size: 10px;
  color: #c0c4cc;
}

.chat-input {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.chat-login-tip {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #e4e7ed;
}

.chat-login-tip p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

/* 加载和错误状态样式 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.error-container h3 {
  margin: 16px 0 8px 0;
  color: #303133;
}

.error-container p {
  margin: 0 0 24px 0;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .live-room-layout {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chat-container {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .live-room-layout {
    padding: 16px;
  }

  .room-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .room-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .room-stats {
    justify-content: center;
  }

  .room-actions {
    justify-content: center;
  }
}
</style>
