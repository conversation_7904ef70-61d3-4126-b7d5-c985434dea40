@echo off
chcp 65001 >nul

echo ========================================
echo 腾讯云直播平台 - 启动服务
echo ========================================
echo.

echo [INFO] 启动后端服务...
start "后端服务" cmd /k "cd backend && bin\server.exe"

echo [INFO] 等待后端启动...
timeout /t 3 /nobreak >nul

echo [INFO] 启动前端服务...
start "前端服务" cmd /k "cd frontend && npm run dev"

echo.
echo ========================================
echo 🎉 服务启动完成！
echo ========================================
echo.
echo 📱 访问地址：
echo   前端应用: http://localhost:5173
echo   后端API: http://localhost:8080
echo.
echo 💡 使用说明：
echo   1. 访问前端地址注册用户
echo   2. 登录后创建直播间
echo   3. 获取推流地址开始直播
echo.
echo 🔧 腾讯云配置：
echo   SecretId: AKIDYw4vVpsfCSSBdojNkrjMiofFYbOu9CyU
echo   推流域名: 215131.push.tlivecloud.com
echo   播放域名: 215131.liveplay.myqcloud.com
echo.
echo ⚠️  注意：关闭此窗口不会停止服务
echo    要停止服务请关闭对应的命令行窗口
echo.

pause
