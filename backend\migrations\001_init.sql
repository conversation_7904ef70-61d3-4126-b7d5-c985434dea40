-- 初始化数据库表结构

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `nickname` varchar(50) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `gender` int DEFAULT '0',
  `birthday` datetime(3) DEFAULT NULL,
  `bio` varchar(500) DEFAULT NULL,
  `status` int DEFAULT '1',
  `is_verified` tinyint(1) DEFAULT '0',
  `role` varchar(20) DEFAULT 'user',
  `level` int DEFAULT '1',
  `follow_count` int DEFAULT '0',
  `follower_count` int DEFAULT '0',
  `stream_count` int DEFAULT '0',
  `total_watch_time` int DEFAULT '0',
  `last_login_at` datetime(3) DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_users_username` (`username`),
  UNIQUE KEY `idx_users_email` (`email`),
  KEY `idx_users_deleted_at` (`deleted_at`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 关注关系表
CREATE TABLE IF NOT EXISTS `follows` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `follower_id` bigint unsigned NOT NULL,
  `followed_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_follows_unique` (`follower_id`,`followed_id`),
  KEY `idx_follows_deleted_at` (`deleted_at`),
  KEY `idx_follows_follower_id` (`follower_id`),
  KEY `idx_follows_followed_id` (`followed_id`),
  CONSTRAINT `fk_follows_follower` FOREIGN KEY (`follower_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_follows_followed` FOREIGN KEY (`followed_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 分类表
CREATE TABLE IF NOT EXISTS `categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `sort` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `room_count` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_categories_name` (`name`),
  KEY `idx_categories_deleted_at` (`deleted_at`),
  KEY `idx_categories_is_active` (`is_active`),
  KEY `idx_categories_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 直播间表
CREATE TABLE IF NOT EXISTS `rooms` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL,
  `title` varchar(200) NOT NULL,
  `description` varchar(1000) DEFAULT NULL,
  `cover` varchar(255) DEFAULT NULL,
  `stream_key` varchar(100) NOT NULL,
  `push_url` varchar(500) DEFAULT NULL,
  `play_url` varchar(500) DEFAULT NULL,
  `hls_play_url` varchar(500) DEFAULT NULL,
  `flv_play_url` varchar(500) DEFAULT NULL,
  `status` int DEFAULT '0',
  `is_private` tinyint(1) DEFAULT '0',
  `password` varchar(50) DEFAULT NULL,
  `category_id` bigint unsigned DEFAULT '1',
  `tags` varchar(500) DEFAULT NULL,
  `viewer_count` int DEFAULT '0',
  `max_viewer_count` int DEFAULT '0',
  `like_count` int DEFAULT '0',
  `share_count` int DEFAULT '0',
  `message_count` int DEFAULT '0',
  `gift_count` int DEFAULT '0',
  `started_at` datetime(3) DEFAULT NULL,
  `ended_at` datetime(3) DEFAULT NULL,
  `duration` int DEFAULT '0',
  `allow_chat` tinyint(1) DEFAULT '1',
  `allow_gift` tinyint(1) DEFAULT '1',
  `chat_mode` int DEFAULT '0',
  `quality_level` int DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_rooms_stream_key` (`stream_key`),
  KEY `idx_rooms_deleted_at` (`deleted_at`),
  KEY `idx_rooms_user_id` (`user_id`),
  KEY `idx_rooms_category_id` (`category_id`),
  KEY `idx_rooms_status` (`status`),
  KEY `idx_rooms_created_at` (`created_at`),
  CONSTRAINT `fk_rooms_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_rooms_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 消息表
CREATE TABLE IF NOT EXISTS `messages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `room_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `content` varchar(1000) NOT NULL,
  `type` int DEFAULT '1',
  `is_deleted` tinyint(1) DEFAULT '0',
  `deleted_by` bigint unsigned DEFAULT '0',
  `client_ip` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_messages_deleted_at` (`deleted_at`),
  KEY `idx_messages_room_id` (`room_id`),
  KEY `idx_messages_user_id` (`user_id`),
  KEY `idx_messages_created_at` (`created_at`),
  CONSTRAINT `fk_messages_room` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_messages_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 观众记录表
CREATE TABLE IF NOT EXISTS `viewers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `room_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `joined_at` datetime(3) DEFAULT NULL,
  `left_at` datetime(3) DEFAULT NULL,
  `duration` int DEFAULT '0',
  `client_ip` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_viewers_deleted_at` (`deleted_at`),
  KEY `idx_viewers_room_id` (`room_id`),
  KEY `idx_viewers_user_id` (`user_id`),
  KEY `idx_viewers_joined_at` (`joined_at`),
  CONSTRAINT `fk_viewers_room` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_viewers_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 录制记录表
CREATE TABLE IF NOT EXISTS `records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `room_id` bigint unsigned NOT NULL,
  `filename` varchar(255) NOT NULL,
  `file_url` varchar(500) DEFAULT NULL,
  `file_size` bigint DEFAULT '0',
  `duration` int DEFAULT '0',
  `format` varchar(10) DEFAULT 'mp4',
  `quality` varchar(20) DEFAULT '720p',
  `status` int DEFAULT '0',
  `started_at` datetime(3) DEFAULT NULL,
  `finished_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_records_deleted_at` (`deleted_at`),
  KEY `idx_records_room_id` (`room_id`),
  KEY `idx_records_status` (`status`),
  KEY `idx_records_started_at` (`started_at`),
  CONSTRAINT `fk_records_room` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认分类数据
INSERT INTO `categories` (`name`, `description`, `icon`, `sort`, `is_active`, `room_count`) VALUES
('游戏', '游戏直播', 'game', 1, 1, 0),
('娱乐', '娱乐直播', 'entertainment', 2, 1, 0),
('教育', '教育直播', 'education', 3, 1, 0),
('科技', '科技直播', 'tech', 4, 1, 0),
('生活', '生活直播', 'life', 5, 1, 0),
('其他', '其他分类', 'other', 99, 1, 0)
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`);

-- 创建管理员用户（密码：admin123456）
INSERT INTO `users` (`username`, `email`, `password`, `nickname`, `role`, `status`, `is_verified`) VALUES
('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq/3/Hm', '管理员', 'admin', 1, 1)
ON DUPLICATE KEY UPDATE `username` = VALUES(`username`);
