# 前端直播开播指南

## 概述

本指南详细介绍了如何在前端使用直播功能，包括开播、观看直播、聊天互动等功能。

## 开播流程

### 1. 用户权限要求

开播需要满足以下条件：
- 用户已登录
- 用户角色为 `streamer` 或 `admin`
- 用户通过实名认证（可选）

### 2. 访问直播控制台

登录后，如果用户有开播权限，在首页头部会显示"开始直播"按钮：

```vue
<el-button
  v-if="userStore.isStreamer"
  type="primary"
  @click="$router.push('/studio')"
>
  开始直播
</el-button>
```

点击按钮跳转到直播控制台页面 `/studio`。

### 3. 创建直播间

如果用户还没有直播间，需要先创建：

#### 3.1 填写直播间信息
- **直播标题**：必填，1-100字符
- **直播描述**：可选，最多500字符
- **直播分类**：必选，从预设分类中选择
- **标签**：可选，用逗号分隔多个标签
- **画质等级**：选择直播画质（480p/720p/1080p/4K）
- **功能设置**：
  - 允许聊天：是否开启聊天功能
  - 允许礼物：是否接受观众送礼
  - 私密直播间：是否设为私密（需要密码）

#### 3.2 创建直播间
```typescript
const createRoom = async () => {
  try {
    await roomFormRef.value.validate()
    creating.value = true

    const response = await roomApi.createRoom(roomForm)
    currentRoom.value = response.data
    
    ElMessage.success('直播间创建成功')
  } catch (error) {
    ElMessage.error('创建直播间失败')
  } finally {
    creating.value = false
  }
}
```

### 4. 配置推流

创建直播间后，系统会自动生成推流配置：

#### 4.1 推流信息
- **推流地址**：RTMP推流URL
- **流密钥**：Stream Key，用于OBS等推流软件
- **播放地址**：包含RTMP、HLS、FLV三种格式

#### 4.2 复制推流信息
```typescript
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}
```

### 5. 配置推流软件

#### 5.1 OBS Studio配置
1. 打开OBS Studio
2. 点击"设置" → "推流"
3. 服务选择"自定义"
4. 服务器填入推流地址
5. 推流密钥填入流密钥
6. 点击"确定"保存设置

#### 5.2 其他推流软件
- **XSplit**：类似OBS的配置方式
- **移动端**：可使用手机直播APP，配置RTMP推流

### 6. 开始直播

#### 6.1 启动直播
```typescript
const startLive = async () => {
  try {
    await ElMessageBox.confirm('确定要开始直播吗？', '开始直播')
    
    starting.value = true
    await roomApi.startLive(currentRoom.value.id)
    
    currentRoom.value.status = 1
    startDurationTimer() // 开始计时
    initPreview() // 初始化预览
    
    ElMessage.success('直播已开始')
  } catch (error) {
    ElMessage.error('开始直播失败')
  } finally {
    starting.value = false
  }
}
```

#### 6.2 直播状态监控
- 实时观众数量
- 直播时长计时
- 点赞数、消息数统计
- 最高观众数记录

### 7. 直播管理

#### 7.1 实时预览
```vue
<video
  ref="previewVideo"
  class="preview-video"
  controls
  muted
  autoplay
>
  您的浏览器不支持视频播放
</video>
```

#### 7.2 结束直播
```typescript
const stopLive = async () => {
  try {
    await ElMessageBox.confirm('确定要结束直播吗？结束后无法恢复。', '结束直播')
    
    stopping.value = true
    await roomApi.stopLive(currentRoom.value.id)
    
    currentRoom.value.status = 3
    stopDurationTimer() // 停止计时
    
    ElMessage.success('直播已结束')
  } catch (error) {
    ElMessage.error('结束直播失败')
  } finally {
    stopping.value = false
  }
}
```

## 观看直播

### 1. 访问直播间

通过以下方式访问直播间：
- 首页热门直播列表
- 直播列表页面 `/live`
- 直接访问直播间URL `/live/:id`

### 2. 视频播放

#### 2.1 自动播放
```vue
<video
  ref="videoPlayer"
  class="live-video"
  controls
  autoplay
  muted
  :poster="room.cover"
>
  您的浏览器不支持视频播放
</video>
```

#### 2.2 多格式支持
- **HLS (.m3u8)**：适合移动端和Web播放
- **FLV**：低延迟播放
- **RTMP**：专业播放器支持

### 3. 互动功能

#### 3.1 聊天功能
```typescript
const sendMessage = async () => {
  try {
    const response = await chatApi.sendMessage(roomId.value, {
      content: messageInput.value.trim()
    })
    
    messages.value.push(response.data)
    messageInput.value = ''
    scrollToBottom()
  } catch (error) {
    ElMessage.error('发送消息失败')
  }
}
```

#### 3.2 点赞功能
```typescript
const toggleLike = async () => {
  try {
    if (isLiked.value) {
      await interactionApi.unlikeRoom(roomId.value)
      room.value.like_count--
    } else {
      await interactionApi.likeRoom(roomId.value)
      room.value.like_count++
    }
    isLiked.value = !isLiked.value
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
```

#### 3.3 关注主播
```typescript
const toggleFollow = async () => {
  try {
    // 调用关注/取消关注API
    isFollowing.value = !isFollowing.value
    ElMessage.success(isFollowing.value ? '关注成功' : '取消关注成功')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
```

## 技术实现要点

### 1. 状态管理

使用Pinia管理用户状态和直播状态：
```typescript
const userStore = useUserStore()
const isStreamer = computed(() => userStore.isStreamer)
```

### 2. 实时通信

使用WebSocket实现实时聊天和状态更新：
```typescript
// WebSocket连接管理
const ws = new WebSocket(import.meta.env.VITE_WS_BASE_URL)
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  handleWebSocketMessage(data)
}
```

### 3. 视频播放优化

- 自适应码率
- 断线重连
- 缓冲优化
- 移动端适配

### 4. 错误处理

完善的错误处理机制：
```typescript
try {
  // API调用
} catch (error) {
  console.error('操作失败:', error)
  ElMessage.error('操作失败，请重试')
}
```

## 注意事项

1. **浏览器兼容性**：确保浏览器支持WebRTC和现代视频标准
2. **网络要求**：推流需要稳定的上行带宽
3. **设备权限**：需要摄像头和麦克风权限
4. **内容审核**：遵守平台内容规范
5. **性能优化**：大量观众时注意性能优化

## 故障排除

### 常见问题

1. **推流失败**
   - 检查网络连接
   - 验证推流地址和密钥
   - 确认防火墙设置

2. **播放卡顿**
   - 检查网络带宽
   - 尝试切换播放格式
   - 降低视频质量

3. **聊天消息不显示**
   - 检查WebSocket连接
   - 确认用户登录状态
   - 验证聊天权限设置

通过以上指南，用户可以完整地使用前端直播功能进行开播和观看直播。
