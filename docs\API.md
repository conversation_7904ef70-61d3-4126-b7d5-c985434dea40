# API接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **认证方式**: <PERSON><PERSON> (JWT)
- **Content-Type**: `application/json`

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2023-01-01T00:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误类型",
  "message": "详细错误信息",
  "code": "ERROR_CODE",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

## 认证接口

### 用户注册
```http
POST /auth/register
```

**请求参数**:
```json
{
  "username": "string",     // 用户名，3-50字符
  "email": "string",        // 邮箱地址
  "password": "string",     // 密码，最少6字符
  "nickname": "string"      // 昵称，可选
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2023-01-02T00:00:00Z",
    "user": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户",
      "email": "<EMAIL>",
      "avatar": "",
      "role": "user",
      "created_at": "2023-01-01T00:00:00Z"
    }
  }
}
```

### 用户登录
```http
POST /auth/login
```

**请求参数**:
```json
{
  "username": "string",     // 用户名或邮箱
  "password": "string"      // 密码
}
```

**响应示例**: 同注册接口

### 刷新令牌
```http
POST /auth/refresh
Authorization: Bearer <token>
```

**响应示例**: 同登录接口

## 用户管理接口

### 获取当前用户信息
```http
GET /users/profile
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "gender": 1,
    "bio": "这是个人简介",
    "role": "user",
    "level": 5,
    "follow_count": 100,
    "follower_count": 200,
    "stream_count": 10,
    "created_at": "2023-01-01T00:00:00Z",
    "last_login_at": "2023-01-01T12:00:00Z"
  }
}
```

### 获取指定用户信息
```http
GET /users/{id}/profile
```

**路径参数**:
- `id`: 用户ID

**响应示例**: 同获取当前用户信息

### 更新用户资料
```http
PUT /users/profile
Authorization: Bearer <token>
```

**请求参数**:
```json
{
  "nickname": "string",     // 昵称，可选
  "avatar": "string",       // 头像URL，可选
  "gender": 1,              // 性别：0未知，1男，2女
  "birthday": "1990-01-01", // 生日，可选
  "bio": "string"           // 个人简介，可选
}
```

### 修改密码
```http
PUT /users/password
Authorization: Bearer <token>
```

**请求参数**:
```json
{
  "old_password": "string", // 原密码
  "new_password": "string"  // 新密码
}
```

### 搜索用户
```http
GET /users/search?keyword={keyword}&page={page}&page_size={page_size}
```

**查询参数**:
- `keyword`: 搜索关键词
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应示例**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "testuser",
        "nickname": "测试用户",
        "avatar": "https://example.com/avatar.jpg",
        "role": "user",
        "level": 5,
        "follower_count": 200
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  }
}
```

## 关注关系接口

### 关注用户
```http
POST /users/{id}/follow
Authorization: Bearer <token>
```

**路径参数**:
- `id`: 被关注用户ID

### 取消关注
```http
POST /users/{id}/unfollow
Authorization: Bearer <token>
```

**路径参数**:
- `id`: 被取消关注用户ID

### 获取粉丝列表
```http
GET /users/followers?page={page}&page_size={page_size}
GET /users/{id}/followers?page={page}&page_size={page_size}
Authorization: Bearer <token> (获取自己的粉丝时需要)
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 2,
        "username": "follower1",
        "nickname": "粉丝1",
        "avatar": "https://example.com/avatar2.jpg",
        "role": "user",
        "level": 3
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20,
    "total_pages": 3
  }
}
```

### 获取关注列表
```http
GET /users/following?page={page}&page_size={page_size}
GET /users/{id}/following?page={page}&page_size={page_size}
Authorization: Bearer <token> (获取自己的关注时需要)
```

**响应示例**: 同获取粉丝列表

## 直播间管理接口

### 创建直播间
```http
POST /rooms
Authorization: Bearer <token>
```

**请求参数**:
```json
{
  "title": "string",        // 直播间标题
  "description": "string",  // 直播间描述，可选
  "cover": "string",        // 封面图URL，可选
  "category_id": 1,         // 分类ID
  "tags": "游戏,娱乐",      // 标签，可选
  "is_private": false,      // 是否私密直播间
  "password": "string",     // 直播间密码，私密时必填
  "allow_chat": true,       // 是否允许聊天
  "allow_gift": true,       // 是否允许礼物
  "chat_mode": 0,           // 聊天模式：0所有人，1关注者，2禁言
  "quality_level": 2        // 画质等级：1流畅，2标清，3高清，4超清
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "我的直播间",
    "stream_key": "live_123456789",
    "push_url": "rtmp://push.example.com/live/stream_key?auth=xxx",
    "play_url": "rtmp://play.example.com/live/stream_key",
    "hls_play_url": "http://play.example.com/live/stream_key.m3u8",
    "flv_play_url": "http://play.example.com/live/stream_key.flv",
    "status": 0,
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

### 获取直播间列表
```http
GET /rooms?page={page}&page_size={page_size}&category={category}&status={status}
```

**查询参数**:
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `category`: 分类ID，可选
- `status`: 直播状态，可选（0未开播，1直播中，2暂停，3结束）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "rooms": [
      {
        "id": 1,
        "title": "王者荣耀巅峰赛",
        "cover": "https://example.com/cover.jpg",
        "status": 1,
        "viewer_count": 1250,
        "like_count": 500,
        "started_at": "2023-01-01T10:00:00Z",
        "user": {
          "id": 1,
          "username": "streamer1",
          "nickname": "主播1",
          "avatar": "https://example.com/avatar.jpg"
        },
        "category": {
          "id": 1,
          "name": "游戏"
        }
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  }
}
```

### 获取直播间详情
```http
GET /rooms/{id}
```

**路径参数**:
- `id`: 直播间ID

### 更新直播间信息
```http
PUT /rooms/{id}
Authorization: Bearer <token>
```

**请求参数**: 同创建直播间，所有字段可选

### 开始直播
```http
POST /rooms/{id}/start
Authorization: Bearer <token>
```

### 结束直播
```http
POST /rooms/{id}/stop
Authorization: Bearer <token>
```

### 获取直播流状态
```http
GET /rooms/{id}/status
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "stream_name": "live_123456789",
    "status": "active",
    "start_time": "2023-01-01T10:00:00Z",
    "viewer_count": 1250,
    "bandwidth": 2048,
    "video_codec": "H264",
    "audio_codec": "AAC",
    "resolution": "1920x1080",
    "frame_rate": 30,
    "bitrate": 2000
  }
}
```

## 聊天消息接口

### 获取聊天消息
```http
GET /rooms/{id}/messages?page={page}&page_size={page_size}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "id": 1,
        "content": "欢迎来到直播间！",
        "type": 1,
        "created_at": "2023-01-01T10:00:00Z",
        "user": {
          "id": 1,
          "username": "user1",
          "nickname": "观众1",
          "avatar": "https://example.com/avatar.jpg"
        }
      }
    ],
    "total": 500,
    "page": 1,
    "page_size": 50,
    "total_pages": 10
  }
}
```

### 发送聊天消息
```http
POST /rooms/{id}/messages
Authorization: Bearer <token>
```

**请求参数**:
```json
{
  "content": "string",      // 消息内容
  "type": 1                 // 消息类型：1普通消息，2系统消息，3礼物消息
}
```

## 分类管理接口

### 获取分类列表
```http
GET /categories
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "游戏",
      "description": "游戏直播",
      "icon": "game",
      "room_count": 1250,
      "is_active": true
    },
    {
      "id": 2,
      "name": "娱乐",
      "description": "娱乐直播",
      "icon": "entertainment",
      "room_count": 890,
      "is_active": true
    }
  ]
}
```

## WebSocket接口

### 连接地址
```
ws://localhost:8080/ws/rooms/{room_id}?token={jwt_token}
```

### 消息格式

#### 客户端发送消息
```json
{
  "type": "chat",
  "data": {
    "content": "Hello World!"
  }
}
```

#### 服务端推送消息
```json
{
  "type": "chat",
  "data": {
    "id": 1,
    "content": "Hello World!",
    "user": {
      "id": 1,
      "username": "user1",
      "nickname": "用户1"
    },
    "created_at": "2023-01-01T10:00:00Z"
  }
}
```

#### 消息类型
- `chat`: 聊天消息
- `viewer_count`: 观众数量更新
- `stream_status`: 直播状态更新
- `gift`: 礼物消息
- `system`: 系统消息

## 错误码说明

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| USER_NOT_FOUND | 404 | 用户不存在 |
| INVALID_CREDENTIALS | 401 | 用户名或密码错误 |
| TOKEN_EXPIRED | 401 | 令牌已过期 |
| PERMISSION_DENIED | 403 | 权限不足 |
| ROOM_NOT_FOUND | 404 | 直播间不存在 |
| ROOM_ALREADY_LIVE | 400 | 直播间已在直播中 |
| VALIDATION_ERROR | 422 | 参数验证失败 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 限流说明

- 认证接口：每分钟最多10次请求
- 聊天接口：每分钟最多60次请求
- 其他接口：每分钟最多100次请求

## 版本说明

当前API版本：v1.0.0

版本更新时会在响应头中包含版本信息：
```
API-Version: v1.0.0
```
