# 腾讯云直播平台配置说明

## 概述

本文档详细说明了腾讯云直播平台的配置信息和集成方式。

## 腾讯云凭证配置

### API 凭证
- **SecretId**: `AKIDYw4vVpsfCSSBdojNkrjMiofFYbOu9CyU`
- **SecretKey**: `HH0TfaGhsnUv5yby4tRihQsjKfkY7QSE`
- **地域**: `ap-guangzhou` (广州)

### 直播域名配置
- **推流域名**: `215131.push.tlivecloud.com`
- **播放域名**: `215131.liveplay.myqcloud.com`

## 数据库配置

### MySQL 数据库
- **主机**: `gz-cdb-hao8jf8p.sql.tencentcdb.com`
- **端口**: `25118`
- **用户名**: `root`
- **密码**: `sd!Q53dsff`
- **数据库名**: `auth_system`

### Redis 缓存
- **主机**: `gz-crs-juno9w6l.sql.tencentcdb.com`
- **端口**: `27226`
- **用户名**: `cqf`
- **密码**: `cqf117857907`
- **数据库**: `0`

## 直播流地址生成

### 推流地址
格式：`rtmp://{推流域名}/live/{流名称}?{鉴权参数}`

示例：
```
rtmp://215131.push.tlivecloud.com/live/user_1_room_1_1703123456?txSecret=abc123&txTime=64a1b2c3
```

### 播放地址

#### RTMP 播放
格式：`rtmp://{播放域名}/live/{流名称}`

示例：
```
rtmp://215131.liveplay.myqcloud.com/live/user_1_room_1_1703123456
```

#### HLS 播放
格式：`http://{播放域名}/live/{流名称}.m3u8`

示例：
```
http://215131.liveplay.myqcloud.com/live/user_1_room_1_1703123456.m3u8
```

#### FLV 播放
格式：`http://{播放域名}/live/{流名称}.flv`

示例：
```
http://215131.liveplay.myqcloud.com/live/user_1_room_1_1703123456.flv
```

## 后端集成

### 配置文件位置
- 文件路径：`backend/config/.env`
- 示例文件：`backend/config/.env.example`

### 关键配置项
```env
# 腾讯云配置
TENCENT_SECRET_ID=AKIDYw4vVpsfCSSBdojNkrjMiofFYbOu9CyU
TENCENT_SECRET_KEY=HH0TfaGhsnUv5yby4tRihQsjKfkY7QSE
TENCENT_REGION=ap-guangzhou

# 直播域名配置
LIVE_PUSH_DOMAIN=215131.push.tlivecloud.com
LIVE_PLAY_DOMAIN=215131.liveplay.myqcloud.com
LIVE_PUSH_KEY=your-push-auth-key
LIVE_PLAY_KEY=your-play-auth-key
LIVE_EXPIRE_TIME=3600

# 数据库配置
DB_HOST=gz-cdb-hao8jf8p.sql.tencentcdb.com
DB_PORT=25118
DB_USER=root
DB_PASSWORD=sd!Q53dsff
DB_NAME=auth_system

# Redis配置
REDIS_HOST=gz-crs-juno9w6l.sql.tencentcdb.com
REDIS_PORT=27226
REDIS_USERNAME=cqf
REDIS_PASSWORD=cqf117857907
REDIS_DB=0
```

## 前端集成

### 配置文件位置
- 文件路径：`frontend/.env`
- 示例文件：`frontend/.env.example`

### 关键配置项
```env
# API配置
VITE_API_BASE_URL=http://localhost:8080/api
VITE_WS_BASE_URL=ws://localhost:8080/ws

# 腾讯云配置
VITE_TENCENT_LIVE_PUSH_DOMAIN=215131.push.tlivecloud.com
VITE_TENCENT_LIVE_PLAY_DOMAIN=215131.liveplay.myqcloud.com
```

## 核心功能

### 直播间管理
- 创建直播间
- 生成推流地址和播放地址
- 开始/结束直播
- 直播状态监控

### 流密钥生成
格式：`user_{用户ID}_room_{直播间ID}_{时间戳}`

示例：`user_1_room_1_1703123456`

### 鉴权机制
使用腾讯云标准鉴权算法：
```
txSecret = MD5(key + streamPath + expireTimeHex)
```

## API 端点

### 直播间相关
- `POST /api/rooms` - 创建直播间
- `GET /api/rooms/my` - 获取我的直播间
- `GET /api/rooms/{id}` - 获取直播间详情
- `POST /api/rooms/{id}/start` - 开始直播
- `POST /api/rooms/{id}/stop` - 结束直播
- `GET /api/rooms/{id}/status` - 获取流状态

### 分类相关
- `GET /api/categories` - 获取分类列表
- `GET /api/categories/{id}` - 获取分类详情

## 测试说明

### 后端测试
运行 `test_backend.bat` 启动后端服务

### 前端测试
运行 `test_frontend.bat` 启动前端开发服务器

### 完整测试
1. 启动后端服务（端口 8080）
2. 启动前端服务（端口 5173）
3. 访问 http://localhost:5173
4. 注册用户并设置为主播角色
5. 创建直播间并测试推流

## 注意事项

1. **安全性**：生产环境中请更换默认的密钥和密码
2. **域名配置**：确保腾讯云直播域名已正确配置和备案
3. **网络环境**：确保服务器能够访问腾讯云服务
4. **数据库迁移**：首次启动时会自动创建数据库表结构
5. **日志监控**：建议配置日志收集和监控系统

## 故障排除

### 常见问题
1. **推流失败**：检查域名配置和鉴权参数
2. **播放失败**：确认流是否正在推送
3. **数据库连接失败**：检查网络和凭证配置
4. **API 调用失败**：检查腾讯云 API 凭证

### 日志查看
- 后端日志：`backend/logs/app.log`
- 前端控制台：浏览器开发者工具
- 数据库日志：腾讯云控制台
