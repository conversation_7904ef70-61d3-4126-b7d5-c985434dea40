# 开发指南

## 开发环境搭建

### 前置要求

- Go 1.21+
- Node.js 18+
- Docker & Docker Compose
- Git
- MySQL 8.0+
- Redis 6.0+

### 本地开发环境

#### 1. 克隆项目
```bash
git clone <repository-url>
cd live-streaming-platform
```

#### 2. 后端开发环境
```bash
cd backend

# 安装依赖
go mod tidy

# 复制配置文件
cp config/.env.example config/.env

# 编辑配置文件，配置数据库连接
vim config/.env

# 启动数据库服务
docker-compose up -d mysql redis

# 运行数据库迁移
go run cmd/migrate/main.go

# 启动开发服务器
go run cmd/server/main.go
```

#### 3. 前端开发环境
```bash
cd frontend

# 安装依赖
npm install

# 复制配置文件
cp .env.example .env.local

# 编辑配置文件
vim .env.local

# 启动开发服务器
npm run dev
```

## 代码规范

### Go代码规范

#### 1. 命名规范
- 包名：小写，简短，有意义
- 变量名：驼峰命名法
- 常量名：大写，下划线分隔
- 函数名：驼峰命名法，导出函数首字母大写

```go
// 好的例子
package user

const (
    DefaultPageSize = 20
    MaxPageSize     = 100
)

type UserService interface {
    GetUserByID(ctx context.Context, id uint) (*User, error)
    CreateUser(ctx context.Context, user *User) error
}

// 不好的例子
package User

const defaultpagesize = 20

func getUserbyid(id uint) *User {
    // ...
}
```

#### 2. 错误处理
```go
// 好的例子
func (s *userService) GetUser(ctx context.Context, id uint) (*User, error) {
    user, err := s.userRepo.GetByID(ctx, id)
    if err != nil {
        return nil, fmt.Errorf("获取用户失败: %w", err)
    }
    
    if user == nil {
        return nil, fmt.Errorf("用户不存在")
    }
    
    return user, nil
}

// 不好的例子
func (s *userService) GetUser(ctx context.Context, id uint) *User {
    user, _ := s.userRepo.GetByID(ctx, id)
    return user
}
```

#### 3. 接口设计
```go
// 好的例子
type UserRepository interface {
    Create(ctx context.Context, user *User) error
    GetByID(ctx context.Context, id uint) (*User, error)
    Update(ctx context.Context, user *User) error
    Delete(ctx context.Context, id uint) error
}

// 不好的例子
type UserRepository interface {
    DoEverything(action string, data interface{}) interface{}
}
```

### TypeScript代码规范

#### 1. 类型定义
```typescript
// 好的例子
interface User {
  id: number
  username: string
  email: string
  createdAt: string
}

interface ApiResponse<T> {
  success: boolean
  data?: T
  message: string
}

// 不好的例子
interface User {
  id: any
  username: any
  email: any
}
```

#### 2. 组件设计
```vue
<!-- 好的例子 -->
<script setup lang="ts">
interface Props {
  user: User
  loading?: boolean
}

interface Emits {
  (e: 'update', user: User): void
  (e: 'delete', id: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()
</script>

<!-- 不好的例子 -->
<script setup lang="ts">
const props = defineProps(['user', 'loading'])
const emit = defineEmits(['update', 'delete'])
</script>
```

## 架构设计

### 后端架构

#### 1. 分层架构
```
Controller Layer (控制器层)
    ↓
Service Layer (业务逻辑层)
    ↓
Repository Layer (数据访问层)
    ↓
Model Layer (数据模型层)
```

#### 2. 依赖注入
```go
// 定义接口
type UserService interface {
    GetUser(ctx context.Context, id uint) (*User, error)
}

type UserRepository interface {
    GetByID(ctx context.Context, id uint) (*User, error)
}

// 实现服务
type userService struct {
    userRepo UserRepository
}

func NewUserService(userRepo UserRepository) UserService {
    return &userService{
        userRepo: userRepo,
    }
}

// 在main.go中组装
func main() {
    db := setupDatabase()
    userRepo := repository.NewUserRepository(db)
    userService := service.NewUserService(userRepo)
    userController := controller.NewUserController(userService)
    
    // 注册路由
    router.POST("/users", userController.CreateUser)
}
```

### 前端架构

#### 1. 组件层次
```
App.vue
├── Layout Components
│   ├── Header.vue
│   ├── Sidebar.vue
│   └── Footer.vue
├── Page Components
│   ├── HomeView.vue
│   ├── LoginView.vue
│   └── ProfileView.vue
└── Common Components
    ├── Button.vue
    ├── Modal.vue
    └── Loading.vue
```

#### 2. 状态管理
```typescript
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const loading = ref(false)
  
  // 计算属性
  const isLoggedIn = computed(() => !!user.value)
  
  // 方法
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response = await authApi.login(credentials)
      user.value = response.data.user
    } finally {
      loading.value = false
    }
  }
  
  return { user, loading, isLoggedIn, login }
})
```

## 测试指南

### 后端测试

#### 1. 单元测试
```go
func TestUserService_GetUser(t *testing.T) {
    tests := []struct {
        name    string
        userID  uint
        want    *User
        wantErr bool
    }{
        {
            name:   "成功获取用户",
            userID: 1,
            want:   &User{ID: 1, Username: "test"},
            wantErr: false,
        },
        {
            name:    "用户不存在",
            userID:  999,
            want:    nil,
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 创建mock repository
            mockRepo := &MockUserRepository{}
            service := NewUserService(mockRepo)
            
            // 设置mock期望
            if tt.wantErr {
                mockRepo.On("GetByID", mock.Anything, tt.userID).Return(nil, errors.New("not found"))
            } else {
                mockRepo.On("GetByID", mock.Anything, tt.userID).Return(tt.want, nil)
            }
            
            // 执行测试
            got, err := service.GetUser(context.Background(), tt.userID)
            
            // 验证结果
            if (err != nil) != tt.wantErr {
                t.Errorf("GetUser() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("GetUser() = %v, want %v", got, tt.want)
            }
        })
    }
}
```

#### 2. 集成测试
```go
func TestUserAPI_Integration(t *testing.T) {
    // 设置测试数据库
    db := setupTestDB()
    defer cleanupTestDB(db)
    
    // 创建测试服务器
    router := setupTestRouter(db)
    server := httptest.NewServer(router)
    defer server.Close()
    
    // 测试创建用户
    user := &User{Username: "test", Email: "<EMAIL>"}
    body, _ := json.Marshal(user)
    
    resp, err := http.Post(server.URL+"/api/users", "application/json", bytes.NewBuffer(body))
    assert.NoError(t, err)
    assert.Equal(t, http.StatusCreated, resp.StatusCode)
}
```

### 前端测试

#### 1. 组件测试
```typescript
// UserCard.test.ts
import { mount } from '@vue/test-utils'
import UserCard from '@/components/UserCard.vue'

describe('UserCard', () => {
  it('显示用户信息', () => {
    const user = {
      id: 1,
      username: 'test',
      email: '<EMAIL>'
    }
    
    const wrapper = mount(UserCard, {
      props: { user }
    })
    
    expect(wrapper.text()).toContain('test')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
  
  it('点击编辑按钮触发事件', async () => {
    const user = { id: 1, username: 'test', email: '<EMAIL>' }
    const wrapper = mount(UserCard, {
      props: { user }
    })
    
    await wrapper.find('[data-test="edit-button"]').trigger('click')
    
    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')[0]).toEqual([user])
  })
})
```

## API设计规范

### RESTful API设计

#### 1. URL设计
```
GET    /api/users          # 获取用户列表
GET    /api/users/:id      # 获取单个用户
POST   /api/users          # 创建用户
PUT    /api/users/:id      # 更新用户
DELETE /api/users/:id      # 删除用户

GET    /api/users/:id/rooms    # 获取用户的直播间
POST   /api/users/:id/follow   # 关注用户
```

#### 2. 响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "test",
    "email": "<EMAIL>"
  },
  "timestamp": "2023-01-01T00:00:00Z"
}
```

#### 3. 错误响应
```json
{
  "success": false,
  "error": "用户不存在",
  "message": "ID为123的用户不存在",
  "code": "USER_NOT_FOUND",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

## 数据库设计

### 1. 表设计原则
- 使用有意义的表名和字段名
- 每个表都有主键
- 外键约束保证数据完整性
- 适当的索引提高查询性能
- 使用软删除而不是物理删除

### 2. 迁移管理
```sql
-- 001_create_users_table.sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_deleted_at (deleted_at)
);
```

## 部署和运维

### 1. 环境管理
- 开发环境：本地开发
- 测试环境：自动化测试
- 预生产环境：性能测试
- 生产环境：正式服务

### 2. CI/CD流程
```yaml
# .github/workflows/ci.yml
name: CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Go
      uses: actions/setup-go@v2
      with:
        go-version: 1.21
    
    - name: Run tests
      run: |
        cd backend
        go test ./...
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: 18
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm install
        npm run test
```

### 3. 监控和日志
- 使用结构化日志
- 实现健康检查接口
- 监控关键指标
- 设置告警规则

## 性能优化

### 1. 后端优化
- 数据库查询优化
- 缓存策略
- 连接池配置
- 异步处理

### 2. 前端优化
- 代码分割
- 懒加载
- 图片优化
- 缓存策略

### 3. 系统优化
- 负载均衡
- CDN加速
- 数据库分库分表
- 微服务拆分

## 安全考虑

### 1. 认证和授权
- JWT令牌管理
- 权限控制
- 会话管理
- 密码安全

### 2. 数据安全
- 输入验证
- SQL注入防护
- XSS防护
- CSRF防护

### 3. 网络安全
- HTTPS配置
- 防火墙设置
- DDoS防护
- 访问控制
